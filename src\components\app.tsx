import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import PAGE_URL from 'constants/PAGE_URL';
import { withLoading } from 'hocs/withLoading';
import MainLayout from 'layouts/MainLayout/MainLayout';
import React, { lazy } from 'react';
import { Route, Routes } from 'react-router-dom';
import { RecoilRoot } from 'recoil';
import { AnimationRoutes, App, SnackbarProvider, ZMPRouter } from 'zmp-ui';
import { ScrollRestoration } from './ScrollRestoration';

const HomePage = withLoading(lazy(() => import('pages/home/<USER>')));
const SchedulePage = withLoading(lazy(() => import('pages/schedule/Schedule')));
const NewsDetailPage = withLoading(lazy(() => import('pages/news/NewsDetail')));
const JobDetailPage = withLoading(lazy(() => import('pages/jobs/JobDetail')));
const JobRegistrationPage = withLoading(lazy(() => import('pages/jobs/JobRegistration')));
const MyJobRegistrationsPage = withLoading(lazy(() => import('pages/jobs/MyJobRegistrations')));

const queryClient = new QueryClient({
    defaultOptions: {
        queries: {
            refetchOnWindowFocus: false,
        },
    },
});
const MyApp = () => {
    return (
        <QueryClientProvider client={queryClient}>
            <RecoilRoot>
                <App>
                    <SnackbarProvider>
                        <ZMPRouter>
                            <ScrollRestoration />
                            <Routes>
                                <Route element={<MainLayout />}>
                                    <Route path={PAGE_URL.HOME} element={<HomePage />}></Route>
                                    <Route path={`${PAGE_URL.SCHEDULE}/*`} element={<SchedulePage />}></Route>
                                    <Route
                                        path={PAGE_URL.MY_JOB_REGISTRATIONS}
                                        element={<MyJobRegistrationsPage />}
                                    ></Route>
                                </Route>
                                <Route path="/news/:id" element={<NewsDetailPage />}></Route>
                                <Route path="/jobs/:id" element={<JobDetailPage />}></Route>
                                <Route
                                    path={PAGE_URL.JOB_REGISTRATION}
                                    element={<JobRegistrationPage />}
                                ></Route>
                            </Routes>
                        </ZMPRouter>
                    </SnackbarProvider>
                </App>
            </RecoilRoot>
        </QueryClientProvider>
    );
};
export default MyApp;
