import { atom, selector } from 'recoil';
import { PhieuKetQua } from 'apis/getPhieuKetQuaApi';

// Atom to store all appointments
export const appointmentsState = atom<PhieuKetQua[]>({
    key: 'appointmentsState',
    default: [],
});

// Selector to get appointments sorted by date (newest first)
export const sortedAppointmentsState = selector({
    key: 'sortedAppointmentsState',
    get: ({ get }) => {
        const appointments = get(appointmentsState);
        return [...appointments].sort((a, b) => {
            // So sánh theo ngày tạo
            const dateA = new Date(a.ngayTao);
            const dateB = new Date(b.ngayTao);
            return dateB.getTime() - dateA.getTime(); // Sắp xếp giảm dần (mới nhất trước)
        });
    },
});
