import React from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useNavigate } from 'react-router-dom';
import { AppointmentFormData, AppointmentType } from 'types/appointment.type';
import { useAppointments } from 'hooks/useAppointments';
import PAGE_URL from 'constants/PAGE_URL';
import './AppointmentForm.scss';

const validationSchema = yup.object({
    title: yup.string().required('Vui lòng nhập tiêu đề lịch hẹn'),
    description: yup.string().required('Vui lòng nhập mô tả lịch hẹn'),
    date: yup.string().required('Vui lòng chọn ngày hẹn'),
    time: yup.string().required('Vui lòng chọn giờ hẹn'),
    type: yup.string().required('<PERSON>ui lòng chọn loại lịch hẹn'),
    location: yup.string().required('<PERSON>ui lòng nhập địa điểm'),
}).required();

interface AppointmentFormProps {
    initialData?: Partial<AppointmentFormData>;
    isEditing?: boolean;
    appointmentId?: string;
}

const AppointmentForm: React.FC<AppointmentFormProps> = ({ 
    initialData, 
    isEditing = false,
    appointmentId
}) => {
    const navigate = useNavigate();
    const { createAppointment, updateAppointment } = useAppointments();
    
    const { 
        register, 
        handleSubmit, 
        formState: { errors, isSubmitting } 
    } = useForm<AppointmentFormData>({
        resolver: yupResolver(validationSchema),
        defaultValues: initialData || {
            title: '',
            description: '',
            date: new Date().toISOString().split('T')[0],
            time: '09:00',
            type: AppointmentType.UNEMPLOYMENT_BENEFIT,
            location: 'Trung tâm Dịch vụ việc làm Khánh Hòa - 87 Hoàng Hoa Thám, Nha Trang',
            notes: ''
        }
    });
    
    const onSubmit = async (data: AppointmentFormData) => {
        if (isEditing && appointmentId) {
            const result = await updateAppointment(appointmentId, data);
            if (result) {
                navigate(PAGE_URL.SCHEDULE);
            }
        } else {
            const result = await createAppointment(data);
            if (result) {
                navigate(PAGE_URL.SCHEDULE);
            }
        }
    };
    
    return (
        <div className="appointment-form">
            <form onSubmit={handleSubmit(onSubmit)}>
                <div className="form-group">
                    <label htmlFor="title">Tiêu đề lịch hẹn <span className="required">*</span></label>
                    <input 
                        type="text" 
                        id="title" 
                        className={`form-control ${errors.title ? 'is-invalid' : ''}`}
                        placeholder="Nhập tiêu đề lịch hẹn"
                        {...register('title')}
                    />
                    {errors.title && <div className="invalid-feedback">{errors.title.message}</div>}
                </div>
                
                <div className="form-group">
                    <label htmlFor="description">Mô tả <span className="required">*</span></label>
                    <textarea 
                        id="description" 
                        className={`form-control ${errors.description ? 'is-invalid' : ''}`}
                        placeholder="Nhập mô tả chi tiết về lịch hẹn"
                        rows={3}
                        {...register('description')}
                    ></textarea>
                    {errors.description && <div className="invalid-feedback">{errors.description.message}</div>}
                </div>
                
                <div className="form-row">
                    <div className="form-group form-group--half">
                        <label htmlFor="date">Ngày hẹn <span className="required">*</span></label>
                        <input 
                            type="date" 
                            id="date" 
                            className={`form-control ${errors.date ? 'is-invalid' : ''}`}
                            {...register('date')}
                        />
                        {errors.date && <div className="invalid-feedback">{errors.date.message}</div>}
                    </div>
                    
                    <div className="form-group form-group--half">
                        <label htmlFor="time">Giờ hẹn <span className="required">*</span></label>
                        <input 
                            type="time" 
                            id="time" 
                            className={`form-control ${errors.time ? 'is-invalid' : ''}`}
                            {...register('time')}
                        />
                        {errors.time && <div className="invalid-feedback">{errors.time.message}</div>}
                    </div>
                </div>
                
                <div className="form-group">
                    <label htmlFor="type">Loại lịch hẹn <span className="required">*</span></label>
                    <select 
                        id="type" 
                        className={`form-control ${errors.type ? 'is-invalid' : ''}`}
                        {...register('type')}
                    >
                        <option value={AppointmentType.UNEMPLOYMENT_BENEFIT}>Trợ cấp thất nghiệp</option>
                        <option value={AppointmentType.CONSULTATION}>Tư vấn</option>
                        <option value={AppointmentType.DOCUMENT_SUBMISSION}>Nộp hồ sơ</option>
                        <option value={AppointmentType.OTHER}>Khác</option>
                    </select>
                    {errors.type && <div className="invalid-feedback">{errors.type.message}</div>}
                </div>
                
                <div className="form-group">
                    <label htmlFor="location">Địa điểm <span className="required">*</span></label>
                    <input 
                        type="text" 
                        id="location" 
                        className={`form-control ${errors.location ? 'is-invalid' : ''}`}
                        placeholder="Nhập địa điểm lịch hẹn"
                        {...register('location')}
                    />
                    {errors.location && <div className="invalid-feedback">{errors.location.message}</div>}
                </div>
                
                <div className="form-group">
                    <label htmlFor="notes">Ghi chú</label>
                    <textarea 
                        id="notes" 
                        className="form-control"
                        placeholder="Nhập ghi chú (nếu có)"
                        rows={2}
                        {...register('notes')}
                    ></textarea>
                </div>
                
                <div className="form-actions">
                    <button 
                        type="button" 
                        className="btn btn-secondary"
                        onClick={() => navigate(PAGE_URL.SCHEDULE)}
                    >
                        Hủy
                    </button>
                    <button 
                        type="submit" 
                        className="btn btn-primary"
                        disabled={isSubmitting}
                    >
                        {isEditing ? 'Cập nhật' : 'Đặt lịch'}
                    </button>
                </div>
            </form>
        </div>
    );
};

export default AppointmentForm;
