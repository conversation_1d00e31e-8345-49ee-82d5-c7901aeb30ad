import { useQuery } from '@tanstack/react-query';
import { memberApi } from 'apis/getMemberApi';
import { PageContainer } from 'components/page-container';
import React, { useState } from 'react';
import Skeleton from 'react-loading-skeleton';
import { useRecoilValue } from 'recoil';
import { userNumberState } from 'state';
import { Header } from 'zmp-ui';
import './MemberCard.scss';

function MemberCard() {
    const [selectedCardId, setSelectedCardId] = useState<string | null>(null);
    const userPhoneNumber = useRecoilValue(userNumberState);
    const { data: memberData, isLoading: memberLoading } = useQuery({
        queryKey: ['member', userPhoneNumber],
        queryFn: () => memberApi.getMemberByPhoneNumber(userPhoneNumber),
        enabled: !!userPhoneNumber,
    });

    const { data: memberCardData, isLoading: memberCardLoading } = useQuery({
        queryKey: ['member-card', memberData?.maKhach],
        queryFn: () => memberApi.getMemberPrepaidCard(memberData?.maKhach),
        enabled: !!memberData?.maKhach,
    });

    const { data: cardHistory, isLoading: historyLoading } = useQuery({
        queryKey: ['card-history', selectedCardId],
        queryFn: () => memberApi.getCardHistory(memberData?.maKhach),
        enabled: !!selectedCardId,
    });

    const handleCardClick = (cardId: string) => {
        setSelectedCardId(cardId === selectedCardId ? null : cardId);
    };

    const getTransactionLabel = (type: string) => {
        switch (type) {
            case 'BILL':
                return 'Thanh toán hóa đơn';
            case 'TOPUP':
                return 'Nạp tiền';
            case 'REFUND':
                return 'Hoàn tiền';
            default:
                return 'Giao dịch khác';
        }
    };

    if (memberLoading || memberCardLoading) {
        return (
            <div>
                <Skeleton width={'100%'} height={100} />
                <Skeleton width={'100%'} height={100} />
            </div>
        );
    }

    if (!memberCardData?.length) {
        return (
            <div className="empty-state">
                <div className="empty-state__icon">💳</div>
                <p>Bạn chưa có thẻ trả trước nào</p>
            </div>
        );
    }

    return (
        <PageContainer withHeader={true} className="bg-white">
            <Header title={'Thẻ của bạn'} showBackIcon={false} className="no-divider have-shadow" />
            {/* <div className="member-card__header">
                <h2 className="title"></h2>
                <p className="subtitle">Quản lý tất cả thẻ trả trước tại đây</p>
            </div> */}
            <div className="member-card">
                <div className="member-card__list">
                    {memberLoading || memberCardLoading ? (
                        <div>
                            <Skeleton width={'100%'} height={200} />
                        </div>
                    ) : (
                        memberCardData?.map((card) => (
                            <div
                                key={card.id}
                                className={`card-item ${selectedCardId === card.id ? 'expanded' : ''}`}
                                onClick={() => handleCardClick(card.id)}
                            >
                                <div className="card-item__decoration">
                                    <div className="circle circle-1"></div>
                                    <div className="circle circle-2"></div>
                                </div>

                                <div className="card-item__content">
                                    <div className="card-header">
                                        <div className="card-type">
                                            <span className="icon">💳</span>
                                            <span className="name">{card.cardName}</span>
                                        </div>
                                        <div className={`status ${card.isActive ? 'active' : 'inactive'}`}>
                                            {card.isActive ? 'Active' : 'Inactive'}
                                        </div>
                                    </div>

                                    <div className="card-balance">
                                        <div className="available">
                                            <span className="label">Số dư khả dụng</span>
                                            <span className="amount">
                                                {new Intl.NumberFormat('vi-VN', {
                                                    style: 'currency',
                                                    currency: 'VND',
                                                }).format(card.availableAmount)}
                                            </span>
                                        </div>

                                        <div className="progress-bar">
                                            <div
                                                className="progress"
                                                style={{
                                                    width: `${(card.usedAmount / card.totalAmount) * 100}%`,
                                                }}
                                            ></div>
                                        </div>

                                        <div className="balance-details">
                                            <div className="detail-item">
                                                <span className="label">Tổng số tiền</span>
                                                <span className="value">
                                                    {new Intl.NumberFormat('vi-VN', {
                                                        style: 'currency',
                                                        currency: 'VND',
                                                    }).format(card.totalAmount)}
                                                </span>
                                            </div>
                                            <div className="detail-item used">
                                                <span className="label">Đã sử dụng</span>
                                                <span className="value">
                                                    {new Intl.NumberFormat('vi-VN', {
                                                        style: 'currency',
                                                        currency: 'VND',
                                                    }).format(card.usedAmount)}
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="card-info">
                                        <div className="info-grid">
                                            <div className="info-item">
                                                <span className="label">Mã thẻ</span>
                                                <span className="value">{card.code}</span>
                                            </div>
                                            <div className="info-item">
                                                <span className="label">Mã khách hàng</span>
                                                <span className="value">{card.idKhach}</span>
                                            </div>
                                            <div className="info-item">
                                                <span className="label">Ngày tạo</span>
                                                <span className="value">
                                                    {new Date(card.createdTime).toLocaleDateString('vi-VN')}
                                                </span>
                                            </div>
                                            <div className="info-item">
                                                <span className="label">Cập nhật cuối</span>
                                                <span className="value">
                                                    {new Date(card.lastUpdatedTime).toLocaleDateString(
                                                        'vi-VN'
                                                    )}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {selectedCardId === card.id && (
                                    <div className="card-history">
                                        <h3 className="history-title">
                                            <span>Lịch sử giao dịch</span>
                                            {/* <button
                                        className="close-btn"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            setSelectedCardId(null);
                                        }}
                                    >
                                        ×
                                    </button> */}
                                        </h3>

                                        {historyLoading ? (
                                            <div className="history-loading">
                                                <div className="loading-spinner"></div>
                                                <span>Đang tải lịch sử giao dịch...</span>
                                            </div>
                                        ) : cardHistory?.length === 0 ? (
                                            <div className="history-empty">
                                                <span className="icon">📝</span>
                                                <span className="message">Chưa có giao dịch nào</span>
                                            </div>
                                        ) : (
                                            <div className="history-list">
                                                {cardHistory?.map((transaction) => (
                                                    <div key={transaction.id} className="history-item">
                                                        <div className="transaction-info">
                                                            <div className="main-info">
                                                                <span className="type">
                                                                    {getTransactionLabel(
                                                                        transaction.transactionType
                                                                    )}
                                                                </span>
                                                                <span className="reference">
                                                                    Mã GD: {transaction.referenceCode}
                                                                </span>
                                                            </div>
                                                            <div className="sub-info">
                                                                <span className="time">
                                                                    {new Date(
                                                                        transaction.transationTime
                                                                    ).toLocaleDateString('vi-VN', {
                                                                        hour: '2-digit',
                                                                        minute: '2-digit',
                                                                        day: '2-digit',
                                                                        month: '2-digit',
                                                                        year: 'numeric',
                                                                    })}
                                                                </span>
                                                                <span className="branch">
                                                                    Chi nhánh: {transaction.branch}
                                                                </span>
                                                            </div>
                                                        </div>
                                                        <div className="transaction-amount">
                                                            <span
                                                                className={`amount ${
                                                                    transaction.amount < 0
                                                                        ? 'debit'
                                                                        : 'credit'
                                                                }`}
                                                            >
                                                                {'-'}
                                                                {new Intl.NumberFormat('vi-VN', {
                                                                    style: 'currency',
                                                                    currency: 'VND',
                                                                }).format(Math.abs(transaction.amount))}
                                                            </span>
                                                            {transaction.discount > 0 && (
                                                                <span className="discount">
                                                                    Giảm giá:{' '}
                                                                    {new Intl.NumberFormat('vi-VN', {
                                                                        style: 'currency',
                                                                        currency: 'VND',
                                                                    }).format(transaction.discount)}
                                                                </span>
                                                            )}
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        ))
                    )}
                    {}
                </div>
            </div>
        </PageContainer>
    );
}

export default MemberCard;
