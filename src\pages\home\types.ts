import { UseFormRegister, FieldErrors } from 'react-hook-form';
import { IGuestFromCrm } from 'types/guest.type';

export interface IFormInputs {
    hoTen: string;
    email?: string;
    dienThoai: string;
    diaChi?: string;
    maDinhDanh?: string;
}

export interface RegisterFormProps {
    onSubmit: () => void;
    register: UseFormRegister<IFormInputs>;
    errors: FieldErrors<IFormInputs>;
    onMaDinhDanhChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export interface GuestFormProps {
    register: UseFormRegister<IGuestFromCrm>;
}

export interface HeroSlide {
    id: number;
    background: string;
    title: string;
    name: string;
    message: string;
}

export interface ImageItem {
    image: string;
    name: string;
}

export interface ImageListItem {
    id: number;
    data: ImageItem[];
}
