.job-list-container {
    margin-top: 20px;
}

.job-tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    background-color: #fff;
}

.job-tab {
    flex: 1;
    padding: 12px 8px;
    background: #f9fafb;
    border: none;
    font-size: 14px;
    font-weight: 500;
    color: #4b5563;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    border-bottom: 2px solid transparent;

    &:first-child {
        border-right: 1px solid #e5e7eb;
    }

    &.active {
        background: #fff;
        color: #1e40af;
        border-bottom: 2px solid #3b82f6;
        font-weight: 600;
    }

    &:hover:not(.active) {
        background: #f3f4f6;
    }

    .tab-icon {
        font-size: 14px;
    }

    @media (max-width: 360px) {
        font-size: 13px;
        padding: 10px 6px;

        .tab-icon {
            font-size: 12px;
        }
    }
}

.job-list-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #333;
    text-align: center;
    position: relative;
    padding-bottom: 10px;

    &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 3px;
        background: linear-gradient(90deg, #3b82f6, #2563eb);
        border-radius: 3px;
    }
}

.job-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.empty-jobs {
    text-align: center;
    padding: 30px 20px;
    background-color: #f9fafb;
    border-radius: 12px;
    border: 1px dashed #d1d5db;
    margin: 20px 0;

    p {
        color: #6b7280;
        font-size: 15px;
    }
}

.job-card {
    background-color: #fff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;

    &:hover {
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
        transform: translateY(-2px);
    }

    &:active {
        transform: scale(0.99);
    }

    &.expanded {
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    &.highlight-job {
        animation: highlight-pulse 1s ease;
    }
}

.job-card-header {
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    border-bottom: 1px solid #f0f0f0;
}

.job-title-container {
    flex: 1;
}

.job-title {
    font-size: 16px;
    font-weight: 600;
    color: #1e40af;
    margin: 0 0 8px 0;
    line-height: 1.3;

    @media (max-width: 768px) {
        font-size: 15px;
    }
}

.job-company {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #4b5563;
    margin: 0;
}

.job-badge {
    display: flex;
    align-items: center;
    background-color: #e0f2fe;
    color: #0369a1;
    padding: 4px 8px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    margin-left: 8px;
}

.job-quantity {
    margin-right: 4px;
}

.job-icon {
    margin-right: 6px;
    font-size: 14px;
    color: #6b7280;
}

.job-icon-small {
    font-size: 12px;
}

.job-card-body {
    padding: 16px;
}

.job-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.job-salary,
.job-type {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #4b5563;
    margin: 0;
}

.job-salary {
    color: #047857;
    font-weight: 500;
}

.job-details {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px dashed #e5e7eb;
}

.job-description,
.job-requirements,
.job-additional-info,
.job-contact {
    margin-bottom: 16px;

    h4 {
        font-size: 15px;
        font-weight: 600;
        color: #374151;
        margin: 0 0 8px 0;
    }

    p {
        font-size: 14px;
        color: #4b5563;
        margin: 0;
        line-height: 1.5;
    }

    ul {
        margin: 0;
        padding-left: 20px;

        li {
            font-size: 14px;
            color: #4b5563;
            margin-bottom: 4px;
            line-height: 1.5;

            strong {
                color: #374151;
            }
        }
    }
}

.contact-button {
    display: inline-flex;
    align-items: center;
    background-color: #10b981;
    color: white;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    margin-top: 8px;

    svg {
        margin-right: 8px;
    }

    &:active {
        background-color: #059669;
    }
}

.job-card-footer {
    padding: 14px 16px;
    background-color: #f9fafb;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom-left-radius: 16px;
    border-bottom-right-radius: 16px;
}

.job-deadline {
    display: flex;
    align-items: center;
    font-size: 13px;
    color: #6b7280;
}

.job-card-actions {
    display: flex;
    gap: 12px;
    align-items: center;
    margin-top: 4px;
}

.view-more-btn,
.view-less-btn {
    background: none;
    border: none;
    color: #6b7280;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    padding: 6px 10px;
    border-radius: 20px;
    transition: all 0.2s ease;
    min-width: 90px;

    &:hover {
        background-color: #f3f4f6;
        color: #4b5563;
    }
}

.register-btn,
.registered-btn {
    border: none;
    border-radius: 6px;
    padding: 7px 12px;
    font-size: 13px;
    font-weight: 400;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    transition: all 0.2s ease;
    min-width: 100px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    min-width: 120px;

    .job-icon {
        font-size: 12px;
    }
}

.register-btn {
    background-color: #f0f9ff;
    color: #0284c7;
    border: 1px solid #bae6fd;

    &:hover {
        background-color: #e0f2fe;
    }

    &:active {
        background-color: #bae6fd;
    }
}

.registered-btn {
    background-color: #f0fdf4;
    color: #16a34a;
    border: 1px solid #bbf7d0;

    &:disabled {
        opacity: 1;
    }

    &:hover {
        background-color: #dcfce7;
    }
}

// Skeleton loading
.job-card-skeleton {
    height: 120px;
    background-color: #fff;
    border-radius: 12px;
    margin-bottom: 16px;
    overflow: hidden;
    position: relative;
}

.skeleton-pulse {
    height: 100%;
    width: 100%;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        background-position: 0% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

@keyframes highlight-pulse {
    0% {
        box-shadow: 0 0 0 rgba(33, 57, 130, 0.2);
    }
    50% {
        box-shadow: 0 0 20px rgba(33, 57, 130, 0.6);
    }
    100% {
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
}

// Error state
.job-list-error {
    text-align: center;
    padding: 32px 16px;

    h2 {
        font-size: 18px;
        color: #ef4444;
        margin-bottom: 8px;
    }

    p {
        font-size: 14px;
        color: #6b7280;
    }
}

// Công việc gợi ý
.recommended-job-card {
    // border-left: 4px solid #4caf50;
    // position: relative;
    & .job-badge {
        display: none;
    }
    .job-match-score {
        position: absolute;
        top: 10px;
        right: 10px;
        background-color: #e8f5e9;
        color: #2e7d32;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        display: flex;
        align-items: center;
        font-weight: 500;

        .match-icon {
            margin-right: 4px;
            font-size: 10px;
        }

        .match-text {
            white-space: nowrap;
        }

        .match-reason {
            display: none;
            position: absolute;
            top: 100%;
            right: 0;
            background-color: white;
            border: 1px solid #e0e0e0;
            padding: 8px;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            width: 200px;
            z-index: 10;
            font-size: 12px;
            margin-top: 5px;
        }

        &:hover .match-reason {
            display: block;
        }
    }
}
