import React from 'react';
import './FloatingProduct.scss';

interface FloatingProductProps {
    image: string;
    delay?: number;
}

const FloatingProduct: React.FC<FloatingProductProps> = ({ image, delay = 0 }) => {
    return (
        <div className="floating-product" style={{ animationDelay: `${delay}s` }}>
            <img src={image} alt="product" />
        </div>
    );
};

export default FloatingProduct;
