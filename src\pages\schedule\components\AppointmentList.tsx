import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useRecoilValue } from 'recoil';
import { sortedAppointmentsState } from 'state/appointmentState';
import { Appointment, AppointmentStatus } from 'types/appointment.type';
import { FaCalendarCheck, FaCalendarTimes, FaCalendarDay, FaCalendarAlt } from 'react-icons/fa';
import PAGE_URL from 'constants/PAGE_URL';
import './AppointmentList.scss';

const AppointmentList: React.FC = () => {
    const appointments = useRecoilValue(sortedAppointmentsState);
    const navigate = useNavigate();
    
    const getStatusIcon = (status: AppointmentStatus) => {
        switch (status) {
            case AppointmentStatus.CONFIRMED:
                return <FaCalendarCheck className="appointment-item__icon appointment-item__icon--confirmed" />;
            case AppointmentStatus.CANCELLED:
                return <FaCalendarTimes className="appointment-item__icon appointment-item__icon--cancelled" />;
            case AppointmentStatus.COMPLETED:
                return <FaCalendarCheck className="appointment-item__icon appointment-item__icon--completed" />;
            default:
                return <FaCalendarDay className="appointment-item__icon appointment-item__icon--pending" />;
        }
    };
    
    const getStatusClass = (status: AppointmentStatus) => {
        switch (status) {
            case AppointmentStatus.CONFIRMED:
                return 'appointment-item--confirmed';
            case AppointmentStatus.CANCELLED:
                return 'appointment-item--cancelled';
            case AppointmentStatus.COMPLETED:
                return 'appointment-item--completed';
            default:
                return 'appointment-item--pending';
        }
    };
    
    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('vi-VN', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };
    
    const handleAppointmentClick = (appointment: Appointment) => {
        navigate(`${PAGE_URL.SCHEDULE}/${appointment.id}`);
    };
    
    if (appointments.length === 0) {
        return (
            <div className="appointment-list-empty">
                <FaCalendarAlt className="appointment-list-empty__icon" />
                <p className="appointment-list-empty__text">Bạn chưa có lịch hẹn nào</p>
                <button 
                    className="appointment-list-empty__button"
                    onClick={() => navigate(`${PAGE_URL.SCHEDULE}/new`)}
                >
                    Đặt lịch hẹn ngay
                </button>
            </div>
        );
    }
    
    return (
        <div className="appointment-list">
            {appointments.map((appointment) => (
                <div 
                    key={appointment.id}
                    className={`appointment-item ${getStatusClass(appointment.status)}`}
                    onClick={() => handleAppointmentClick(appointment)}
                >
                    <div className="appointment-item__icon-container">
                        {getStatusIcon(appointment.status)}
                    </div>
                    <div className="appointment-item__content">
                        <h3 className="appointment-item__title">{appointment.title}</h3>
                        <p className="appointment-item__date">
                            {formatDate(appointment.date)} - {appointment.time}
                        </p>
                        <p className="appointment-item__location">{appointment.location}</p>
                    </div>
                </div>
            ))}
        </div>
    );
};

export default AppointmentList;
