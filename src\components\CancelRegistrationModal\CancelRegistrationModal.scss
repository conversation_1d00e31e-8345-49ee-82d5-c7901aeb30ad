.cancel-registration-modal {
    .zaui-modal-content {
        border-radius: 12px !important;
        overflow: hidden;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
    }

    .zaui-modal-header {
        display: none !important;
    }

    .modal-content {
        padding: 24px 20px 20px;
    }

    .modal-icon {
        display: flex;
        justify-content: center;
        margin-bottom: 16px;

        svg {
            font-size: 36px;
            color: #f87171;
            opacity: 0.9;
        }
    }

    .modal-description {
        margin-bottom: 20px;
        font-size: 14px;
        color: #4b5563;
        line-height: 1.5;
        text-align: center;
    }

    .reason-input {
        margin-bottom: 24px;

        input {
            width: 100%;
            border-radius: 6px;
            padding: 10px 14px;
            font-size: 14px;
            border: 1px solid #e5e7eb;
            background-color: #f9fafb;
            transition: all 0.2s ease;

            &:focus {
                background-color: white;
                border-color: #bae6fd;
                outline: none;
                box-shadow: 0 0 0 3px rgba(186, 230, 253, 0.25);
            }

            &::placeholder {
                color: #9ca3af;
            }
        }
    }

    .modal-actions {
        display: flex;
        justify-content: space-between;
        gap: 10px;
    }

    .cancel-button {
        background-color: white;
        color: #6b7280;
        border: 1px solid #e5e7eb;
        border-radius: 6px;
        padding: 9px 16px;
        font-weight: 400;
        font-size: 14px;
        flex: 1;
        transition: all 0.2s ease;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

        &:hover {
            background-color: #f9fafb;
            border-color: #d1d5db;
        }

        &:active {
            background-color: #f3f4f6;
        }
    }

    .submit-button {
        background-color: #fef2f2;
        color: #b91c1c;
        border: 1px solid #fecaca;
        border-radius: 6px;
        padding: 9px 16px;
        font-weight: 400;
        font-size: 14px;
        flex: 1;
        transition: all 0.2s ease;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

        &:hover {
            background-color: #fee2e2;
        }

        &:active {
            background-color: #fecaca;
        }
    }
}
