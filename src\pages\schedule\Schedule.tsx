import React, { useState } from 'react';
import { Header } from 'zmp-ui';
import { useRecoilValue } from 'recoil';
import { useAppointments } from 'hooks/useAppointments';
import './Schedule.scss';
import { sortedAppointmentsState } from 'state/appointmentState';
import { FaMapMarkerAlt, FaClock } from 'react-icons/fa';
import { MdOutlineEventAvailable } from 'react-icons/md';
import AppointmentDetailModal from 'components/AppointmentDetailModal';

import { PageContainer } from 'components/page-container';
import { formatCurrency } from 'utils/common';

function Schedule() {
    useAppointments();
    const appointments = useRecoilValue(sortedAppointmentsState);

    // State để lưu trữ tab đang được chọn: sắp đến hoặc đã hết hạn
    const [activeTab, setActiveTab] = useState<'upcoming' | 'expired'>('upcoming');

    // State để quản lý modal chi tiết lịch hẹn
    const [selectedAppointment, setSelectedAppointment] = useState<any>(null);
    const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);

    // Hàm mở modal chi tiết lịch hẹn
    const openAppointmentDetail = (appointment: any) => {
        setSelectedAppointment(appointment);
        setIsDetailModalVisible(true);
    };

    // Hàm đóng modal chi tiết lịch hẹn
    const closeAppointmentDetail = () => {
        setIsDetailModalVisible(false);
    };

    // Lọc lịch hẹn theo tab đang chọn
    const filteredAppointments = React.useMemo(() => {
        const today = new Date();

        if (activeTab === 'upcoming') {
            // Lịch hẹn sắp đến: ngày hiện tại nằm trong khoảng thời gian hưởng trợ cấp hoặc chưa đến
            return appointments.filter((appointment) => {
                try {
                    // Xử lý ngày tháng từ thoiGianBatDau và thoiGianKetThuc (định dạng dd-MM-yyyy)
                    if (appointment.thoiGianBatDau && appointment.thoiGianKetThuc) {
                        const startParts = appointment.thoiGianBatDau.split('-');
                        const endParts = appointment.thoiGianKetThuc.split('-');

                        if (startParts.length === 3 && endParts.length === 3) {
                            // Chuyển từ dd-MM-yyyy sang Date object
                            const startDate = new Date(`${startParts[2]}-${startParts[1]}-${startParts[0]}`);
                            const endDate = new Date(`${endParts[2]}-${endParts[1]}-${endParts[0]}`);

                            // Lịch hẹn sắp đến: ngày hiện tại nằm trong khoảng thời gian hoặc chưa đến
                            return (today >= startDate && today <= endDate) || today < startDate;
                        }
                    }
                    return false; // Nếu không có ngày hoặc không thể parse, không hiển thị
                } catch (error) {
                    console.error('Lỗi khi xử lý ngày tháng:', error);
                    return false;
                }
            });
        } else {
            // Lịch hẹn đã hết hạn: ngày hiện tại sau ngày kết thúc
            return appointments.filter((appointment) => {
                try {
                    // Xử lý ngày tháng từ thoiGianKetThuc (định dạng dd-MM-yyyy)
                    if (appointment.thoiGianKetThuc) {
                        const parts = appointment.thoiGianKetThuc.split('-');
                        if (parts.length === 3) {
                            // Chuyển từ dd-MM-yyyy sang Date object
                            const endDate = new Date(`${parts[2]}-${parts[1]}-${parts[0]}`);
                            return today > endDate; // Đã hết hạn nếu ngày hiện tại sau ngày kết thúc
                        }
                    }
                    return false;
                } catch (error) {
                    console.error('Lỗi khi xử lý ngày tháng:', error);
                    return false;
                }
            });
        }
    }, [appointments, activeTab]);

    return (
        <PageContainer className="schedule-container" withHeader resetScroll={true}>
            <Header title="Lịch hẹn của tôi" showBackIcon={false} className="schedule-header" />

            <div className="schedule-page">
                <div className="schedule-tabs">
                    <button
                        className={`schedule-tab ${activeTab === 'upcoming' ? 'active' : ''}`}
                        onClick={() => setActiveTab('upcoming')}
                    >
                        Đang hưởng hoặc sắp đến
                    </button>
                    <button
                        className={`schedule-tab ${activeTab === 'expired' ? 'active' : ''}`}
                        onClick={() => setActiveTab('expired')}
                    >
                        Đã hết hạn
                    </button>
                </div>

                <div className="appointment-list">
                    {filteredAppointments.length === 0 ? (
                        <div className="empty-state">
                            <MdOutlineEventAvailable className="empty-state__icon" />
                            <p className="empty-state__text">
                                {activeTab === 'upcoming'
                                    ? 'Không có lịch hẹn nào đang hưởng hoặc sắp đến'
                                    : 'Không có lịch hẹn nào đã hết hạn'}
                            </p>
                        </div>
                    ) : (
                        filteredAppointments.map((appointment) => (
                            <div
                                key={appointment.id}
                                className="appointment-item"
                                onClick={() => openAppointmentDetail(appointment)}
                            >
                                <div className="appointment-item__header">
                                    <div className="appointment-item__date-badge">
                                        <div className="appointment-item__day">
                                            {appointment.thoiGianBatDau
                                                ? appointment.thoiGianBatDau.split('-')[0]
                                                : '--'}
                                        </div>
                                        <div className="appointment-item__month">
                                            {appointment.thoiGianBatDau
                                                ? `Th.${appointment.thoiGianBatDau.split('-')[1]}`
                                                : 'Th.--'}
                                        </div>
                                    </div>
                                    <div className="appointment-item__header-content">
                                        <h3 className="appointment-item__title">Trợ cấp thất nghiệp</h3>
                                        <div className="appointment-item__time">
                                            <FaClock className="appointment-item__time-icon" />
                                            <span className="appointment-item__time-text">
                                                {appointment.hoTen}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div className="appointment-item__body">
                                    <div className="appointment-item__notes-container">
                                        <p className="appointment-item__notes">
                                            Mức trợ cấp:{' '}
                                            {appointment.mucTroCapHangThang
                                                ? formatCurrency(appointment.mucTroCapHangThang)
                                                : 'Chưa xác định'}
                                        </p>
                                    </div>

                                    <p className="appointment-item__description">
                                        Từ {appointment.thoiGianBatDau} đến {appointment.thoiGianKetThuc}
                                    </p>

                                    <div className="appointment-item__location">
                                        <FaMapMarkerAlt className="appointment-item__location-icon" />
                                        <span>Trung tâm DVVL Khánh Hòa</span>
                                    </div>
                                </div>
                            </div>
                        ))
                    )}
                </div>
            </div>

            {/* Modal chi tiết lịch hẹn */}
            {selectedAppointment && (
                <AppointmentDetailModal
                    appointment={selectedAppointment}
                    visible={isDetailModalVisible}
                    onClose={closeAppointmentDetail}
                />
            )}
        </PageContainer>
    );
}

export default Schedule;
