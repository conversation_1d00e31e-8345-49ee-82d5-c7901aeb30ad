.news-detail-page {
    background-color: #f8f9fa;
    min-height: 100vh;

    @media (max-width: 480px) {
        background-color: #fff;
    }

    .news-detail-container {
        padding: 20px;
        background-color: #fff;
        border-radius: 12px;
        margin: 16px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        position: relative;
        overflow: hidden;

        @media (max-width: 480px) {
            padding: 16px;
            margin: 0;
            border-radius: 0;
            box-shadow: none;
        }

        &:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, #213982, #4c6bc5);

            @media (max-width: 480px) {
                height: 3px;
            }
        }
    }

    .news-detail-title {
        font-size: 22px;
        font-weight: 700;
        color: #213982;
        margin-bottom: 16px;
        line-height: 1.4;
        position: relative;
        padding-bottom: 10px;

        @media (max-width: 480px) {
            font-size: 20px;
            margin-bottom: 14px;
            padding-bottom: 8px;
        }

        &:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background-color: #213982;
            border-radius: 3px;

            @media (max-width: 480px) {
                width: 50px;
                height: 2px;
            }
        }
    }

    .news-detail-meta {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        margin-bottom: 20px;
        color: #666;
        font-size: 14px;
        padding: 10px 15px;
        border-bottom: 1px solid #eee;
        background-color: #f9f9f9;
        border-radius: 6px;

        @media (max-width: 480px) {
            margin-bottom: 16px;
            padding: 8px 12px;
            font-size: 13px;
            border-radius: 4px;
        }
    }

    .news-detail-date {
        display: flex;
        align-items: center;
        gap: 6px;

        @media (max-width: 480px) {
            gap: 4px;
        }

        svg {
            color: #213982;
            font-size: 16px;

            @media (max-width: 480px) {
                font-size: 14px;
            }
        }
    }

    .news-detail-source {
        font-style: italic;
        font-size: 12px;
        background-color: rgba(33, 57, 130, 0.1);
        padding: 4px 8px;
        border-radius: 4px;
        color: #213982;

        @media (max-width: 480px) {
            font-size: 11px;
            padding: 3px 6px;
        }
    }

    .news-detail-image {
        margin-bottom: 25px;
        border-radius: 10px;
        overflow: hidden;
        background-color: #f0f0f0;
        position: relative;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

        @media (max-width: 480px) {
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
        }

        &:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 40px;
            background: linear-gradient(to top, rgba(0, 0, 0, 0.2), transparent);
            pointer-events: none;

            @media (max-width: 480px) {
                height: 30px;
            }
        }

        img {
            width: 100%;
            height: auto;
            object-fit: cover;
            transition: transform 0.5s ease;
            display: block;
        }

        &:hover img {
            transform: scale(1.03);
        }

        .image-placeholder {
            width: 100%;
            height: 250px;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;

            @media (max-width: 480px) {
                height: 200px;
            }
        }
    }

    // Gallery styles
    .news-detail-gallery {
        margin-bottom: 25px;

        @media (max-width: 480px) {
            margin-bottom: 20px;
        }

        .gallery-container {
            display: flex;
            flex-direction: column;
            gap: 15px;

            @media (max-width: 480px) {
                gap: 12px;
            }
        }

        .gallery-item {
            position: relative;
            border-radius: 10px;
            overflow: hidden;
            background-color: #f0f0f0;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

            @media (max-width: 480px) {
                border-radius: 8px;
                box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
            }

            .gallery-image {
                width: 100%;
                height: auto;
                display: block;
                transition: transform 0.5s ease;
            }

            &:hover .gallery-image {
                transform: scale(1.02);
            }

            .image-number {
                position: absolute;
                bottom: 10px;
                right: 10px;
                background-color: rgba(0, 0, 0, 0.7);
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 500;
                z-index: 2;

                @media (max-width: 480px) {
                    bottom: 8px;
                    right: 8px;
                    padding: 3px 6px;
                    font-size: 11px;
                }
            }

            .image-placeholder {
                width: 100%;
                height: 300px;
                background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                background-size: 200% 100%;
                animation: loading 1.5s infinite;

                @media (max-width: 480px) {
                    height: 200px;
                }
            }
        }
    }

    .news-detail-content-wrapper {
        background-color: #fff;
        border-radius: 8px;
        padding: 0;
        margin-bottom: 20px;

        @media (max-width: 480px) {
            margin-bottom: 16px;
        }
    }

    @keyframes loading {
        0% {
            background-position: 200% 0;
        }
        100% {
            background-position: -200% 0;
        }
    }

    .news-detail-content {
        font-size: 16px;
        line-height: 1.7;
        color: #333;
        padding: 5px 0;

        @media (max-width: 480px) {
            font-size: 15px;
            line-height: 1.6;
        }

        p {
            margin-bottom: 18px;
            text-align: justify;

            @media (max-width: 480px) {
                margin-bottom: 16px;
            }

            &.no-content {
                text-align: center;
                padding: 30px 20px;
                color: #666;
                font-style: italic;
                background-color: #f9f9f9;
                border-radius: 8px;
                margin: 20px 0;

                @media (max-width: 480px) {
                    padding: 20px 15px;
                    margin: 15px 0;
                }
            }
        }

        img {
            max-width: 100%;
            height: auto;
            margin: 20px auto;
            border-radius: 8px;
            display: block;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);

            @media (max-width: 480px) {
                margin: 16px auto;
                border-radius: 6px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            }
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            margin-top: 28px;
            margin-bottom: 18px;
            color: #213982;
            font-weight: 600;
            position: relative;
            padding-bottom: 8px;

            @media (max-width: 480px) {
                margin-top: 24px;
                margin-bottom: 16px;
                padding-bottom: 6px;
                font-size: 0.95em;
            }

            &:after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 0;
                width: 40px;
                height: 3px;
                background-color: #213982;
                border-radius: 3px;

                @media (max-width: 480px) {
                    width: 30px;
                    height: 2px;
                }
            }
        }

        ul,
        ol {
            margin-bottom: 18px;
            padding-left: 24px;

            @media (max-width: 480px) {
                margin-bottom: 16px;
                padding-left: 20px;
            }

            li {
                margin-bottom: 10px;
                position: relative;

                @media (max-width: 480px) {
                    margin-bottom: 8px;
                }
            }
        }

        a {
            color: #213982;
            text-decoration: none;
            position: relative;
            padding-bottom: 2px;

            &:after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 1px;
                background-color: #213982;
                transform: scaleX(0);
                transition: transform 0.3s ease;
                transform-origin: right;
            }

            &:hover:after {
                transform: scaleX(1);
                transform-origin: left;
            }
        }

        blockquote {
            border-left: 4px solid #213982;
            padding: 15px 20px;
            margin: 20px 0;
            background-color: #f9f9f9;
            border-radius: 0 8px 8px 0;
            font-style: italic;
            color: #555;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

            th,
            td {
                border: 1px solid #ddd;
                padding: 12px 15px;
                text-align: left;
            }

            th {
                background-color: #213982;
                color: white;
                font-weight: 600;
            }

            tr:nth-child(even) {
                background-color: #f9f9f9;
            }

            tr:hover {
                background-color: #f0f0f0;
            }
        }
    }

    // Actions Section

    .news-detail-actions {
        margin-top: 30px;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 15px;
        padding-top: 20px;
        border-top: 1px solid #eee;

        @media (max-width: 480px) {
            margin-top: 24px;
            padding-top: 16px;
            gap: 12px;
            flex-direction: column;
        }
    }

    .back-button {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        background-color: #213982;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 6px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 5px rgba(33, 57, 130, 0.2);

        @media (max-width: 480px) {
            width: 100%;
            padding: 14px 20px;
            font-size: 15px;
        }

        &:hover {
            background-color: #162a61;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(33, 57, 130, 0.3);
        }

        &:active {
            transform: translateY(0);
            box-shadow: 0 2px 3px rgba(33, 57, 130, 0.2);
        }
    }

    .source-button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        background-color: #f8f9fa;
        color: #213982;
        border: 1px solid #213982;
        padding: 12px 24px;
        border-radius: 6px;
        font-weight: 500;
        text-decoration: none;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);

        @media (max-width: 480px) {
            width: 100%;
            padding: 14px 20px;
            font-size: 15px;
        }

        &:hover {
            background-color: #213982;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        &:active {
            transform: translateY(0);
            box-shadow: 0 2px 3px rgba(0, 0, 0, 0.05);
        }
    }

    // Loading and Error States

    .news-detail-loading,
    .news-detail-error {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 20px;
        text-align: center;
        min-height: 300px;

        p {
            margin-top: 20px;
            color: #666;
            font-size: 16px;
        }
    }

    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(33, 57, 130, 0.2);
        border-radius: 50%;
        border-top-color: #213982;
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to {
            transform: rotate(360deg);
        }
    }
}
