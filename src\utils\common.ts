export const formatPhoneNumber = {
    // <PERSON><PERSON><PERSON>n từ 0792289045 -> 84792289045
    toInternational: (phone: string) => {
        if (phone.startsWith('0')) {
            return '84' + phone.slice(1);
        }
        return phone;
    },

    // <PERSON><PERSON>ển từ 84792289045 -> 0792289045
    toNational: (phone: string) => {
        if (phone.startsWith('84')) {
            return '0' + phone.slice(2);
        }
        return phone;
    },
};

export const replaceAllUrls = (content: string, baseUrl: string) => {
    if (!content) return '';

    return content
        .replace(/src=["']\/(?!\/)/g, `src="${baseUrl}/`)
        .replace(/href=["']\/(?!\/)/g, `href="${baseUrl}/`)
        .replace(/url\(["']?\/(?!\/)/g, `url("${baseUrl}/`)
        .replace(/src=["'](?!http|https|\/\/|data:|blob:)/g, `src="${baseUrl}/`)
        .replace(/href=["'](?!http|https|\/\/|mailto:|tel:|#)/g, `href="${baseUrl}/`);
};

export const formatCurrency = (amount: number | string): string => {
    if (amount === null || amount === undefined) return '0đ';

    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

    if (isNaN(numAmount)) return '0đ';

    return numAmount.toLocaleString('vi-VN') + 'đ';
};
