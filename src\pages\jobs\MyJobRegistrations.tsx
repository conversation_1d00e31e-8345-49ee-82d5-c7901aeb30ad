import React, { useEffect, useState } from 'react';
import { Box, Button, Input, Page, Text, useSnackbar } from 'zmp-ui';
import { useJobRegistrations } from 'hooks/useJobRegistrations';
import { useRecoilValue } from 'recoil';
import { isMemberState, userInfoState } from 'state';
import { FaBuilding, FaCalendarAlt, FaTimesCircle } from 'react-icons/fa';
import './MyJobRegistrations.scss';
import { useJobList } from 'hooks/useJobList';
import CancelRegistrationModal from 'components/CancelRegistrationModal/CancelRegistrationModal';
import { PageContainer } from 'components/page-container';

const MyJobRegistrations = () => {
    const { registrations, isLoading, error, refetch } = useJobRegistrations();
    const { data: jobsData } = useJobList();
    const { openSnackbar } = useSnackbar();
    const userInfo = useRecoilValue(userInfoState);
    const isMember = useRecoilValue(isMemberState);
    const [hasMaDinhDanh, setHasMaDinhDanh] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedRegistration, setSelectedRegistration] = useState<{
        id: number;
        maDinhDanh: string;
    } | null>(null);

    useEffect(() => {
        if (isMember && userInfo) {
            const userMaDinhDanh = userInfo.maDinhDanh || userInfo.cccd || '';
            if (userMaDinhDanh) {
                setHasMaDinhDanh(true);
            }
        }
    }, [isMember, userInfo]);

    const formatDate = (dateString: string) => {
        if (!dateString) return '';

        try {
            let date: Date | undefined;
            if (dateString.includes('T')) {
                date = new Date(dateString);
            } else if (dateString.includes('-')) {
                const parts = dateString.split('-');
                if (parts.length === 3) {
                    date = new Date(parseInt(parts[0]), parseInt(parts[1]) - 1, parseInt(parts[2]));
                }
            } else if (dateString.includes('/')) {
                const parts = dateString.split('/');
                if (parts.length === 3) {
                    date = new Date(parseInt(parts[2]), parseInt(parts[1]) - 1, parseInt(parts[0]));
                }
            }

            if (date && !isNaN(date.getTime())) {
                const day = date.getDate().toString().padStart(2, '0');
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const year = date.getFullYear();
                const hours = date.getHours().toString().padStart(2, '0');
                const minutes = date.getMinutes().toString().padStart(2, '0');

                return `${day}/${month}/${year} ${hours}:${minutes}`;
            }
        } catch (error) {
            console.error('Lỗi định dạng ngày tháng:', error);
        }

        return dateString;
    };

    const handleCancelRegistration = (registration: { id: number; maDinhDanh: string }) => {
        setSelectedRegistration(registration);
        setModalVisible(true);
    };

    const handleModalClose = () => {
        setModalVisible(false);
        setSelectedRegistration(null);
    };

    const renderJobStatus = (status: number) => {
        let statusClass = '';
        let statusText = '';

        switch (status) {
            case 1:
                statusClass = 'status-registered';
                statusText = 'Đã đăng ký';
                break;
            case 2:
                statusClass = 'status-canceled';
                statusText = 'Đã hủy đăng ký';
                break;
            case 3:
                statusClass = 'status-approved';
                statusText = 'Đã được duyệt';
                break;
            default:
                statusClass = 'status-default';
                statusText = 'Không xác định';
                break;
        }

        return <span className={`job-status ${statusClass}`}>{statusText}</span>;
    };

    const getJobInfo = (maCongViec: number) => {
        if (!jobsData || !jobsData.data) return { tenCongViec: 'Không xác định', congTy: 'Không xác định' };

        const job = jobsData.data.find((job) => job.id === maCongViec);
        return {
            tenCongViec: job?.tenCongViec || 'Không xác định',
            congTy: job?.congTy || 'Không xác định',
        };
    };

    return (
        <PageContainer className="my-job-registrations-page">
            <div className="page-header">
                <h1>Việc làm đã đăng ký</h1>
            </div>

            <div className="job-registrations-list">
                {!hasMaDinhDanh ? (
                    <div className="empty-container">
                        <p>Bạn cần đăng nhập với tài khoản có mã định danh để xem việc làm đã đăng ký</p>
                    </div>
                ) : isLoading ? (
                    <div className="loading-container">
                        <div className="loading-spinner"></div>
                        <p>Đang tải dữ liệu...</p>
                    </div>
                ) : error ? (
                    <div className="error-container">
                        <p>{error}</p>
                    </div>
                ) : registrations.length === 0 ? (
                    <div className="empty-container">
                        <p>Không tìm thấy việc làm đã đăng ký</p>
                    </div>
                ) : (
                    registrations.map((registration) => {
                        const jobInfo = getJobInfo(registration.maCongViec);
                        return (
                            <div key={registration.id} className="job-registration-card">
                                <div className="job-registration-header">
                                    <h3 className="job-title">{jobInfo.tenCongViec}</h3>
                                    {renderJobStatus(registration.trangThai)}
                                </div>
                                <div className="job-registration-body">
                                    <div className="job-info-item">
                                        <FaBuilding className="job-icon" />
                                        <span>{jobInfo.congTy}</span>
                                    </div>
                                    <div className="job-info-item">
                                        <FaCalendarAlt className="job-icon" />
                                        <span>Ngày đăng ký: {formatDate(registration.ngayDangKy)}</span>
                                    </div>
                                    {registration.ghiChu && (
                                        <div className="job-info-item job-note">
                                            <strong>Ghi chú:</strong> {registration.ghiChu}
                                        </div>
                                    )}
                                </div>
                                {registration.trangThai === 1 && (
                                    <div className="job-registration-actions">
                                        <button
                                            className="cancel-registration-btn"
                                            onClick={() =>
                                                handleCancelRegistration({
                                                    id: registration.id,
                                                    maDinhDanh: registration.maDinhDanh,
                                                })
                                            }
                                        >
                                            <FaTimesCircle className="btn-icon" /> Hủy đăng ký
                                        </button>
                                    </div>
                                )}
                            </div>
                        );
                    })
                )}
            </div>

            {selectedRegistration && (
                <CancelRegistrationModal
                    visible={modalVisible}
                    onClose={handleModalClose}
                    registrationId={selectedRegistration.id}
                    maDinhDanh={selectedRegistration.maDinhDanh}
                    onSuccess={() => {
                        setTimeout(() => {
                            refetch();
                            openSnackbar({
                                text: 'Đã cập nhật danh sách đăng ký',
                                type: 'success',
                                duration: 2000,
                            });
                        }, 500);
                    }}
                />
            )}
        </PageContainer>
    );
};

export default MyJobRegistrations;
