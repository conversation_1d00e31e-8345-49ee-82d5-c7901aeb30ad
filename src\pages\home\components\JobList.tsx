import React, { useState, useMemo, useEffect } from 'react';
import { useJobList, JobItem } from 'hooks/useJobList';
import {
    FaBuilding,
    FaMoneyBillWave,
    FaCalendarAlt,
    FaUsers,
    FaPhoneAlt,
    FaUserPlus,
    FaCheck,
    FaBriefcase,
    FaListAlt,
    FaSearch,
    FaThumbsUp,
} from 'react-icons/fa';
import './JobList.scss';
import { useNavigate } from 'react-router-dom';
import PAGE_URL from 'constants/PAGE_URL';
import { useRegisteredJobs } from 'hooks/useRegisteredJobs';
import { useRecoilValue } from 'recoil';
import { isMemberState, userNumberState } from 'state';
import { tuVanViecLamApi, TuVanViecLamItem } from 'apis/tuVanViecLamApi';
import { useQuery } from '@tanstack/react-query';
import { findMatchingJobs, JobWithScore } from 'utils/jobMatching';

function JobList() {
    const { data, isLoading, error } = useJobList();
    const navigate = useNavigate();
    const [expandedJobId, setExpandedJobId] = useState<number | null>(null);
    const { isJobRegistered, registeredJobIds } = useRegisteredJobs();
    const [activeTab, setActiveTab] = useState<'myJobs' | 'allJobs' | 'recommendedJobs'>('allJobs');
    const isMember = useRecoilValue(isMemberState);
    const userNumber = useRecoilValue(userNumberState);

    const { data: tuVanData, isLoading: tuVanLoading } = useQuery(
        ['tuVanViecLam', userNumber],
        () => tuVanViecLamApi.getBySoDienThoai(userNumber),
        {
            enabled: !!userNumber,
            staleTime: 5 * 60 * 1000,
            cacheTime: 30 * 60 * 1000,
            refetchOnWindowFocus: false,
            onSuccess: (data) => {
                if (data?.data?.some((item) => item.viTriCanTimCongViec) && isMember) {
                    setActiveTab('recommendedJobs');
                }
            },
            onError: (error) => {
                console.error('Lỗi khi lấy dữ liệu tư vấn việc làm:', error);
            },
        }
    );

    const myJobs = useMemo(() => {
        if (!data?.data || !registeredJobIds.length) return [];
        return data.data.filter((job) => registeredJobIds.includes(job.id));
    }, [data?.data, registeredJobIds]);

    const recommendedJobs = useMemo(() => {
        if (!data?.data || !tuVanData?.data?.length) return [];

        const viTriCanTim = tuVanData.data
            .filter((item) => item.viTriCanTimCongViec && item.viTriCanTimCongViec.trim() !== '')
            .map((item) => item.viTriCanTimCongViec as string);

        if (!viTriCanTim.length) return [];

        let allMatches: (JobItem & JobWithScore)[] = [];

        viTriCanTim.forEach((viTri) => {
            const matches = findMatchingJobs<JobItem>(viTri, data.data, 40);
            allMatches = [...allMatches, ...matches];
        });

        const uniqueMatches = allMatches.reduce((acc, current) => {
            const x = acc.find((item) => item.id === current.id);
            if (!x) {
                return acc.concat([current]);
            } else if (current.matchScore > x.matchScore) {
                return acc.map((item) => (item.id === current.id ? current : item));
            } else {
                return acc;
            }
        }, [] as (JobItem & JobWithScore)[]);

        return uniqueMatches.sort((a, b) => b.matchScore - a.matchScore);
    }, [data?.data, tuVanData?.data]);

    const handleJobClick = (jobId: number) => {
        const isExpanding = expandedJobId !== jobId;
        setExpandedJobId(isExpanding ? jobId : null);

        if (isExpanding) {
            setTimeout(() => {
                const jobElement = document.getElementById(`job-${jobId}`);
                if (jobElement) {
                    jobElement.classList.add('highlight-job');
                    const jobHeaderElement = jobElement.querySelector('.job-card-header');

                    if (jobHeaderElement) {
                        const headerRect = jobHeaderElement.getBoundingClientRect();
                        const offset = 80;
                        const scrollTop = window.scrollY || document.documentElement.scrollTop;
                        const targetY = scrollTop + headerRect.top - offset;

                        window.scrollTo({
                            top: targetY,
                            behavior: 'smooth',
                        });
                    } else {
                        jobElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start',
                        });
                    }

                    setTimeout(() => {
                        jobElement.classList.remove('highlight-job');
                    }, 1000);
                }
            }, 100);

            setTimeout(() => {
                const jobElement = document.getElementById(`job-${jobId}`);
                if (jobElement) {
                    const jobDetailsElement = jobElement.querySelector('.job-details');
                    if (jobDetailsElement) {
                        const detailsRect = jobDetailsElement.getBoundingClientRect();

                        if (detailsRect.bottom > window.innerHeight || detailsRect.top < 0) {
                            const scrollTop = window.scrollY || document.documentElement.scrollTop;
                            const targetY = scrollTop + detailsRect.top - 120;

                            window.scrollTo({
                                top: targetY,
                                behavior: 'smooth',
                            });
                        }
                    }
                }
            }, 500);
        }
    };

    const handleRegisterClick = (e: React.MouseEvent, jobId: number) => {
        e.stopPropagation();
        navigate(PAGE_URL.JOB_REGISTRATION.replace(':id', jobId.toString()));
    };

    const formatDate = (dateString: string) => {
        if (!dateString) return '';
        const parts = dateString.split('/');
        if (parts.length !== 3) return dateString;
        return `${parts[0]}/${parts[1]}/${parts[2]}`;
    };

    const renderJobCard = (job: JobItem) => (
        <div
            id={`job-${job.id}`}
            key={job.id}
            className={`job-card ${expandedJobId === job.id ? 'expanded' : ''}`}
            onClick={() => handleJobClick(job.id)}
        >
            <div className="job-card-header">
                <div className="job-title-container">
                    <h3 className="job-title">{job.tenCongViec}</h3>
                    <p className="job-company">
                        <FaBuilding className="job-icon" />
                        {job.congTy}
                    </p>
                </div>
                <div className="job-badge">
                    <span className="job-quantity">{job.soLuong}</span>
                    <FaUsers className="job-icon-small" />
                </div>
            </div>

            <div className="job-card-body">
                <div className="job-info">
                    <p className="job-salary">
                        <FaMoneyBillWave className="job-icon" />
                        {job.mucLuong || 'Thỏa thuận'}
                    </p>
                    <p className="job-type">
                        <FaCalendarAlt className="job-icon" />
                        {job.hinhThucLamViec || 'Toàn thời gian'}
                    </p>
                </div>

                {expandedJobId === job.id && (
                    <div className="job-details">
                        {job.moTa && (
                            <div className="job-description">
                                <h4>Mô tả công việc</h4>
                                <p>{job.moTa}</p>
                            </div>
                        )}

                        <div className="job-requirements">
                            <h4>Yêu cầu</h4>
                            <ul>
                                {job.trinhDoHocVan && (
                                    <li>
                                        <strong>Trình độ học vấn:</strong> {job.trinhDoHocVan}
                                    </li>
                                )}
                                {job.trinhDoCMKT && (
                                    <li>
                                        <strong>Trình độ chuyên môn:</strong> {job.trinhDoCMKT}
                                    </li>
                                )}
                                {job.kinhNghiem && (
                                    <li>
                                        <strong>Kinh nghiệm:</strong> {job.kinhNghiem}
                                    </li>
                                )}
                                {job.ngoaiNgu && (
                                    <li>
                                        <strong>Ngoại ngữ:</strong> {job.ngoaiNgu}
                                    </li>
                                )}
                                {job.tinHoc && (
                                    <li>
                                        <strong>Tin học:</strong> {job.tinHoc}
                                    </li>
                                )}
                            </ul>
                        </div>

                        <div className="job-additional-info">
                            <h4>Thông tin thêm</h4>
                            <ul>
                                {job.loaiHopDong && (
                                    <li>
                                        <strong>Loại hợp đồng:</strong> {job.loaiHopDong}
                                    </li>
                                )}
                                {job.mucDich && (
                                    <li>
                                        <strong>Mục đích:</strong> {job.mucDich}
                                    </li>
                                )}
                                {job.hinhThucTuyenDung && (
                                    <li>
                                        <strong>Hình thức tuyển dụng:</strong> {job.hinhThucTuyenDung}
                                    </li>
                                )}
                                <li>
                                    <strong>Thời hạn:</strong> {formatDate(job.thoiHanTu)} -{' '}
                                    {formatDate(job.thoiHanDen)}
                                </li>
                            </ul>
                        </div>

                        <div className="job-contact">
                            <h4>Liên hệ</h4>
                            {job.soDienThoai && (
                                <a href={`tel:${job.soDienThoai}`} className="contact-button">
                                    <FaPhoneAlt /> Gọi ngay: {job.soDienThoai}
                                </a>
                            )}
                        </div>
                    </div>
                )}
            </div>

            <div className="job-card-footer">
                <div className="job-deadline">
                    <FaCalendarAlt className="job-icon" />
                    Hạn nộp: {formatDate(job.thoiHanDen)}
                </div>
                {expandedJobId === job.id ? (
                    <div className="job-card-actions">
                        <button className="view-less-btn">Thu gọn</button>
                        {isJobRegistered(job.id) ? (
                            <button className="registered-btn" disabled>
                                <FaCheck className="job-icon" /> Đã đăng ký
                            </button>
                        ) : (
                            <button className="register-btn" onClick={(e) => handleRegisterClick(e, job.id)}>
                                <FaUserPlus className="job-icon" /> Đăng ký
                            </button>
                        )}
                    </div>
                ) : (
                    <div className="job-card-actions">
                        <button className="view-more-btn">Xem thêm</button>
                        {isJobRegistered(job.id) ? (
                            <button className="registered-btn" disabled>
                                <FaCheck className="job-icon" /> Đã đăng ký
                            </button>
                        ) : (
                            <button className="register-btn" onClick={(e) => handleRegisterClick(e, job.id)}>
                                <FaUserPlus className="job-icon" /> Đăng ký
                            </button>
                        )}
                    </div>
                )}
            </div>
        </div>
    );

    if (isLoading) {
        return (
            <div className="job-list-container">
                <h2 className="job-list-title">Việc làm mới nhất</h2>
                {[...Array(5)].map((_, index) => (
                    <div key={index} className="job-card-skeleton">
                        <div className="skeleton-pulse"></div>
                    </div>
                ))}
            </div>
        );
    }

    if (error) {
        return (
            <div className="job-list-error">
                <h2>Không thể tải danh sách việc làm</h2>
                <p>Vui lòng thử lại sau</p>
            </div>
        );
    }

    const renderRecommendedJobCard = (job: JobItem & JobWithScore) => {
        const baseJobCard = renderJobCard(job);

        return React.cloneElement(baseJobCard, {
            className: `${baseJobCard.props.className} recommended-job-card`,
            children: [
                ...React.Children.toArray(baseJobCard.props.children),
                <div key="match-score" className="job-match-score">
                    <FaThumbsUp className="match-icon" />
                    <span className="match-text">Phù hợp {job.matchScore}%</span>
                    <div className="match-reason">{job.matchReason}</div>
                </div>,
            ],
        });
    };

    return (
        <div className="job-list-container">
            <div className="job-tabs">
                {tuVanData?.data?.some((item) => item.viTriCanTimCongViec) && isMember && (
                    <button
                        className={`job-tab ${activeTab === 'recommendedJobs' ? 'active' : ''}`}
                        onClick={() => setActiveTab('recommendedJobs')}
                    >
                        <FaSearch className="tab-icon" />
                        Công việc gợi ý
                    </button>
                )}
                <button
                    className={`job-tab ${activeTab === 'allJobs' ? 'active' : ''}`}
                    onClick={() => setActiveTab('allJobs')}
                >
                    <FaListAlt className="tab-icon" />
                    Toàn bộ công việc
                </button>
            </div>

            {activeTab === 'recommendedJobs' && (
                <>
                    {recommendedJobs.length === 0 ? (
                        <div className="empty-jobs">
                            <p>Không tìm thấy công việc phù hợp với yêu cầu của bạn</p>
                        </div>
                    ) : (
                        <div className="job-list">
                            {recommendedJobs.map((job) => renderRecommendedJobCard(job))}
                        </div>
                    )}
                </>
            )}

            {activeTab === 'myJobs' && (
                <>
                    {!isMember ? (
                        <div className="empty-jobs">
                            <p>Vui lòng đăng nhập để xem công việc của bạn</p>
                        </div>
                    ) : myJobs.length === 0 ? (
                        <div className="empty-jobs">
                            <p>Bạn chưa đăng ký công việc nào</p>
                        </div>
                    ) : (
                        <div className="job-list">{myJobs.map((job) => renderJobCard(job))}</div>
                    )}
                </>
            )}

            {activeTab === 'allJobs' && (
                <>
                    <div className="job-list">{data?.data.map((job) => renderJobCard(job))}</div>
                </>
            )}
        </div>
    );
}

export default JobList;
