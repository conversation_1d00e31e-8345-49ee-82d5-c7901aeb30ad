.message {
    min-height: 100vh;
    background: linear-gradient(135deg, #f6f9fc 0%, #ffffff 100%);

    &__header {
        background: linear-gradient(135deg, #1a73e8 0%, #0d47a1 100%);
        padding: 1.2rem;
        display: flex;
        align-items: center;
        gap: 1.2rem;
        color: white;
        box-shadow: 0 2px 12px rgba(26, 115, 232, 0.2);

        &-back {
            padding: 8px;
            border-radius: 50%;
            transition: all 0.3s ease;
            cursor: pointer;

            &:hover {
                background: rgba(255, 255, 255, 0.1);
            }
        }

        &-info {
            p {
                font-weight: 600;
                margin: 0;
                font-size: 1.1rem;
            }

            span {
                font-size: 0.9rem;
                opacity: 0.9;
                display: block;
                margin-top: 4px;
            }
        }
    }

    &__container {
        max-width: 600px;
        margin: 0 auto;
        padding: 1.5rem 1rem;
    }

    &__card {
        background: white;
        border-radius: 20px;
        padding: 1.5rem;
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.06);

        &-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;

            &-logo {
                width: 48px;
                height: 48px;
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            &-info {
                flex: 1;

                h2 {
                    margin: 0;
                    font-size: 1.2rem;
                    color: #1a73e8;
                    font-weight: 600;
                }

                span {
                    font-size: 0.9rem;
                    color: #64748b;
                }
            }
        }

        &-content {
            text-align: center;

            h3 {
                color: #1e293b;
                font-size: 1.3rem;
                margin-bottom: 1rem;
            }

            p {
                color: #64748b;
                line-height: 1.6;
                margin: 0.5rem 0;
            }
        }

        &-otp {
            background: linear-gradient(135deg, #f0f7ff 0%, #e6f0fd 100%);
            padding: 1.5rem;
            border-radius: 16px;
            margin: 1.5rem 0;

            span {
                font-size: 2.5rem;
                font-weight: 700;
                color: #1a73e8;
                letter-spacing: 8px;
                text-shadow: 0 2px 4px rgba(26, 115, 232, 0.1);
            }

            p {
                margin-top: 0.8rem;
                color: #64748b;
                font-size: 0.9rem;
            }
        }

        &-action {
            margin-top: 1.5rem;

            button {
                width: 100%;
                padding: 1rem;
                border: none;
                border-radius: 12px;
                background: linear-gradient(135deg, #1a73e8 0%, #0d47a1 100%);
                color: white;
                font-weight: 600;
                font-size: 1rem;
                cursor: pointer;
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;

                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(
                        135deg,
                        rgba(255, 255, 255, 0.1) 0%,
                        rgba(255, 255, 255, 0) 100%
                    );
                    opacity: 0;
                    transition: opacity 0.3s ease;
                }

                &:hover:not(:disabled)::before {
                    opacity: 1;
                }

                &:disabled {
                    background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
                    cursor: not-allowed;
                }
            }
        }

        &-timer {
            margin-top: 1rem;
            text-align: center;
            color: #64748b;
            font-size: 0.9rem;

            span {
                color: #1a73e8;
                font-weight: 600;
            }
        }

        &-footer {
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e2e8f0;
            text-align: center;

            p {
                color: #64748b;
                font-size: 0.9rem;
                line-height: 1.6;
                margin: 0;

                strong {
                    color: #1e293b;
                }
            }
        }
    }

    // Loading animation
    @keyframes pulse {
        0% {
            opacity: 1;
        }
        50% {
            opacity: 0.5;
        }
        100% {
            opacity: 1;
        }
    }

    .loading {
        animation: pulse 1.5s infinite;
    }
}
