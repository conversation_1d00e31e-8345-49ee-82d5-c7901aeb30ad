import React from 'react';
import { Button } from 'zmp-ui';
import FormInput from 'components/Form/FormInput';
import { RegisterFormProps } from '../types';

const RegisterForm: React.FC<RegisterFormProps> = ({ onSubmit, register, errors, onMaDinhDanhChange }) => {
    return (
        <form onSubmit={onSubmit}>
            <FormInput
                label="Họ tên"
                placeholder="Nhập họ tên"
                name="hoTen"
                register={register}
                error={errors.hoTen?.message}
                required={true}
            />
            <FormInput
                label="Email"
                placeholder="<EMAIL>"
                name="email"
                register={register}
                error={errors.email?.message}
            />
            <FormInput
                label="Số điện thoại"
                placeholder="Nhập số điện thoại"
                name="dienThoai"
                register={register}
                error={errors.dienThoai?.message}
                required={true}
                readOnly={true}
            />
            <FormInput
                label="Địa chỉ"
                placeholder="Nhập địa chỉ"
                name="diaChi"
                register={register}
                error={errors.diaChi?.message}
            />
            <FormInput
                label="Mã CCCD/CMND (12 ký tự)"
                placeholder="Nhập mã CCCD/CMND"
                name="maDinhDanh"
                register={register}
                error={errors.maDinhDanh?.message}
                onChange={onMaDinhDanhChange}
                required={true}
            />
            <Button onClick={onSubmit} size="large" className="register-button">
                Đăng ký
            </Button>
        </form>
    );
};

export default RegisterForm;
