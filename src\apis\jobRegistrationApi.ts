import { axiosClient } from './axiosClient';

export interface JobRegistrationData {
    maDinhDanh: string;
    maCongViec: number;
    hoTen?: string;
    soDienThoai?: string;
    diaChi?: string;
    ghiChu?: string;
}

export interface JobRegistrationResponse {
    success: string;
    message: string;
    data?: any;
}

export interface JobRegistrationListItem {
    id: number;
    maCongViec: number;
    maDinhDanh: string;
    ngayDangKy: string;
    ngayCapNhat?: string;
    ngayTao?: string;
    soDienThoai?: string;
    trangThai: number;
    hoTen?: string;
    diaChi?: string;
    ghiChu?: string;
    success?: string;
}

export interface JobRegistrationListResponse {
    success: string;
    message: string;
    data: JobRegistrationListItem[];
}

export interface CancelJobRegistrationData {
    maDangKy: number;
    maDinhDanh: string;
    lyDo: string;
}

export const jobRegistrationApi = {
    register: async (data: JobRegistrationData): Promise<JobRegistrationResponse> => {
        try {
            const response = await axiosClient.post('/api/Job/JobRegistration/Register', data);
            return response;
        } catch (error) {
            throw error;
        }
    },

    getByMaDinhDanh: async (maDinhDanh: string): Promise<JobRegistrationListResponse> => {
        try {
            const response = await axiosClient.get(`/api/Job/JobRegistration/GetByMaDinhDanh/${maDinhDanh}`);
            return response;
        } catch (error) {
            throw error;
        }
    },

    cancelRegistration: async (data: CancelJobRegistrationData): Promise<JobRegistrationResponse> => {
        try {
            const response = await axiosClient.put('/api/Job/JobRegistration/Cancel', null, {
                params: {
                    maDangKy: data.maDangKy,
                    maDinhDanh: data.maDinhDanh,
                    lyDo: data.lyDo,
                },
            });
            return response;
        } catch (error) {
            throw error;
        }
    },
};
