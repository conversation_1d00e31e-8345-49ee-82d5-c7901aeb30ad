.custom-modal {
    .modal-content-wrapper {
        border-radius: 14px;
        padding: 0;
        overflow: hidden;
        background-color: rgba(250, 250, 250, 0.95);
        // backdrop-filter: blur(10px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    .modal-message {
        font-size: 16px;
        font-weight: 500;
        color: #000;
        text-align: center;
    }
    .modal-button-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        // height: 64px;
        margin-top: 10px;
        padding: 12px;
    }
    .modal-button {
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.1);
        color: #fff;
        font-size: 16px;
        font-weight: 500;
        border-radius: 10px;
        border: none;
        outline: none;
        padding: 12px;
        background-color: var(--primary-color);
    }
}

// .ant-modal {
//     transform-origin: center bottom !important;

//     &.zoom-enter,
//     &.zoom-appear {
//         transform: scale(1.1);
//         opacity: 0;
//     }

//     &.zoom-enter-active,
//     &.zoom-appear-active {
//         transform: scale(1);
//         opacity: 1;
//         transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
//     }

//     &.zoom-exit {
//         transform: scale(1);
//         opacity: 1;
//     }

//     &.zoom-exit-active {
//         transform: scale(0.9);
//         opacity: 0;
//         transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
//     }
// }
