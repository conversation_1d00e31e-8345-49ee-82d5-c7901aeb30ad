{"name": "member-check-mini-app", "private": true, "version": "1.0.0", "description": "zmp-blank-templates", "repository": "", "license": "UNLICENSED", "browserslist": ["Android >= 5", "IOS >= 9.3", "Edge >= 15", "Safari >= 9.1", "Chrome >= 49", "Firefox >= 31", "Samsung >= 5"], "scripts": {"login": "zmp login", "start": "zmp start", "deploy": "zmp deploy"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@tanstack/react-query": "^4.36.1", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^1.3.2", "axios": "^1.7.7", "cheerio": "^1.0.0", "dayjs": "^1.11.13", "framer-motion": "^12.6.3", "lottie-react": "^2.4.0", "prop-types": "^15.8.1", "react": "^18.3.1", "react-barcode": "^1.6.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-icons": "^5.4.0", "react-lazy-load-image-component": "^1.6.3", "react-loading-skeleton": "^3.5.0", "react-qr-code": "^2.0.15", "react-router-dom": "^6.23.0", "recoil": "^0.7.7", "swiper": "^11.2.0", "tailwind-merge": "^2.6.0", "uuid": "^11.1.0", "yup": "^1.6.1", "zmp-sdk": "latest", "zmp-ui": "latest"}, "devDependencies": {"@types/react": "^18.3.1", "@types/react-dom": "^18.3.0", "@types/react-lazy-load-image-component": "^1.6.4", "@types/react-router-dom": "^5.3.3", "autoprefixer": "^10.4.19", "cross-env": "^7.0.3", "postcss": "^8.4.38", "postcss-cli": "^8.3.1", "postcss-preset-env": "^6.7.0", "sass": "^1.76.0", "tailwindcss": "^3.4.3", "vite": "^2.6.14", "vite-tsconfig-paths": "4.0.5"}}