.appointment-detail-modal {
    .zmp-modal-content {
        border-radius: 16px;
        padding: 0;
        overflow: hidden;
        max-width: 92%;
        width: 100%;
        max-height: 85vh;
        overflow-y: auto;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
}

.appointment-detail {
    display: flex;
    flex-direction: column;
    width: 100%;
    background-color: #fff;
    position: relative;

    &__close-icon {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 28px;
        height: 28px;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.2);
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 14px;
        cursor: pointer;
        z-index: 10;
        transition: all 0.2s ease;

        &:active {
            transform: scale(0.95);
            background-color: rgba(255, 255, 255, 0.3);
        }
    }

    &__header {
        background: linear-gradient(135deg, var(--primary-color) 0%, darken(#1976d2, 10%) 100%);
        color: white;
        padding: 16px;
        position: relative;
    }

    &__title {
        font-size: 16px;
        font-weight: 600;
        margin: 0 0 12px;
        text-align: center;
    }

    &__status {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 12px;

        svg {
            font-size: 16px;
        }

        &.active {
            color: #4caf50;
        }

        &.upcoming {
            color: #ff9800;
        }

        &.expired {
            color: #f44336;
        }
    }

    &__time-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 10px;
    }

    &__date-range {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    &__icon {
        font-size: 18px;
        color: rgba(255, 255, 255, 0.8);
    }

    &__date-label {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.9);
        line-height: 1.4;
    }

    &__days-counter {
        background-color: rgba(255, 255, 255, 0.15);
        border-radius: 8px;
        padding: 6px 10px;
        text-align: center;
    }

    &__days-value {
        font-size: 18px;
        font-weight: 700;
        line-height: 1;
    }

    &__days-label {
        font-size: 10px;
        margin-top: 2px;
        opacity: 0.9;
    }

    &__content {
        padding: 16px;
    }

    &__section {
        margin-bottom: 16px;
        border-bottom: 1px solid #eee;
        padding-bottom: 16px;

        &:last-child {
            margin-bottom: 0;
            border-bottom: none;
            padding-bottom: 0;
        }
    }

    &__section-title {
        font-size: 15px;
        font-weight: 600;
        color: #333;
        margin: 0 0 12px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    &__section-icon {
        color: var(--primary-color);
        font-size: 16px;
    }

    &__info-list {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    &__info-row {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        font-size: 14px;
        line-height: 1.4;
    }

    &__info-label {
        color: #666;
        flex: 0 0 40%;
    }

    &__info-value {
        color: #333;
        font-weight: 500;
        text-align: right;
        flex: 0 0 60%;
        word-break: break-word;

        &--highlight {
            color: var(--primary-color);
            font-weight: 700;
        }
    }

    &__actions {
        padding: 16px;
        display: flex;
        justify-content: center;
        border-top: 1px solid #eee;
    }

    &__close-btn {
        background-color: var(--primary-color);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 24px;
        font-size: 15px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        width: 100%;

        &:active {
            transform: scale(0.98);
            opacity: 0.9;
        }
    }
}

// Media queries for larger screens
@media (min-width: 480px) {
    .appointment-detail-modal {
        .zmp-modal-content {
            max-width: 400px;
        }
    }
}
