import React, { useEffect } from 'react';
import { useScrollRestoration } from '../hooks/useScrollRestoration';

export const ScrollRestoration: React.FC = () => {
    useScrollRestoration();

    // Debug log khi component mount
    useEffect(() => {
        console.log('ScrollRestoration component mounted');

        // Kiểm tra xem có dữ liệu vị trí scroll được lưu trong sessionStorage không
        const savedPositions = sessionStorage.getItem('scrollPositions');
        if (savedPositions) {
            console.log('Found saved scroll positions:', JSON.parse(savedPositions));
        } else {
            console.log('No saved scroll positions found');
        }

        return () => {
            console.log('ScrollRestoration component unmounted');
        };
    }, []);

    return null;
};
