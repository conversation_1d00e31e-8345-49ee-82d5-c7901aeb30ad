import React from 'react';
import './Message.scss';
import { MdArrowBackIosNew } from 'react-icons/md';
import { useQuery } from '@tanstack/react-query';
import { userNumberState } from 'state';
import { useRecoilValue } from 'recoil';
import { memberApi } from 'apis/getMemberApi';
import { useNavigate } from 'react-router-dom';
import { LazyLoadImage } from 'react-lazy-load-image-component';
import { BaoLocLogo } from 'assets/pngs';
import { Header, useSnackbar } from 'zmp-ui';
import { PageContainer } from 'components/page-container';

const Message: React.FC = () => {
    const { openSnackbar } = useSnackbar();
    const navigate = useNavigate();
    const userNumber = useRecoilValue(userNumberState);
    const [otpCode, setOtpCode] = React.useState<string>('');
    const [countdown, setCountdown] = React.useState<number>(0);
    const [loading, setLoading] = React.useState<boolean>(false);

    const { data, isLoading, isFetching, refetch } = useQuery({
        queryKey: ['member', userNumber],
        queryFn: () => memberApi.getMemberByPhoneNumber(userNumber),
        enabled: !!userNumber,
    });

    React.useEffect(() => {
        if (countdown > 0) {
            const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
            return () => clearTimeout(timer);
        }
    }, [countdown]);

    const handleGetOTP = async () => {
        try {
            setLoading(true);
            const res = await memberApi.getOtpCode(userNumber);
            if (res.success === '1') {
                openSnackbar({
                    text: 'Lấy mã OTP thành công',
                });
                setOtpCode(res?.data?.code);
                setCountdown(60);
            } else {
                openSnackbar({
                    text: 'Lấy mã OTP thất bại',
                });
            }
        } catch (error) {
            console.error('Lỗi khi lấy mã OTP:', error);
        } finally {
            setLoading(false);
        }
    };

    const formatDateTime = () => {
        const now = new Date();
        const weekdays = ['Chủ Nhật', 'Thứ Hai', 'Thứ Ba', 'Thứ Tư', 'Thứ Năm', 'Thứ Sáu', 'Thứ Bảy'];

        const hours = now.getHours().toString().padStart(2, '0');
        const minutes = now.getMinutes().toString().padStart(2, '0');
        const weekday = weekdays[now.getDay()];
        const day = now.getDate().toString().padStart(2, '0');
        const month = (now.getMonth() + 1).toString().padStart(2, '0');
        const year = now.getFullYear();

        return `${hours}:${minutes} - ${weekday}, ${day}/${month}/${year}`;
    };

    return (
        <PageContainer withHeader={true} className="bg-white">
            <Header
                title={data?.hoTen + ' - ' + data?.dienThoai}
                showBackIcon={false}
                className="no-divider have-shadow"
            />
            <div className="message">
                {/* <header className="message__header">
                <div className="message__header-back" onClick={() => navigate('/')}>
                    <MdArrowBackIosNew size={24} />
                </div>
                <div className="message__header-info">
                    <p>{data?.hoTen}</p>
                    <span>{data?.dienThoai}</span>
                </div>
            </header> */}

                <div className="message__container">
                    <div className="message__card">
                        <div className="message__card-header">
                            <div className="message__card-header-logo">
                                <LazyLoadImage src={BaoLocLogo} alt="Bảo Lộc Coffee" />
                            </div>
                            <div className="message__card-header-info">
                                <h2>Bảo Lộc Center</h2>
                                <span>{formatDateTime()}</span>
                            </div>
                        </div>

                        <div className="message__card-content">
                            <h3>Xác thực OTP</h3>
                            {otpCode ? (
                                <>
                                    <p>Mã xác thực của bạn là:</p>
                                    <div className="message__card-otp">
                                        <span>{otpCode}</span>
                                        <p>Mã có hiệu lực trong {countdown} giây</p>
                                    </div>
                                </>
                            ) : (
                                <p>Vui lòng nhấn nút bên dưới để nhận mã OTP</p>
                            )}

                            <div className="message__card-action">
                                <button onClick={handleGetOTP} disabled={countdown > 0 || loading}>
                                    {loading
                                        ? 'Đang xử lý...'
                                        : countdown > 0
                                        ? `Thử lại sau ${countdown}s`
                                        : 'Lấy mã OTP'}
                                </button>
                            </div>

                            <div className="message__card-footer">
                                <p>
                                    <strong>Lưu ý:</strong> Không chia sẻ mã OTP này cho bất kỳ ai để bảo vệ
                                    tài khoản của bạn
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </PageContainer>
    );
};

export default Message;
