.appointment-calendar {
    padding: 16px;
    position: relative;
    
    .calendar-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
    }
    
    .calendar-title {
        font-size: 18px;
        font-weight: 600;
        color: var(--primary-color);
    }
    
    .calendar-nav-button {
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: white;
        border: 1px solid #ddd;
        border-radius: 50%;
        color: #666;
        cursor: pointer;
        transition: all 0.2s ease;
        
        &:hover {
            background-color: #f5f5f5;
            color: var(--primary-color);
        }
    }
    
    .calendar-days-header {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        margin-bottom: 8px;
    }
    
    .calendar-day-name {
        text-align: center;
        font-size: 12px;
        font-weight: 600;
        color: #666;
        padding: 8px 0;
    }
    
    .calendar-grid {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 4px;
    }
    
    .calendar-day {
        aspect-ratio: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: white;
        border-radius: 8px;
        position: relative;
        cursor: pointer;
        transition: all 0.2s ease;
        
        &.empty {
            background-color: transparent;
            cursor: default;
        }
        
        &.today {
            background-color: rgba(33, 57, 130, 0.1);
            
            .day-number {
                color: var(--primary-color);
                font-weight: 700;
            }
        }
        
        &.has-appointments {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            
            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            }
        }
        
        .day-number {
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }
        
        .appointment-indicators {
            display: flex;
            gap: 2px;
            margin-top: 4px;
        }
        
        .appointment-indicator {
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background-color: var(--primary-color);
            
            &.cancelled {
                background-color: #f44336;
            }
        }
    }
    
    .add-appointment-button {
        position: fixed;
        bottom: 80px;
        right: 20px;
        width: 56px;
        height: 56px;
        border-radius: 50%;
        background-color: var(--primary-color);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        border: none;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        cursor: pointer;
        transition: all 0.2s ease;
        z-index: 10;
        
        &:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
        }
    }
}
