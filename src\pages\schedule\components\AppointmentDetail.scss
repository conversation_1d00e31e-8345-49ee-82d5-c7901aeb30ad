.appointment-detail {
    padding: 16px;
    
    &__header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    
    &__title {
        font-size: 20px;
        font-weight: 700;
        color: #333;
        flex: 1;
    }
    
    &__status {
        padding: 6px 12px;
        border-radius: 16px;
        font-size: 12px;
        font-weight: 600;
        
        &.status-confirmed {
            background-color: rgba(76, 175, 80, 0.1);
            color: #4caf50;
        }
        
        &.status-pending {
            background-color: rgba(255, 152, 0, 0.1);
            color: #ff9800;
        }
        
        &.status-cancelled {
            background-color: rgba(244, 67, 54, 0.1);
            color: #f44336;
        }
        
        &.status-completed {
            background-color: rgba(33, 150, 243, 0.1);
            color: #2196f3;
        }
    }
    
    &__info {
        background-color: white;
        border-radius: 12px;
        padding: 16px;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }
    
    .info-item {
        display: flex;
        margin-bottom: 16px;
        
        &:last-child {
            margin-bottom: 0;
        }
        
        &--full {
            .info-item__value {
                white-space: normal;
            }
        }
        
        &__icon {
            color: var(--primary-color);
            font-size: 18px;
            margin-right: 12px;
            margin-top: 2px;
        }
        
        &__content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        &__label {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }
        
        &__value {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }
    }
    
    &__actions {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
    }
    
    .btn {
        border-radius: 8px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        
        &-outline {
            background-color: transparent;
            border: 1px solid #f44336;
            color: #f44336;
            padding: 10px 16px;
            
            &:hover {
                background-color: rgba(244, 67, 54, 0.1);
            }
        }
        
        &-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--primary-color);
            color: white;
            border: none;
            
            &:hover {
                background-color: darken(#213982, 10%);
            }
            
            &--danger {
                background-color: #f44336;
                
                &:hover {
                    background-color: darken(#f44336, 10%);
                }
            }
        }
    }
    
    &__reminder {
        background-color: rgba(33, 150, 243, 0.1);
        border-radius: 12px;
        padding: 16px;
        
        p {
            font-size: 13px;
            color: #2196f3;
            margin-bottom: 8px;
            
            &:last-child {
                margin-bottom: 0;
            }
        }
    }
}
