import { useEffect } from 'react';
import { useRecoilState, useRecoilValue } from 'recoil';
import { phieuKetQuaState } from 'state/phieuKetQuaState';
import { appointmentNotificationState, hasShownNotificationState } from 'state/notificationState';
import { PhieuKetQua } from 'apis/getPhieuKetQuaApi';
import { formatCurrency } from 'utils/common';

export const useGlobalAppointmentNotifications = () => {
    const [notificationState, setNotificationState] = useRecoilState(appointmentNotificationState);
    const phieuKetQua = useRecoilValue(phieuKetQuaState);

    const [hasShownNotification, setHasShownNotification] = useRecoilState(hasShownNotificationState);

    useEffect(() => {
        if (!phieuKetQua || phieuKetQua.length === 0 || hasShownNotification) {
            return;
        }

        const validAppointments = phieuKetQua.filter((appointment) => {
            return appointment && appointment.thoiGianBatDau && appointment.thoiGianKetThuc;
        });

        if (validAppointments.length === 0) {
            return;
        }

        const appointment = validAppointments[0];

        try {
            const startDateParts = appointment.thoiGianBatDau.split('-');
            const endDateParts = appointment.thoiGianKetThuc.split('-');

            if (startDateParts.length === 3 && endDateParts.length === 3) {
                setNotificationState({
                    appointment,
                    appointments: validAppointments,
                    currentIndex: 0,
                    isVisible: true,
                });

                setHasShownNotification(true);

                const sessionKey = 'hasShownNotification_' + new Date().toDateString();
                sessionStorage.setItem(sessionKey, 'true');
            }
        } catch (error) {}
    }, [phieuKetQua, setNotificationState, hasShownNotification, setHasShownNotification]);

    const closeNotification = () => {
        setNotificationState((prev) => ({
            ...prev,
            isVisible: false,
        }));
    };

    const nextAppointment = () => {
        setNotificationState((prev) => {
            if (!prev.appointments || prev.appointments.length <= 1) {
                return prev;
            }

            const nextIndex = (prev.currentIndex + 1) % prev.appointments.length;
            return {
                ...prev,
                currentIndex: nextIndex,
                appointment: prev.appointments[nextIndex],
            };
        });
    };

    const prevAppointment = () => {
        setNotificationState((prev) => {
            if (!prev.appointments || prev.appointments.length <= 1) {
                return prev;
            }

            const prevIndex = (prev.currentIndex - 1 + prev.appointments.length) % prev.appointments.length;
            return {
                ...prev,
                currentIndex: prevIndex,
                appointment: prev.appointments[prevIndex],
            };
        });
    };

    const formatNotificationContent = (phieuKetQua: PhieuKetQua) => {
        return {
            title: 'Trợ cấp thất nghiệp',
            message: `Bạn đang trong thời gian hưởng trợ cấp từ ${phieuKetQua.thoiGianBatDau} đến ${phieuKetQua.thoiGianKetThuc}`,
            details: `Mức trợ cấp: ${
                phieuKetQua.mucTroCapHangThang
                    ? formatCurrency(phieuKetQua.mucTroCapHangThang)
                    : 'Chưa xác định'
            }`,
        };
    };

    const resetNotificationState = () => {
        setHasShownNotification(false);
        const sessionKey = 'hasShownNotification_' + new Date().toDateString();
        sessionStorage.removeItem(sessionKey);

        if (phieuKetQua && phieuKetQua.length > 0) {
            const validAppointments = phieuKetQua.filter((appointment) => {
                return appointment && appointment.thoiGianBatDau && appointment.thoiGianKetThuc;
            });

            if (validAppointments.length > 0) {
                setNotificationState({
                    appointment: validAppointments[0],
                    appointments: validAppointments,
                    currentIndex: 0,
                    isVisible: true,
                });
            }
        }
    };

    return {
        currentNotification: notificationState.appointment,
        allNotifications: notificationState.appointments,
        currentIndex: notificationState.currentIndex,
        totalNotifications: notificationState.appointments.length,
        isNotificationVisible: notificationState.isVisible,
        closeNotification,
        nextAppointment,
        prevAppointment,
        formatNotificationContent,
        resetNotificationState,
    };
};
