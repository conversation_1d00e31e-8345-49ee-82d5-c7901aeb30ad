import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import * as cheerio from 'cheerio';
import { fetchWithServerProxy } from './useServerProxy';

const CACHE_TIME = 24 * 60 * 60 * 1000;
const STALE_TIME = 12 * 60 * 60 * 1000;

export interface JobItem {
    id: string;
    title: string;
    company: string;
    location: string;
    salary: string;
    image: string;
    link: string;
}

export interface NewsItem {
    id: string;
    title: string;
    date: string;
    image: string;
    link: string;
    content?: string;
    images?: string[];
}

export interface CrawlData {
    featuredJobs: JobItem[];
    latestJobs: JobItem[];
    marketReports: NewsItem[];
    centerActivities: NewsItem[];
}

const PROXY_URLS = [
    'https://corsproxy.io/?',
    'https://cors-anywhere.herokuapp.com/',
    'https://api.allorigins.win/raw?url=',
    'https://thingproxy.freeboard.io/fetch/',
    'https://cors.bridged.cc/',
];
const TARGET_URL = 'https://thongtinvieclamkhanhhoa.vn';

const fetchWithFallbackProxies = async (url: string) => {
    let lastError: Error | unknown;

    try {
        return await fetchWithServerProxy(url);
    } catch (error) {
        console.error('Server proxy failed, falling back to public proxies:', error);
        lastError = error;
    }

    for (const proxyUrl of PROXY_URLS) {
        try {
            const response = await axios.get(`${proxyUrl}${encodeURIComponent(url)}`, {
                timeout: 10000, // 10 seconds timeout
                headers: {
                    'User-Agent':
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                    Referer: 'https://www.google.com/',
                },
            });
            return response.data;
        } catch (error) {
            console.error(`Error with proxy ${proxyUrl}:`, error);
            lastError = error;
        }
    }

    throw lastError || new Error('All proxies failed');
};

const fetchData = async () => {
    const html = await fetchWithServerProxy('https://thongtinvieclamkhanhhoa.vn');
    const $ = cheerio.load(html);

    const featuredJobs: JobItem[] = [];
    const latestJobs: JobItem[] = [];
    const marketReports: NewsItem[] = [];
    const centerActivities: NewsItem[] = [];

    $('.contentJOb .itemJob').each((index, element) => {
        const id = $(element).attr('href')?.split('Id=')[1] || `job-${index}`;
        const title = $(element).find('.titleJob').text().trim();
        const location = $(element).find('.textArea').text().replace('Khu vực:', '').trim();
        const salary = $(element).find('.textSalary').text().replace('Mức lương:', '').trim();

        let image = $(element).find('img').attr('src') || '';

        let company = $(element).find('.nameCompany').text().trim();
        if (!company) {
            company = $(element).find('.textCompany').text().replace('Công ty:', '').trim();
        }

        const link = $(element).attr('href') || '';

        if (image && image.startsWith('/')) {
            image = `${TARGET_URL}${image}`;
        } else if (image && !image.startsWith('http')) {
            image = `${TARGET_URL}/${image}`;
        }

        featuredJobs.push({
            id,
            title,
            company,
            location,
            salary,
            image,
            link: link.startsWith('/') ? `${TARGET_URL}${link}` : link,
        });
    });

    if (featuredJobs.length === 0) {
        $('.itemJob').each((index, element) => {
            if (index < 6) {
                const id = $(element).attr('href')?.split('Id=')[1] || `job-${index}`;
                const title = $(element).find('.titleJob').text().trim();
                const location = $(element).find('.textArea').text().replace('Khu vực:', '').trim();
                const salary = $(element).find('.textSalary').text().replace('Mức lương:', '').trim();
                const image = $(element).find('img').attr('src') || '';
                const link = $(element).attr('href') || '';
                let company = $(element).find('.nameCompany, .textCompany').text().trim();
                if (company.includes('Công ty:')) {
                    company = company.replace('Công ty:', '').trim();
                }

                featuredJobs.push({
                    id,
                    title,
                    company,
                    location,
                    salary,
                    image: image.startsWith('/') ? `${TARGET_URL}${image}` : image,
                    link: link.startsWith('/') ? `${TARGET_URL}${link}` : link,
                });
            }
        });
    }

    $('.listContent .itemJob').each((index, element) => {
        if (index < 10) {
            const id = $(element).attr('href')?.split('Id=')[1] || `latest-${index}`;
            const title = $(element).find('.titleJob').text().trim();
            const location = $(element).find('.textArea').text().replace('Khu vực:', '').trim();
            const salary = $(element).find('.textSalary').text().replace('Mức lương:', '').trim();
            const image = $(element).find('img').attr('src') || '';
            const link = $(element).attr('href') || '';
            const company = '';

            latestJobs.push({
                id,
                title,
                company,
                location,
                salary,
                image,
                link: link.startsWith('/') ? `${TARGET_URL}${link}` : link,
            });
        }
    });

    $('h2:contains("Tin tức - Sự kiện")')
        .parent()
        .parent()
        .find('.itemNews')
        .each((index, element) => {
            if (index < 6) {
                const link = $(element).attr('href') || '';
                const id = link.split('/').pop() || `news-${index}`;

                let title = $(element).text().trim();
                title = title.replace(/Chi tiết$/g, '').trim();
                title = title.replace(/Ngày:\s*[\d\/]+/g, '').trim();

                let date = '';
                const dateText = $(element)
                    .text()
                    .match(/Ngày:\s*([\d\/]+)/i);
                if (dateText && dateText[1]) {
                    date = dateText[1].trim();
                }

                let image = '';

                const imgElement = $(element).find('img');
                if (imgElement.length > 0) {
                    image = imgElement.attr('src') || '';
                } else {
                    const bgElement = $(element).find('[style*="background-image"]');
                    if (bgElement.length > 0) {
                        const bgStyle = bgElement.attr('style') || '';
                        const bgMatch = bgStyle.match(/background-image:\s*url\(['"](.*?)['"]\)/i);
                        if (bgMatch && bgMatch[1]) {
                            image = bgMatch[1];
                        }
                    }
                }

                if (!image) {
                    const parentElement = $(element).parent();
                    const siblingImg = parentElement.find('img');
                    if (siblingImg.length > 0) {
                        image = siblingImg.attr('src') || '';
                    }
                }

                if (image && image.startsWith('/')) {
                    image = `${TARGET_URL}${image}`;
                } else if (image && !image.startsWith('http')) {
                    image = `${TARGET_URL}/${image}`;
                }

                marketReports.push({
                    id,
                    title,
                    date,
                    image:
                        image || `${TARGET_URL}/assets/images/brand/trung-tam-dich-viec-lam-logo-header.svg`,
                    link: link.startsWith('/') ? `${TARGET_URL}${link}` : link,
                });
            }
        });

    $('h2:contains("Hoạt động trung tâm")')
        .parent()
        .parent()
        .find('.itemNews')
        .each((index, element) => {
            if (index < 6) {
                const link = $(element).attr('href') || '';
                const id = link.split('/').pop() || `activity-${index}`;

                let title = $(element).text().trim();
                title = title.replace(/Chi tiết$/g, '').trim();
                title = title.replace(/Ngày:\s*[\d\/]+/g, '').trim();

                let date = '';
                const dateText = $(element)
                    .text()
                    .match(/Ngày:\s*([\d\/]+)/i);
                if (dateText && dateText[1]) {
                    date = dateText[1].trim();
                }

                let image = '';

                const imgElement = $(element).find('img');
                if (imgElement.length > 0) {
                    image = imgElement.attr('src') || '';
                } else {
                    const bgElement = $(element).find('[style*="background-image"]');
                    if (bgElement.length > 0) {
                        const bgStyle = bgElement.attr('style') || '';
                        const bgMatch = bgStyle.match(/background-image:\s*url\(['"](.*?)['"]\)/i);
                        if (bgMatch && bgMatch[1]) {
                            image = bgMatch[1];
                        }
                    }
                }

                if (!image) {
                    const parentElement = $(element).parent();
                    const siblingImg = parentElement.find('img');
                    if (siblingImg.length > 0) {
                        image = siblingImg.attr('src') || '';
                    }
                }

                if (image && image.startsWith('/')) {
                    image = `${TARGET_URL}${image}`;
                } else if (image && !image.startsWith('http')) {
                    image = `${TARGET_URL}/${image}`;
                }

                centerActivities.push({
                    id,
                    title,
                    date,
                    image:
                        image || `${TARGET_URL}/assets/images/brand/trung-tam-dich-viec-lam-logo-header.svg`,
                    link: link.startsWith('/') ? `${TARGET_URL}${link}` : link,
                });
            }
        });

    return {
        featuredJobs,
        latestJobs,
        marketReports,
        centerActivities,
    };
};

export const useCrawlData = () => {
    return useQuery(['crawlData'], fetchData, {
        cacheTime: CACHE_TIME,
        staleTime: STALE_TIME,
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
    });
};
