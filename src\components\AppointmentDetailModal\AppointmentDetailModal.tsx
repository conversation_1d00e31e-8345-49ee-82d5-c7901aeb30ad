import { PhieuKetQua } from 'apis/getPhieuKetQuaApi';
import React from 'react';
import {
    FaCalendarAlt,
    FaCheckCircle,
    FaExclamationCircle,
    FaHourglassHalf,
    FaIdCard,
    FaInfoCircle,
    FaMoneyBillWave,
    FaTimes,
} from 'react-icons/fa';
import { formatCurrency } from 'utils/common';
import { Modal } from 'zmp-ui';
import './AppointmentDetailModal.scss';

interface AppointmentDetailModalProps {
    appointment: PhieuKetQua;
    visible: boolean;
    onClose: () => void;
}

const AppointmentDetailModal: React.FC<AppointmentDetailModalProps> = ({ appointment, visible, onClose }) => {
    // Hàm định dạng ngày tháng
    const formatDate = (dateString: string) => {
        try {
            if (!dateString) return '';

            // <PERSON><PERSON> lý trường hợp ngày tháng có định dạng dd-MM-yyyy
            if (dateString.includes('-') && dateString.split('-').length === 3) {
                const parts = dateString.split('-');
                if (parts[0].length === 2 && parts[1].length === 2 && parts[2].length === 4) {
                    return dateString; // Đã đúng định dạng dd-MM-yyyy
                }
            }

            // Xử lý trường hợp ngày tháng có định dạng ISO
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return dateString;

            return date.toLocaleDateString('vi-VN', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
            });
        } catch (error) {
            console.error('Lỗi khi định dạng ngày tháng:', error);
            return dateString;
        }
    };

    // Tính số ngày còn lại đến ngày kết thúc hoặc số ngày đến khi bắt đầu
    const calculateDays = () => {
        try {
            const today = new Date();

            // Chuyển đổi ngày bắt đầu
            const startParts = appointment.thoiGianBatDau.split('-');
            if (startParts.length !== 3) return { days: 0, isUpcoming: false, isActive: false };
            const startDate = new Date(`${startParts[2]}-${startParts[1]}-${startParts[0]}`);

            // Chuyển đổi ngày kết thúc
            const endParts = appointment.thoiGianKetThuc.split('-');
            if (endParts.length !== 3) return { days: 0, isUpcoming: false, isActive: false };
            const endDate = new Date(`${endParts[2]}-${endParts[1]}-${endParts[0]}`);

            // Kiểm tra trạng thái
            if (today < startDate) {
                // Sắp đến
                const daysUntilStart = Math.ceil(
                    (startDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
                );
                return { days: daysUntilStart, isUpcoming: true, isActive: false };
            } else if (today <= endDate) {
                // Đang diễn ra
                const daysRemaining = Math.ceil(
                    (endDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
                );
                return { days: daysRemaining, isUpcoming: false, isActive: true };
            } else {
                // Đã kết thúc
                const daysSinceEnd = Math.ceil((today.getTime() - endDate.getTime()) / (1000 * 60 * 60 * 24));
                return { days: daysSinceEnd, isUpcoming: false, isActive: false };
            }
        } catch (error) {
            console.error('Lỗi khi tính toán ngày:', error);
            return { days: 0, isUpcoming: false, isActive: false };
        }
    };

    const { days, isUpcoming, isActive } = calculateDays();

    return (
        <Modal visible={visible} onClose={onClose} className="appointment-detail-modal">
            <div className="appointment-detail">
                <button className="appointment-detail__close-icon" onClick={onClose}>
                    <FaTimes />
                </button>

                <div className="appointment-detail__header">
                    <h2 className="appointment-detail__title">Chi tiết trợ cấp</h2>
                    <div
                        className={`appointment-detail__status ${
                            isActive ? 'active' : isUpcoming ? 'upcoming' : 'expired'
                        }`}
                    >
                        {isActive ? (
                            <FaCheckCircle />
                        ) : isUpcoming ? (
                            <FaHourglassHalf />
                        ) : (
                            <FaExclamationCircle />
                        )}
                        <span>{isActive ? 'Đang diễn ra' : isUpcoming ? 'Sắp đến' : 'Đã kết thúc'}</span>
                    </div>

                    <div className="appointment-detail__time-info">
                        <div className="appointment-detail__date-range">
                            <FaCalendarAlt className="appointment-detail__icon" />
                            <div>
                                <div className="appointment-detail__date-label">
                                    Từ {formatDate(appointment.thoiGianBatDau)}
                                </div>
                                <div className="appointment-detail__date-label">
                                    Đến {formatDate(appointment.thoiGianKetThuc)}
                                </div>
                            </div>
                        </div>

                        <div className="appointment-detail__days-counter">
                            <div className="appointment-detail__days-value">{days}</div>
                            <div className="appointment-detail__days-label">
                                {isActive ? 'ngày còn lại' : isUpcoming ? 'ngày nữa' : 'ngày trước'}
                            </div>
                        </div>
                    </div>
                </div>

                <div className="appointment-detail__content">
                    <div className="appointment-detail__section">
                        <h3 className="appointment-detail__section-title">
                            <FaIdCard className="appointment-detail__section-icon" />
                            Thông tin cá nhân
                        </h3>
                        <div className="appointment-detail__info-list">
                            <div className="appointment-detail__info-row">
                                <span className="appointment-detail__info-label">Họ tên:</span>
                                <span className="appointment-detail__info-value">{appointment.hoTen}</span>
                            </div>

                            <div className="appointment-detail__info-row">
                                <span className="appointment-detail__info-label">Mã định danh:</span>
                                <span className="appointment-detail__info-value">
                                    {appointment.maDinhDanh}
                                </span>
                            </div>

                            <div className="appointment-detail__info-row">
                                <span className="appointment-detail__info-label">Ngày sinh:</span>
                                <span className="appointment-detail__info-value">
                                    {formatDate(appointment.ngaySinh)}
                                </span>
                            </div>

                            <div className="appointment-detail__info-row">
                                <span className="appointment-detail__info-label">Địa chỉ:</span>
                                <span className="appointment-detail__info-value">{appointment.diaChi}</span>
                            </div>
                        </div>
                    </div>

                    <div className="appointment-detail__section">
                        <h3 className="appointment-detail__section-title">
                            <FaMoneyBillWave className="appointment-detail__section-icon" />
                            Thông tin trợ cấp
                        </h3>
                        <div className="appointment-detail__info-list">
                            <div className="appointment-detail__info-row">
                                <span className="appointment-detail__info-label">Mức trợ cấp:</span>
                                <span className="appointment-detail__info-value appointment-detail__info-value--highlight">
                                    {appointment.mucTroCapHangThang
                                        ? formatCurrency(appointment.mucTroCapHangThang)
                                        : 'Chưa xác định'}
                                </span>
                            </div>

                            <div className="appointment-detail__info-row">
                                <span className="appointment-detail__info-label">Số tháng hưởng:</span>
                                <span className="appointment-detail__info-value">
                                    {appointment.soThangDuocHuongTroCap
                                        ? appointment.soThangDuocHuongTroCap + ' tháng'
                                        : 'Chưa xác định'}
                                </span>
                            </div>

                            <div className="appointment-detail__info-row">
                                <span className="appointment-detail__info-label">Tài khoản:</span>
                                <span className="appointment-detail__info-value">
                                    {appointment.taiKhoan || 'Chưa cung cấp'}
                                </span>
                            </div>

                            <div className="appointment-detail__info-row">
                                <span className="appointment-detail__info-label">Ngân hàng:</span>
                                <span className="appointment-detail__info-value">
                                    {appointment.nganHang || 'Chưa cung cấp'}
                                </span>
                            </div>
                        </div>
                    </div>

                    {appointment.noiNhanTroCap && (
                        <div className="appointment-detail__section">
                            <h3 className="appointment-detail__section-title">
                                <FaInfoCircle className="appointment-detail__section-icon" />
                                Thông tin khác
                            </h3>
                            <div className="appointment-detail__info-list">
                                <div className="appointment-detail__info-row">
                                    <span className="appointment-detail__info-label">Nơi nhận trợ cấp:</span>
                                    <span className="appointment-detail__info-value">
                                        {appointment.noiNhanTroCap}
                                    </span>
                                </div>
                            </div>
                        </div>
                    )}
                </div>

                <div className="appointment-detail__actions">
                    <button className="appointment-detail__close-btn" onClick={onClose}>
                        Đóng
                    </button>
                </div>
            </div>
        </Modal>
    );
};

export default AppointmentDetailModal;
