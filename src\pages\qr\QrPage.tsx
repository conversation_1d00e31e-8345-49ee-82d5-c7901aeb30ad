import React from 'react';
import './QrPage.scss';
import { Page } from 'zmp-ui';
import Barcode from 'react-barcode';
import { memberApi } from 'apis/getMemberApi';
import { useQuery } from '@tanstack/react-query';
import { useRecoilValue } from 'recoil';
import { userNumberState } from 'state';
function QrPage() {
    const userNumber = useRecoilValue(userNumberState);
    return (
        <Page className="qr-page" hideScrollbar>
            <h1>Mã Bar Code thành viên</h1>
            {userNumber ? (
                <>
                    <Barcode value={userNumber} width={2} height={100} format="CODE128" displayValue={true} />
                    <div className="qr-page__warning">Vui lòng đưa mã này cho nhân viên</div>
                </>
            ) : (
                <div className="qr-page__warning">Bạn chưa là thành viên!</div>
            )}
        </Page>
    );
}

export default QrPage;
