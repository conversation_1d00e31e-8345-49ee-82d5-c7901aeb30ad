import React from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useCrawlJobDetail } from '../../hooks/useCrawlJobDetail';
import { LazyLoadImage } from 'react-lazy-load-image-component';
import 'react-lazy-load-image-component/src/effects/blur.css';
import {
    FaArrowLeft,
    FaCalendarAlt,
    FaMapMarkerAlt,
    FaMoneyBillWave,
    FaBuilding,
    FaGraduationCap,
    FaUserTie,
    FaUsers,
} from 'react-icons/fa';
import './JobDetail.scss';
import { PageContainer } from 'components/page-container';
import { Header } from 'zmp-ui';

const JobDetail: React.FC = () => {
    const { id } = useParams<{ id: string }>();
    const navigate = useNavigate();
    const { state } = useLocation() as { state?: { url: string } };

    // Sử dụng URL từ state nếu có, nếu không thì tạo URL từ ID
    const jobUrl =
        state?.url || (id ? `https://thongtinvieclamkhanhhoa.vn/job/Chi-tiet-viec-lam?Id=${id}` : '');
    const { data, loading, error } = useCrawlJobDetail(jobUrl);

    const handleBack = () => {
        // Quay lại trang trước đó và giữ nguyên vị trí scroll
        navigate(-1);
    };

    if (loading) {
        return (
            <PageContainer className="job-detail-page" withHeader resetScroll={true}>
                <div className="job-detail-container">
                    <div className="job-detail-loading">
                        <div className="loading-spinner"></div>
                        <p>Đang tải thông tin việc làm...</p>
                    </div>
                </div>
            </PageContainer>
        );
    }

    if (error || !data) {
        return (
            <PageContainer className="job-detail-page" withHeader resetScroll={true}>
                <div className="job-detail-container">
                    <div className="job-detail-error">
                        <p>{error || 'Không thể tải thông tin việc làm. Vui lòng thử lại sau.'}</p>
                        <button className="back-button" onClick={handleBack}>
                            <FaArrowLeft /> Quay lại
                        </button>
                    </div>
                </div>
            </PageContainer>
        );
    }

    return (
        <PageContainer className="news-detail-page" withHeader resetScroll={true}>
            <Header title="Chi tiết công việc" showBackIcon onBackClick={handleBack} />
            <div className="job-detail-page">
                <div className="job-detail-container">
                    <div className="job-detail-header">
                        <div className="job-detail-company">
                            <div className="company-logo">
                                <LazyLoadImage
                                    src={
                                        data.image ||
                                        'https://thongtinvieclamkhanhhoa.vn/assets/images/brand/trung-tam-dich-viec-lam-logo-header.svg'
                                    }
                                    alt={data.company || data.title}
                                    effect="blur"
                                    width="100%"
                                    height="100%"
                                    placeholder={<div className="image-placeholder"></div>}
                                />
                            </div>
                            <div className="company-info">
                                <h1 className="job-detail-title">{data.title}</h1>
                                {data.company && <h2 className="company-name">{data.company}</h2>}
                                <div className="job-meta">
                                    {data.deadline && (
                                        <div className="job-deadline">
                                            <FaCalendarAlt className="meta-icon" />
                                            <span>Hạn nộp: {data.deadline}</span>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="job-detail-body">
                        <div className="job-detail-info-cards">
                            <h3 className="section-title">Thông tin việc làm</h3>

                            <div className="info-cards-row">
                                <div className="info-card">
                                    <div className="info-card-icon salary-icon">
                                        <FaMoneyBillWave />
                                    </div>
                                    <div className="info-card-content">
                                        <h4 className="info-card-title">Mức lương</h4>
                                        <p className="info-card-value">
                                            {data.salary || data.jobInfo?.salary || 'Thỏa thuận'}
                                        </p>
                                    </div>
                                </div>

                                <div className="info-card">
                                    <div className="info-card-icon salary-detail-icon">
                                        <FaMoneyBillWave />
                                    </div>
                                    <div className="info-card-content">
                                        <h4 className="info-card-title">Mức lương cụ thể</h4>
                                        <p className="info-card-value">
                                            {data.jobInfo?.salaryDetail || 'Thỏa thuận khi phỏng vấn'}
                                        </p>
                                    </div>
                                </div>

                                <div className="info-card">
                                    <div className="info-card-icon education-icon">
                                        <FaGraduationCap />
                                    </div>
                                    <div className="info-card-content">
                                        <h4 className="info-card-title">Cấp bậc</h4>
                                        <p className="info-card-value">
                                            {data.jobInfo?.level || 'Nhân viên'}
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div className="info-cards-row">
                                <div className="info-card">
                                    <div className="info-card-icon position-icon">
                                        <FaUserTie />
                                    </div>
                                    <div className="info-card-content">
                                        <h4 className="info-card-title">Chức danh công việc</h4>
                                        <p className="info-card-value">
                                            {data.jobInfo?.position || data.title || '-'}
                                        </p>
                                    </div>
                                </div>

                                <div className="info-card">
                                    <div className="info-card-icon type-icon">
                                        <FaBuilding />
                                    </div>
                                    <div className="info-card-content">
                                        <h4 className="info-card-title">Hình thức làm việc</h4>
                                        <p className="info-card-value">
                                            {data.jobInfo?.type || 'Toàn thời gian'}
                                        </p>
                                    </div>
                                </div>

                                <div className="info-card">
                                    <div className="info-card-icon quantity-icon">
                                        <FaUsers />
                                    </div>
                                    <div className="info-card-content">
                                        <h4 className="info-card-title">Số lượng cần tuyển</h4>
                                        <p className="info-card-value">{data.jobInfo?.quantity || '1'}</p>
                                    </div>
                                </div>
                            </div>

                            <div className="info-cards-row">
                                <div className="info-card">
                                    <div className="info-card-icon deadline-icon">
                                        <FaCalendarAlt />
                                    </div>
                                    <div className="info-card-content">
                                        <h4 className="info-card-title">Hạn nộp</h4>
                                        <p className="info-card-value">{data.deadline || '-'}</p>
                                    </div>
                                </div>

                                <div className="info-card">
                                    <div className="info-card-icon time-icon">
                                        <FaCalendarAlt />
                                    </div>
                                    <div className="info-card-content">
                                        <h4 className="info-card-title">Thời gian làm việc</h4>
                                        <p className="info-card-value">{data.jobInfo?.workingTime || '-'}</p>
                                    </div>
                                </div>

                                <div className="info-card">
                                    <div className="info-card-icon industry-icon">
                                        <FaBuilding />
                                    </div>
                                    <div className="info-card-content">
                                        <h4 className="info-card-title">Ngành nghề</h4>
                                        <p className="info-card-value">{data.jobInfo?.industry || '-'}</p>
                                    </div>
                                </div>
                            </div>

                            <div className="info-cards-row">
                                <div className="info-card">
                                    <div className="info-card-icon experience-icon">
                                        <FaUserTie />
                                    </div>
                                    <div className="info-card-content">
                                        <h4 className="info-card-title">Yêu cầu kinh nghiệm</h4>
                                        <p className="info-card-value">
                                            {data.jobInfo?.experience || 'Không yêu cầu kinh nghiệm'}
                                        </p>
                                    </div>
                                </div>

                                <div className="info-card">
                                    <div className="info-card-icon education-req-icon">
                                        <FaGraduationCap />
                                    </div>
                                    <div className="info-card-content">
                                        <h4 className="info-card-title">Yêu cầu bằng cấp</h4>
                                        <p className="info-card-value">
                                            {data.jobInfo?.education || 'Không yêu cầu trình độ'}
                                        </p>
                                    </div>
                                </div>

                                <div className="info-card">
                                    <div className="info-card-icon gender-icon">
                                        <FaUserTie />
                                    </div>
                                    <div className="info-card-content">
                                        <h4 className="info-card-title">Yêu cầu giới tính</h4>
                                        <p className="info-card-value">{data.jobInfo?.gender || 'Nam/ nữ'}</p>
                                    </div>
                                </div>
                            </div>

                            <div className="info-cards-row">
                                <div className="info-card">
                                    <div className="info-card-icon language-icon">
                                        <FaGraduationCap />
                                    </div>
                                    <div className="info-card-content">
                                        <h4 className="info-card-title">Trình độ ngoại ngữ</h4>
                                        <p className="info-card-value">{data.jobInfo?.language || '-'}</p>
                                    </div>
                                </div>

                                <div className="info-card">
                                    <div className="info-card-icon computer-icon">
                                        <FaGraduationCap />
                                    </div>
                                    <div className="info-card-content">
                                        <h4 className="info-card-title">Trình độ tin học</h4>
                                        <p className="info-card-value">
                                            {data.jobInfo?.computerSkills || 'Tin học văn phòng'}
                                        </p>
                                    </div>
                                </div>

                                <div className="info-card">
                                    <div className="info-card-icon location-icon">
                                        <FaMapMarkerAlt />
                                    </div>
                                    <div className="info-card-content">
                                        <h4 className="info-card-title">Địa điểm làm việc</h4>
                                        <p className="info-card-value">
                                            {data.jobInfo?.location || data.location || '-'}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {data.description && (
                            <div className="job-detail-section">
                                <h3 className="section-title">Mô tả công việc</h3>
                                <div
                                    className="section-content"
                                    dangerouslySetInnerHTML={{ __html: data.description }}
                                />
                            </div>
                        )}

                        {data.requirements && (
                            <div className="job-detail-section">
                                <h3 className="section-title">Yêu cầu công việc</h3>
                                <div
                                    className="section-content"
                                    dangerouslySetInnerHTML={{ __html: data.requirements }}
                                />
                            </div>
                        )}

                        {data.benefits && (
                            <div className="job-detail-section">
                                <h3 className="section-title">Quyền lợi được hưởng</h3>
                                <div
                                    className="section-content"
                                    dangerouslySetInnerHTML={{ __html: data.benefits }}
                                />
                            </div>
                        )}

                        {data.contactInfo && (
                            <div className="job-detail-section">
                                <h3 className="section-title">Thông tin liên hệ</h3>
                                <div
                                    className="section-content"
                                    dangerouslySetInnerHTML={{ __html: data.contactInfo }}
                                />
                            </div>
                        )}

                        <div className="job-detail-actions">
                            <button className="back-button" onClick={handleBack}>
                                <FaArrowLeft /> Quay lại
                            </button>
                            <a
                                href={data.link}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="source-button"
                            >
                                Xem bài gốc
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </PageContainer>
    );
};

export default JobDetail;
