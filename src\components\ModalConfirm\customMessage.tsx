import React from 'react';
import CustomModal from './ModalConfirm';
import { createRoot } from 'react-dom/client';

const customMessage = {
    success: (content: string) => showModal('success', 'Thành công', content),
    error: (content: string) => showModal('error', 'Lỗi', content),
    warning: (content: string) => showModal('warning', 'Cảnh báo', content),
    info: (content: string) => showModal('info', 'Thông báo', content),
};

const showModal = (type: 'success' | 'error' | 'warning' | 'info', title: string, content: string) => {
    const div = document.createElement('div');
    document.body.appendChild(div);

    const root = createRoot(div);

    const destroy = () => {
        root.unmount();
        div.remove();
    };

    root.render(
        <CustomModal
            type={type}
            title={title}
            content={content}
            open={true}
            onOk={destroy}
            onCancel={destroy}
        />
    );
};

export default customMessage;
