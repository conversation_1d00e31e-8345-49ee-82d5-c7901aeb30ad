// React core
import React from 'react';
import { createRoot } from 'react-dom/client';

// Tailwind stylesheet
import 'css/tailwind.scss';

// ZaUI stylesheet
import 'zmp-ui/zaui.css';

// Your stylesheet
import 'css/app.scss';

//swiper
import 'swiper/css';
import 'swiper/css/pagination';
// import 'react-lazy-load-image-component/src/effects/blur.css';
import 'react-loading-skeleton/dist/skeleton.css';
// Expose app configuration
import appConfig from '../app-config.json';
const extendedAppConfig = {
    ...appConfig,
    pages: [],
};

if (!window.APP_CONFIG) {
    window.APP_CONFIG = extendedAppConfig;
}

// Mount the app
import App from 'components/app';
const root = createRoot(document.getElementById('app')!);
root.render(React.createElement(App));
