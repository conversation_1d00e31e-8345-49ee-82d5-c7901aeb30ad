.member-card {
    padding: 20px;
    background-color: #fff;
    max-height: 100%;
    overflow-y: scroll;
    &__header {
        text-align: center;
        margin-bottom: 15px;

        .title {
            font-size: 18px;
            font-weight: 500;
        }
    }

    &__list {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
        gap: 32px;
        max-width: 1400px;
        margin: 0 auto;
        padding-bottom: 100px;
    }
}

.card-item {
    position: relative;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    cursor: pointer;

    &:hover {
        // transform: translateY(-8px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    &__decoration {
        position: absolute;
        inset: 0;
        overflow: hidden;
        z-index: 1;

        .circle {
            position: absolute;
            border-radius: 50%;

            &-1 {
                width: 300px;
                height: 300px;
                background: linear-gradient(135deg, #3b82f6, #2563eb);
                top: -150px;
                right: -150px;
                opacity: 0.1;
            }

            &-2 {
                width: 200px;
                height: 200px;
                background: linear-gradient(135deg, #7c3aed, #4f46e5);
                bottom: -100px;
                left: -100px;
                opacity: 0.1;
            }
        }
    }

    &__content {
        position: relative;
        z-index: 2;
        padding: 15px;
    }

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .card-type {
            display: flex;
            align-items: center;
            gap: 12px;

            .icon {
                font-size: 24px;
            }

            .name {
                font-size: 20px;
                font-weight: 700;
                color: #1e293b;
            }
        }

        .status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;

            &.active {
                background: #dcfce7;
                color: #166534;
            }

            &.inactive {
                background: #fee2e2;
                color: #991b1b;
            }
        }
    }

    .card-balance {
        background: linear-gradient(135deg, #1e293b, #0f172a);
        border-radius: 20px;
        padding: 24px;
        color: white;
        margin-bottom: 12px;

        .available {
            text-align: center;
            margin-bottom: 20px;

            .label {
                display: block;
                font-size: 14px;
                opacity: 0.8;
                margin-bottom: 8px;
            }

            .amount {
                font-size: 32px;
                font-weight: 700;
                background: linear-gradient(135deg, #38bdf8, #818cf8);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }
        }

        .progress-bar {
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            margin-bottom: 16px;

            .progress {
                height: 100%;
                background: linear-gradient(to right, #38bdf8, #818cf8);
                border-radius: 4px;
                transition: width 0.3s ease;
            }
        }

        .balance-details {
            display: flex;
            justify-content: space-between;

            .detail-item {
                .label {
                    display: block;
                    font-size: 12px;
                    opacity: 0.8;
                    margin-bottom: 4px;
                }

                .value {
                    font-weight: 600;
                    font-size: 14px;
                }

                &.used .value {
                    color: #fca5a5;
                }
            }
        }
    }

    .card-info {
        .info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;

            .info-item {
                .label {
                    display: block;
                    font-size: 12px;
                    color: #64748b;
                    margin-bottom: 4px;
                }

                .value {
                    font-size: 14px;
                    font-weight: 600;
                    color: #1e293b;
                }
            }
        }
    }

    &.expanded {
        grid-column: 1 / -1;
        transform: none !important;

        .card-history {
            padding-top: 12px;
        }
    }

    .card-history {
        background: white;
        border-radius: 20px;
        padding: 12px;

        .history-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;

            span {
                font-size: 18px;
                font-weight: 700;
                background: linear-gradient(135deg, #2563eb, #7c3aed);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }

            .close-btn {
                width: 36px;
                height: 36px;
                border-radius: 50%;
                border: none;
                background: #f1f5f9;
                color: #64748b;
                font-size: 24px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        .history-loading {
            text-align: center;
            padding: 40px;
            color: #64748b;

            .loading-spinner {
                width: 40px;
                height: 40px;
                border: 3px solid #e2e8f0;
                border-top-color: #2563eb;
                border-radius: 50%;
                margin: 0 auto 16px;
                animation: spin 1s linear infinite;
            }
        }

        .history-empty {
            text-align: center;
            padding: 40px;
            color: #64748b;
            .icon {
                font-size: 48px;
                display: block;
                margin-bottom: 16px;
            }

            .message {
                font-size: 16px;
            }
        }

        .history-list {
            max-height: 500px;
            overflow-y: auto;
            padding-right: 16px;

            &::-webkit-scrollbar {
                width: 6px;
            }

            &::-webkit-scrollbar-track {
                background: #f1f5f9;
                border-radius: 3px;
            }

            &::-webkit-scrollbar-thumb {
                background: #cbd5e1;
                border-radius: 3px;
            }
        }

        .history-item {
            display: flex;
            justify-content: space-between;
            padding: 16px;
            border-radius: 16px;
            background: #f8fafc;
            margin-bottom: 12px;
            transition: all 0.2s ease;

            &:hover {
                background: #f1f5f9;
                transform: translateX(4px);
            }

            .transaction-info {
                .main-info {
                    margin-bottom: 8px;

                    .type {
                        font-size: 16px;
                        font-weight: 600;
                        color: #1e293b;
                        display: block;
                    }

                    .reference {
                        font-size: 13px;
                        color: #64748b;
                    }
                }

                .sub-info {
                    display: flex;
                    gap: 16px;
                    font-size: 13px;
                    color: #64748b;

                    .time,
                    .branch {
                        display: flex;
                        align-items: center;
                        gap: 4px;
                    }
                }
            }

            .transaction-amount {
                text-align: right;

                .amount {
                    display: block;
                    font-size: 18px;
                    font-weight: 700;
                    margin-bottom: 4px;

                    &.debit {
                        color: #ef4444;
                    }

                    &.credit {
                        color: #fca5a5;
                    }
                }

                .discount {
                    font-size: 13px;
                    color: #f59e0b;
                    display: block;
                }
            }
        }
    }
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

// Loading Skeleton
.loading-skeleton {
    height: 480px;
    background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.8) 50%,
        rgba(255, 255, 255, 0) 100%
    );
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 24px;
}

// Empty State
.empty-state {
    text-align: center;
    padding: 64px 24px;

    &__icon {
        font-size: 64px;
        margin-bottom: 24px;
    }

    p {
        font-size: 18px;
        color: #64748b;
    }
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

// Responsive
@media (max-width: 768px) {
    .member-card {
        padding: 16px;

        // &__header {
        //     .title {
        //         font-size: 28px;
        //     }
        // }

        &__list {
            grid-template-columns: 1fr;
        }
    }

    .card-item {
        .card-balance {
            .available .amount {
                font-size: 28px;
            }
        }

        &.expanded {
            .card-history {
                .history-list {
                    max-height: 300px;
                }
            }
        }
    }
}
