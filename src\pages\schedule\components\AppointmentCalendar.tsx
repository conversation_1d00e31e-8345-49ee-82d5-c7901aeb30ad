import React, { useState } from 'react';
import { useRecoilValue } from 'recoil';
import { sortedAppointmentsState } from 'state/appointmentState';
import { AppointmentStatus } from 'types/appointment.type';
import { FaChevronLeft, FaChevronRight, FaPlus } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import PAGE_URL from 'constants/PAGE_URL';
import './AppointmentCalendar.scss';

const DAYS_OF_WEEK = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'];
const MONTHS = [
    'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
    'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
];

const AppointmentCalendar: React.FC = () => {
    const navigate = useNavigate();
    const appointments = useRecoilValue(sortedAppointmentsState);
    const [currentDate, setCurrentDate] = useState(new Date());
    
    const getDaysInMonth = (year: number, month: number) => {
        return new Date(year, month + 1, 0).getDate();
    };
    
    const getFirstDayOfMonth = (year: number, month: number) => {
        return new Date(year, month, 1).getDay();
    };
    
    const goToPreviousMonth = () => {
        setCurrentDate(prev => {
            const newDate = new Date(prev);
            newDate.setMonth(prev.getMonth() - 1);
            return newDate;
        });
    };
    
    const goToNextMonth = () => {
        setCurrentDate(prev => {
            const newDate = new Date(prev);
            newDate.setMonth(prev.getMonth() + 1);
            return newDate;
        });
    };
    
    const handleAddAppointment = () => {
        navigate(`${PAGE_URL.SCHEDULE}/new`);
    };
    
    const renderCalendar = () => {
        const year = currentDate.getFullYear();
        const month = currentDate.getMonth();
        const daysInMonth = getDaysInMonth(year, month);
        const firstDayOfMonth = getFirstDayOfMonth(year, month);
        
        const days = [];
        
        // Add empty cells for days before the first day of the month
        for (let i = 0; i < firstDayOfMonth; i++) {
            days.push(<div key={`empty-${i}`} className="calendar-day empty"></div>);
        }
        
        // Add cells for each day of the month
        for (let day = 1; day <= daysInMonth; day++) {
            const date = new Date(year, month, day);
            const dateString = date.toISOString().split('T')[0];
            
            // Check if there are appointments on this day
            const appointmentsOnDay = appointments.filter(appointment => appointment.date === dateString);
            
            const isToday = date.toDateString() === new Date().toDateString();
            
            days.push(
                <div 
                    key={`day-${day}`} 
                    className={`calendar-day ${isToday ? 'today' : ''} ${appointmentsOnDay.length > 0 ? 'has-appointments' : ''}`}
                    onClick={() => appointmentsOnDay.length > 0 && navigate(`${PAGE_URL.SCHEDULE}?date=${dateString}`)}
                >
                    <span className="day-number">{day}</span>
                    {appointmentsOnDay.length > 0 && (
                        <div className="appointment-indicators">
                            {appointmentsOnDay.map((appointment, index) => (
                                <span 
                                    key={index} 
                                    className={`appointment-indicator ${appointment.status === AppointmentStatus.CANCELLED ? 'cancelled' : ''}`}
                                ></span>
                            ))}
                        </div>
                    )}
                </div>
            );
        }
        
        return days;
    };
    
    return (
        <div className="appointment-calendar">
            <div className="calendar-header">
                <button className="calendar-nav-button" onClick={goToPreviousMonth}>
                    <FaChevronLeft />
                </button>
                <h2 className="calendar-title">
                    {MONTHS[currentDate.getMonth()]} {currentDate.getFullYear()}
                </h2>
                <button className="calendar-nav-button" onClick={goToNextMonth}>
                    <FaChevronRight />
                </button>
            </div>
            
            <div className="calendar-days-header">
                {DAYS_OF_WEEK.map(day => (
                    <div key={day} className="calendar-day-name">{day}</div>
                ))}
            </div>
            
            <div className="calendar-grid">
                {renderCalendar()}
            </div>
            
            <button className="add-appointment-button" onClick={handleAddAppointment}>
                <FaPlus />
            </button>
        </div>
    );
};

export default AppointmentCalendar;
