.schedule-container {
    background-color: #f5f7fa;
}

.schedule-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, darken(#1976d2, 15%) 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .zaui-header-title {
        color: white;
        font-weight: 600;
        font-size: 17px;
        letter-spacing: 0.3px;
    }
}

.schedule-page {
    padding: 12px 0;
    background-color: #f5f7fa;
}

.schedule-intro {
    background-color: white;
    margin: 16px 16px 0;
    padding: 20px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100px;
        height: 100px;
        background: radial-gradient(
            circle,
            rgba(var(--primary-color-rgb), 0.1) 0%,
            rgba(var(--primary-color-rgb), 0) 70%
        );
        border-radius: 50%;
        transform: translate(30%, -30%);
    }

    &__content {
        position: relative;
        z-index: 1;
    }

    &__title {
        font-size: 20px;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 12px;
        position: relative;
        display: inline-block;

        &::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 40px;
            height: 3px;
            background-color: var(--primary-color);
            border-radius: 2px;
        }
    }

    &__description {
        font-size: 14px;
        color: #666;
        line-height: 1.6;
        margin-bottom: 20px;
    }
}

.schedule-tabs {
    display: flex;
    background-color: #f0f2f5;
    border-radius: 10px;
    padding: 3px;
    margin: 8px 12px 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.schedule-tab {
    flex: 1;
    padding: 10px 0;
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    background: none;
    border: none;
    border-radius: 8px;
    transition: all 0.2s ease;

    &.active {
        background-color: white;
        color: var(--primary-color);
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        font-weight: 600;
    }

    &:active {
        transform: scale(0.98);
    }
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px 20px;
    text-align: center;
    background-color: white;
    border-radius: 10px;
    margin: 16px 12px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);

    &__icon {
        font-size: 50px;
        color: #ddd;
        margin-bottom: 12px;
    }

    &__text {
        font-size: 14px;
        color: #777;
        line-height: 1.4;
        max-width: 250px;
    }
}

.appointment-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 0 12px;
}

.appointment-item {
    display: flex;
    flex-direction: column;
    background: white;
    border-radius: 10px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
    border: none;
    transition: all 0.2s ease;
    margin-bottom: 12px;
    cursor: pointer;

    &:active {
        transform: scale(0.98);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    }

    &__header {
        display: flex;
        padding: 10px;
        background: linear-gradient(135deg, var(--primary-color) 0%, darken(#1976d2, 10%) 100%);
        color: white;
        border-radius: 10px 10px 0 0;
    }

    &__date-badge {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-width: 55px;
        height: 45px;
        background-color: rgba(255, 255, 255, 0.15);
        color: white;
        border-radius: 6px;
        margin-right: 12px;
    }

    &__day {
        font-size: 20px;
        font-weight: 700;
        line-height: 1;
    }

    &__month {
        font-size: 11px;
        margin-top: 3px;
        opacity: 0.9;
    }

    &__header-content {
        flex: 1;
    }

    &__title {
        font-size: 15px;
        font-weight: 600;
        color: white;
        margin-bottom: 6px;
        line-height: 1.3;
    }

    &__time {
        display: flex;
        align-items: center;
    }

    &__time-icon {
        color: rgba(255, 255, 255, 0.8);
        font-size: 14px;
        margin-right: 6px;
    }

    &__time-text {
        font-size: 13px;
        font-weight: 500;
        color: white;
        opacity: 0.9;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 200px;
    }

    &__weekday {
        font-size: 13px;
        color: rgba(255, 255, 255, 0.8);
        margin-left: 8px;
    }

    &__body {
        padding: 10px;
        background-color: #fcfcfc;
    }

    &__location {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        background-color: #f5f5f5;
        border-radius: 6px;
        padding: 8px 10px;
        font-size: 13px;
        color: #555;
    }

    &__location-icon {
        font-size: 14px;
        color: #666;
        margin-right: 10px;
    }

    &__description {
        font-size: 13px;
        color: #666;
        margin-bottom: 10px;
        line-height: 1.4;
        padding: 0 2px;
    }

    &__notes-container {
        background-color: rgba(var(--primary-color-rgb), 0.05);
        border-radius: 6px;
        padding: 8px 10px;
        position: relative;
        overflow: hidden;
        border-left: 2px solid var(--primary-color);
        margin-bottom: 8px;
    }

    &__notes {
        font-size: 12px;
        color: var(--primary-color);
        font-weight: 600;
        margin: 0;
        line-height: 1.4;
    }
}
