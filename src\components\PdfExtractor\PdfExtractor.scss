.pdf-extractor {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  
  h2 {
    margin-bottom: 20px;
    color: #333;
  }
  
  .file-upload {
    margin-bottom: 20px;
    padding: 15px;
    border: 2px dashed #ccc;
    border-radius: 5px;
    
    input[type="file"] {
      display: block;
      margin-bottom: 10px;
    }
    
    p {
      margin: 5px 0;
      font-size: 14px;
      color: #666;
    }
  }
  
  .actions {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    
    button {
      padding: 8px 16px;
      background-color: #4caf50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      
      &:disabled {
        background-color: #cccccc;
        cursor: not-allowed;
      }
      
      &:hover:not(:disabled) {
        background-color: #45a049;
      }
      
      &:nth-child(2) {
        background-color: #f44336;
        
        &:hover {
          background-color: #d32f2f;
        }
      }
    }
  }
  
  .error {
    color: #f44336;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #ffebee;
    border-radius: 4px;
  }
  
  .extracted-fields {
    margin-top: 20px;
    
    h3 {
      margin-bottom: 10px;
      color: #333;
    }
    
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
      
      th, td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #ddd;
      }
      
      th {
        background-color: #f5f5f5;
        font-weight: bold;
      }
      
      tr:hover {
        background-color: #f9f9f9;
      }
    }
  }
  
  .extracted-text {
    margin-top: 20px;
    
    h3 {
      margin-bottom: 10px;
      color: #333;
    }
    
    pre {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 4px;
      white-space: pre-wrap;
      word-wrap: break-word;
      max-height: 300px;
      overflow-y: auto;
      font-size: 14px;
      line-height: 1.5;
    }
  }
}
