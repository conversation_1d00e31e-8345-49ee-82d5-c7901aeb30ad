# Ứng dụng cào dữ liệu từ thongtinvieclamkhanhhoa.vn

Ứng dụng này cung cấp giải pháp cào dữ liệu từ trang web thongtinvieclamkhanhhoa.vn mà không bị ảnh hưởng bởi CORS hoặc country blocking.

## Cài đặt

1. Clone repository này
2. Cài đặt các dependencies cho ứng dụng frontend:
    ```
    npm install
    ```
3. Cài đặt các dependencies cho server proxy backend:
    ```
    cd backend
    npm install
    ```

## Chạy ứng dụng

### Bước 1: Khởi động server proxy backend

```
cd backend
npm start
```

Hoặc sử dụng chế độ phát triển (tự động khởi động lại khi có thay đổi):

```
cd backend
npm run dev
```

Server proxy sẽ chạy tại http://localhost:3001

### Bước 2: Khởi động ứng dụng frontend

```
npm run dev
```

Ứng dụng sẽ chạy tại http://localhost:5173 (hoặc cổng khác nếu 5173 đã được sử dụng)

## Cách hoạt động

Ứng dụng sử dụng nhiều phương pháp để tránh bị chặn khi cào dữ liệu:

1. **Server proxy tự host**: Một server Express đơn giản chạy trên máy local để chuyển tiếp các request đến trang web đích.
2. **Nhiều proxy công cộng**: Nếu server proxy local không hoạt động, ứng dụng sẽ thử sử dụng các proxy công cộng khác nhau.
3. **User-Agent và Headers tùy chỉnh**: Sử dụng các headers giống với trình duyệt thông thường để tránh bị phát hiện là bot.

## Cấu trúc dự án

### Frontend

-   `src/hooks/useCrawlData.ts`: Hook để cào dữ liệu từ trang chủ
-   `src/hooks/useCrawlNewsDetail.ts`: Hook để cào dữ liệu chi tiết từ trang tin tức
-   `src/hooks/useServerProxy.ts`: Hook để giao tiếp với server proxy backend
-   `src/pages/home/<USER>/StaticData.tsx`: Component hiển thị dữ liệu đã cào
-   `src/pages/news/NewsDetail.tsx`: Trang hiển thị chi tiết tin tức

### Backend

-   `backend/server.js`: Server proxy Express
-   `backend/package.json`: Cấu hình và dependencies cho backend
-   `backend/README.md`: Hướng dẫn sử dụng backend

## Lưu ý

-   Nếu gặp lỗi "country blocked", hãy đảm bảo server proxy backend đang chạy
-   Để khởi động server proxy backend, hãy chạy lệnh `cd backend && npm start`
-   Nếu tất cả các proxy đều không hoạt động, bạn có thể thêm các proxy khác vào danh sách trong các file `useCrawlData.ts` và `useCrawlNewsDetail.ts`
-   Việc cào dữ liệu từ các trang web nên tuân thủ các quy định và điều khoản sử dụng của trang web đó
