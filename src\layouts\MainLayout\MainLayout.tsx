import ZNSNotification from 'components/ZNSNotification/ZNSNotification';
import PAGE_URL from 'constants/PAGE_URL';
import { useAppointments } from 'hooks/useAppointments';
import React from 'react';
import { AiFillHome } from 'react-icons/ai';
import { FaBriefcase } from 'react-icons/fa';
import { FaCalendarDays } from 'react-icons/fa6';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { useRecoilValue } from 'recoil';
import { isMemberState, userNumberState } from 'state';
import './MainLayout.scss';

const menuItems = [
    {
        icon: <AiFillHome size={22} />,
        label: 'Trang chủ',
        path: PAGE_URL.HOME,
    },
    {
        icon: <FaCalendarDays size={22} />,
        label: 'Lịch hẹn của tôi',
        path: PAGE_URL.SCHEDULE,
    },
    {
        icon: <FaBriefcase size={22} />,
        label: '<PERSON>i<PERSON><PERSON> làm của tôi',
        path: PAGE_URL.MY_JOB_REGISTRATIONS,
    },
    // {
    //     icon: <FaFilePdf />,
    //     label: 'Trích xuất PDF',
    //     path: PAGE_URL.PDF_EXTRACTOR,
    // },
    // {
    //     icon: <FaFilePdf />,
    //     label: 'Đọc PDF đơn giản',
    //     path: PAGE_URL.SIMPLE_PDF_READER,
    // },
    // {
    //     icon: <FaFilePdf />,
    //     label: 'PDF Parse',
    //     path: PAGE_URL.PDF_PARSE_READER,
    // },
    // {
    //     icon: <IoQrCode />,
    //     label: 'QR',
    //     path: PAGE_URL.QR,
    // },
    // {
    //     icon: <FaRegAddressCard />,
    //     label: 'Card',
    //     path: PAGE_URL.MEMBER_CARD,
    // },
    // {
    //     icon: <FaRegUser />,
    //     label: 'Tôi',
    //     path: PAGE_URL.ME,
    // },
];

function MainLayout() {
    const location = useLocation();
    const userNumber = useRecoilValue(userNumberState);
    const navigate = useNavigate();
    const isMember = useRecoilValue(isMemberState);

    useAppointments();

    return (
        <div className={`main-layout`}>
            <Outlet />
            {userNumber && isMember ? (
                <div className="menu">
                    {menuItems.map((item, i) => (
                        <div
                            className={`menu__item ${location.pathname === item.path ? 'active' : ''}`}
                            key={i}
                            onClick={() => navigate(item.path)}
                        >
                            <span className="menu__item-icon">{item.icon}</span>
                            <span className="menu__item-label">{item.label}</span>
                        </div>
                    ))}
                </div>
            ) : // <BottomNavigation activeKey={location.pathname} defaultActiveKey="/" fixed>
            //     {menuItems.map((item, i) => (
            //         <BottomNavigation.Item
            //             onClick={() => navigate(item.path)}
            //             key={item.path}
            //             itemKey={item.path}
            //             icon={item.icon}
            //             label={item.label}
            //         ></BottomNavigation.Item>
            //     ))}
            // </BottomNavigation>
            null}

            {/* ZNS Notification - thay thế appointment notification cũ */}
            <ZNSNotification />
        </div>
    );
}

export default MainLayout;

{
    /* <BottomNavigation.Item
    className={`menu__item ${location.pathname === item.path ? 'active' : ''}`}
    key={i}
    onClick={() => navigate(item.path)}
>
    <span className="menu__item-icon">{item.icon}</span>
    <span className="menu__item-label">{item.label}</span>
</BottomNavigation.Item>; */
}
