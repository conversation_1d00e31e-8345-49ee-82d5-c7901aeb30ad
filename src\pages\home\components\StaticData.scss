.job-data-container {
    background-color: #ffffff;
    padding: 0;

    @media (max-width: 480px) {
        padding: 0;
    }

    .loading-section,
    .error-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 16px;
        text-align: center;
        background-color: #fff;
        border-radius: 16px;
        margin: 16px 0;

        p {
            margin-top: 16px;
            color: #666;
            font-size: 15px;
        }
    }

    .loading-spinner {
        width: 36px;
        height: 36px;
        border: 3px solid rgba(33, 57, 130, 0.1);
        border-radius: 50%;
        border-top-color: #213982;
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to {
            transform: rotate(360deg);
        }
    }
}

.static-data {
    margin-bottom: 20px;
    padding-top: 8px;
}

// Section Container
.section-container {
    margin-bottom: 28px;
    position: relative;
    z-index: 1;
    padding: 16px 0;

    @media (max-width: 480px) {
        margin-bottom: 24px;
        padding: 12px 0;
    }
}

// Section Header
.section-header {
    margin-bottom: 20px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;

    @media (max-width: 480px) {
        margin-bottom: 16px;
    }

    .section-title {
        font-size: 18px;
        font-weight: 600;
        color: #213982;
        margin: 0;
        position: relative;
        padding-left: 12px;

        @media (max-width: 480px) {
            font-size: 17px;
        }

        &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 18px;
            background-color: #213982;
            border-radius: 2px;

            @media (max-width: 480px) {
                height: 16px;
            }
        }
    }

    .section-line {
        flex: 1;
        height: 1px;
        background-color: #eee;
        margin-left: 15px;

        @media (max-width: 480px) {
            margin-left: 10px;
        }
    }
}

// Job Cards
.job-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;

    @media (min-width: 768px) {
        grid-template-columns: repeat(2, 1fr);
    }

    @media (min-width: 992px) {
        grid-template-columns: repeat(3, 1fr);
    }
}

.job-card {
    background-color: #fff;
    border-radius: 16px;
    overflow: hidden;
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;

    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
    &:hover {
        transform: translateY(-3px);
        border-color: transparent;
    }

    .job-card-image {
        height: 140px;
        position: relative;
        overflow: hidden;

        @media (max-width: 480px) {
            height: 160px;
        }

        img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            transition: transform 0.3s;
        }

        &:hover img {
            transform: scale(1.05);
        }

        .image-placeholder {
            width: 100%;
            height: 100%;
            background: #f5f5f5;
        }

        .job-company-badge {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
            color: white;
            padding: 20px 12px 8px;
            font-size: 13px;
            font-weight: 500;
            text-align: left;
        }
    }

    .job-card-content {
        padding: 16px;
        flex: 1;
        display: flex;
        flex-direction: column;

        @media (max-width: 480px) {
            padding: 16px;
        }
    }

    .job-title {
        font-size: 16px;
        font-weight: 600;
        color: #213982;
        margin-bottom: 12px;
        line-height: 1.4;
        height: 44px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;

        @media (max-width: 480px) {
            font-size: 16px;
            height: auto;
            max-height: 46px;
            margin-bottom: 12px;
        }
    }

    .job-details {
        margin-bottom: 15px;
        flex: 1;

        @media (max-width: 480px) {
            margin-bottom: 16px;
        }

        .job-detail-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 14px;
            color: #666;

            @media (max-width: 480px) {
                font-size: 14px;
                margin-bottom: 8px;
            }

            .job-detail-icon {
                color: #213982;
                margin-right: 8px;
                font-size: 14px;
                min-width: 16px;
                opacity: 0.8;

                @media (max-width: 480px) {
                    font-size: 14px;
                    min-width: 16px;
                }
            }

            &.salary {
                color: #e53935;
                font-weight: 500;
            }
        }
    }

    .job-card-action {
        margin-top: auto;
        text-align: center;

        .view-job-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 14px;
            font-weight: 500;
            padding: 10px 16px;
            border-radius: 8px;
            background-color: #213982;
            transition: all 0.3s ease;
            width: 100%;

            .arrow-icon {
                margin-left: 6px;
                font-size: 12px;
                transition: transform 0.3s;
            }

            &:hover {
                background-color: #162a61;

                .arrow-icon {
                    transform: translateX(3px);
                }
            }
        }
    }
}

// News Cards
.news-grid,
.activities-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;

    @media (min-width: 768px) {
        grid-template-columns: repeat(2, 1fr);
    }

    @media (min-width: 992px) {
        grid-template-columns: repeat(3, 1fr);
    }

    @media (min-width: 1200px) {
        grid-template-columns: repeat(4, 1fr);
    }
}

.news-card,
.activity-card {
    background-color: #fff;
    border-radius: 16px;
    overflow: hidden;
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;

    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
    &:hover {
        transform: translateY(-3px);
        border-color: transparent;
    }

    .news-card-image,
    .activity-card-image {
        height: 160px;
        position: relative;
        overflow: hidden;

        @media (max-width: 480px) {
            height: 180px;
        }

        img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            transition: transform 0.3s;
        }

        &:hover img {
            transform: scale(1.05);
        }

        .image-placeholder {
            width: 100%;
            height: 100%;
            background: #f5f5f5;
        }

        &:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 60px;
            background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
            pointer-events: none;
        }
    }

    .activity-overlay {
        position: absolute;
        top: 10px;
        right: 10px;
        background-color: rgba(33, 57, 130, 0.8);
        color: white;
        width: 34px;
        height: 34px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2;

        @media (max-width: 480px) {
            width: 36px;
            height: 36px;
        }

        .activity-icon {
            font-size: 14px;
        }
    }

    .news-card-content,
    .activity-card-content {
        padding: 16px;
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .news-title,
    .activity-title {
        font-size: 15px;
        font-weight: 600;
        color: #213982;
        margin-bottom: 10px;
        line-height: 1.4;
        height: 42px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;

        @media (max-width: 480px) {
            font-size: 15px;
            height: auto;
            max-height: 44px;
            margin-bottom: 10px;
        }
    }

    .news-date,
    .activity-date {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        font-size: 13px;
        color: #666;

        .news-date-icon,
        .activity-date-icon {
            color: #213982;
            margin-right: 8px;
            font-size: 12px;
            opacity: 0.8;
        }
    }

    .news-card-action,
    .activity-card-action {
        margin-top: auto;
        text-align: center;

        .view-news-btn,
        .view-activity-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 13px;
            font-weight: 500;
            padding: 8px 14px;
            border-radius: 8px;
            background-color: #213982;
            transition: all 0.3s ease;
            width: 100%;

            .arrow-icon {
                margin-left: 5px;
                font-size: 11px;
                transition: transform 0.3s;
            }

            &:hover {
                background-color: #162a61;

                .arrow-icon {
                    transform: translateX(3px);
                }
            }
        }
    }
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

@keyframes gradientMove {
    0% {
        background-position: 100% 0;
    }
    100% {
        background-position: -100% 0;
    }
}
