import React from 'react';
import { Input } from 'zmp-ui';
import { UseFormRegister } from 'react-hook-form';

interface FormInputProps {
    label: string;
    placeholder: string;
    name: string;
    register: UseFormRegister<any>;
    error?: string;
    required?: boolean;
    readOnly?: boolean;
    onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const FormInput = ({
    label,
    placeholder,
    name,
    register,
    error,
    required,
    readOnly,
    onChange,
}: FormInputProps) => {
    return (
        <div className={`form-input ${error ? 'form-input-error' : ''}`}>
            <div className="form-input__field">
                <label>
                    {label}
                    {required && <span className="form-input__field-required">*</span>}
                </label>
                <input
                    readOnly={readOnly}
                    {...register(name)}
                    placeholder={placeholder}
                    onChange={(e) => {
                        register(name).onChange(e);
                        if (onChange) onChange(e);
                    }}
                />
            </div>
            {error && <p className="form-error">{error}</p>}
        </div>
    );
};

export default FormInput;
