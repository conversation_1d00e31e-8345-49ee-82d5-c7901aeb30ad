import React from 'react';
import { PhieuKetQua } from 'apis/getPhieuKetQuaApi';
import {
    FaMapMarkerAlt,
    FaClock,
    FaIdCard,
    FaCalendarAlt,
    FaMoneyBillWave,
    FaUniversity,
} from 'react-icons/fa';
import './PhieuKetQuaItem.scss';
import { formatCurrency } from 'utils/common';

interface PhieuKetQuaItemProps {
    phieuKetQua: PhieuKetQua;
}

const PhieuKetQuaItem: React.FC<PhieuKetQuaItemProps> = ({ phieuKetQua }) => {
    // Hàm định dạng ngày tháng
    const formatDate = (dateString: string) => {
        try {
            if (!dateString) return '';

            // Xử lý trường hợp ngày tháng có định dạng dd-MM-yyyy
            if (dateString.includes('-') && dateString.split('-').length === 3) {
                const parts = dateString.split('-');
                if (parts[0].length === 2 && parts[1].length === 2 && parts[2].length === 4) {
                    return dateString; // Đã đúng định dạng dd-MM-yyyy
                }
            }

            // Xử lý trường hợp ngày tháng có định dạng ISO
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return dateString;

            return date.toLocaleDateString('vi-VN', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
            });
        } catch (error) {
            console.error('Lỗi khi định dạng ngày tháng:', error);
            return dateString;
        }
    };

    return (
        <div className="phieu-ket-qua-item">
            <div className="phieu-ket-qua-item__header">
                <h3 className="phieu-ket-qua-item__title">Thông tin trợ cấp thất nghiệp</h3>
            </div>

            <div className="phieu-ket-qua-item__body">
                <div className="phieu-ket-qua-item__info">
                    <FaIdCard className="phieu-ket-qua-item__icon" />
                    <div className="phieu-ket-qua-item__info-content">
                        <span className="phieu-ket-qua-item__label">Họ tên:</span>
                        <span className="phieu-ket-qua-item__value">{phieuKetQua.hoTen}</span>
                    </div>
                </div>

                <div className="phieu-ket-qua-item__info">
                    <FaIdCard className="phieu-ket-qua-item__icon" />
                    <div className="phieu-ket-qua-item__info-content">
                        <span className="phieu-ket-qua-item__label">Mã định danh:</span>
                        <span className="phieu-ket-qua-item__value">{phieuKetQua.maDinhDanh}</span>
                    </div>
                </div>

                <div className="phieu-ket-qua-item__info">
                    <FaCalendarAlt className="phieu-ket-qua-item__icon" />
                    <div className="phieu-ket-qua-item__info-content">
                        <span className="phieu-ket-qua-item__label">Ngày sinh:</span>
                        <span className="phieu-ket-qua-item__value">{formatDate(phieuKetQua.ngaySinh)}</span>
                    </div>
                </div>

                <div className="phieu-ket-qua-item__info">
                    <FaMapMarkerAlt className="phieu-ket-qua-item__icon" />
                    <div className="phieu-ket-qua-item__info-content">
                        <span className="phieu-ket-qua-item__label">Địa chỉ:</span>
                        <span className="phieu-ket-qua-item__value">{phieuKetQua.diaChi}</span>
                    </div>
                </div>

                <div className="phieu-ket-qua-item__info">
                    <FaUniversity className="phieu-ket-qua-item__icon" />
                    <div className="phieu-ket-qua-item__info-content">
                        <span className="phieu-ket-qua-item__label">Tài khoản:</span>
                        <span className="phieu-ket-qua-item__value">{phieuKetQua.taiKhoan}</span>
                    </div>
                </div>

                <div className="phieu-ket-qua-item__info">
                    <FaUniversity className="phieu-ket-qua-item__icon" />
                    <div className="phieu-ket-qua-item__info-content">
                        <span className="phieu-ket-qua-item__label">Ngân hàng:</span>
                        <span className="phieu-ket-qua-item__value">{phieuKetQua.nganHang}</span>
                    </div>
                </div>

                <div className="phieu-ket-qua-item__info">
                    <FaMoneyBillWave className="phieu-ket-qua-item__icon" />
                    <div className="phieu-ket-qua-item__info-content">
                        <span className="phieu-ket-qua-item__label">Mức trợ cấp hàng tháng:</span>
                        <span className="phieu-ket-qua-item__value">
                            {phieuKetQua.mucTroCapHangThang
                                ? formatCurrency(phieuKetQua.mucTroCapHangThang)
                                : 'Chưa xác định'}
                        </span>
                    </div>
                </div>

                <div className="phieu-ket-qua-item__info">
                    <FaClock className="phieu-ket-qua-item__icon" />
                    <div className="phieu-ket-qua-item__info-content">
                        <span className="phieu-ket-qua-item__label">Thời gian hưởng:</span>
                        <span className="phieu-ket-qua-item__value">
                            {formatDate(phieuKetQua.thoiGianBatDau)} -{' '}
                            {formatDate(phieuKetQua.thoiGianKetThuc)}
                        </span>
                    </div>
                </div>

                <div className="phieu-ket-qua-item__info">
                    <FaClock className="phieu-ket-qua-item__icon" />
                    <div className="phieu-ket-qua-item__info-content">
                        <span className="phieu-ket-qua-item__label">Số tháng được hưởng:</span>
                        <span className="phieu-ket-qua-item__value">
                            {phieuKetQua.soThangDuocHuongTroCap
                                ? phieuKetQua.soThangDuocHuongTroCap + ' tháng'
                                : 'Chưa xác định'}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default PhieuKetQuaItem;
