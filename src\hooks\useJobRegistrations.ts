import { useState, useEffect } from 'react';
import { useRecoilValue } from 'recoil';
import { isMemberState, userInfoState } from 'state';
import { jobRegistrationApi, JobRegistrationListItem } from 'apis/jobRegistrationApi';

export const useJobRegistrations = () => {
    const [registrations, setRegistrations] = useState<JobRegistrationListItem[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const userInfo = useRecoilValue(userInfoState);
    const isMember = useRecoilValue(isMemberState);

    const fetchRegistrations = async () => {
        if (!isMember || !userInfo) {
            setIsLoading(false);
            return;
        }

        const userMaDinhDanh = userInfo.maDinhDanh || userInfo.cccd || '';
        if (!userMaDinhDanh) {
            setIsLoading(false);
            return;
        }

        try {
            setIsLoading(true);
            setError(null);
            const response = await jobRegistrationApi.getByMaDinhDanh(userMaDinhDanh);

            if (response.success === '1' && response.data) {
                setRegistrations(response.data);
            } else {
                setRegistrations([]);
            }
        } catch (error) {
            setError('Đã xảy ra lỗi khi tải danh sách đăng ký');
            setRegistrations([]);
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchRegistrations();
    }, [isMember, userInfo]);

    return {
        registrations,
        isLoading,
        error,
        refetch: fetchRegistrations,
    };
};
