import React, { useState, useRef, useEffect } from 'react';
import { Modal } from 'zmp-ui';
import Lottie from 'lottie-react';
import { useNavigate } from 'react-router-dom';
import { PhieuKetQua } from 'apis/getPhieuKetQuaApi';
import './AppointmentNotification.scss';
import PAGE_URL from 'constants/PAGE_URL';
import {
    FaCalendarAlt,
    FaClock,
    FaMapMarkerAlt,
    FaInfoCircle,
    FaMoneyBillWave,
    FaChevronLeft,
    FaChevronRight,
} from 'react-icons/fa';
import { useGlobalAppointmentNotifications } from 'hooks/useGlobalAppointmentNotifications';

// Import a calendar animation from assets
import calendarAnimation from 'assets/lottiers/calendar.json';
import { formatCurrency } from 'utils/common';

interface AppointmentNotificationProps {
    appointment: PhieuKetQua;
    visible: boolean;
    onClose: () => void;
}

const AppointmentNotification: React.FC<AppointmentNotificationProps> = ({
    appointment,
    visible,
    onClose,
}) => {
    const navigate = useNavigate();
    const { formatNotificationContent, nextAppointment, prevAppointment, currentIndex, totalNotifications } =
        useGlobalAppointmentNotifications();

    // State và ref cho thao tác vuốt
    const [touchStart, setTouchStart] = useState<number | null>(null);
    const [touchEnd, setTouchEnd] = useState<number | null>(null);
    const contentRef = useRef<HTMLDivElement>(null);

    // Ngưỡng vuốt tối thiểu để chuyển slide (px)
    const minSwipeDistance = 50;

    // Xử lý sự kiện vuốt
    const onTouchStart = (e: React.TouchEvent) => {
        setTouchEnd(null);
        setTouchStart(e.targetTouches[0].clientX);
    };

    const onTouchMove = (e: React.TouchEvent) => {
        setTouchEnd(e.targetTouches[0].clientX);
    };

    const onTouchEnd = () => {
        if (!touchStart || !touchEnd) return;

        const distance = touchStart - touchEnd;
        const isLeftSwipe = distance > minSwipeDistance;
        const isRightSwipe = distance < -minSwipeDistance;

        if (isLeftSwipe && totalNotifications > 1) {
            // Vuốt sang trái -> lịch hẹn tiếp theo
            nextAppointment();
        } else if (isRightSwipe && totalNotifications > 1) {
            // Vuốt sang phải -> lịch hẹn trước đó
            prevAppointment();
        }
    };

    // Chuyển đổi ngày bắt đầu (dd-MM-yyyy) sang Date object
    const getFormattedDate = (dateStr: string) => {
        try {
            const parts = dateStr.split('-');
            if (parts.length === 3) {
                const date = new Date(`${parts[2]}-${parts[1]}-${parts[0]}`);
                return date.toLocaleDateString('vi-VN', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                });
            }
            return dateStr;
        } catch (error) {
            console.error('Lỗi khi định dạng ngày tháng:', error);
            return dateStr;
        }
    };

    // Lấy ngày bắt đầu và kết thúc
    const startDateFormatted = getFormattedDate(appointment.thoiGianBatDau);
    const endDateFormatted = getFormattedDate(appointment.thoiGianKetThuc);

    const handleViewDetails = () => {
        navigate(PAGE_URL.SCHEDULE);
        onClose();
    };

    // Tính số ngày còn lại đến ngày kết thúc hoặc số ngày đến khi bắt đầu
    const calculateDays = () => {
        try {
            const today = new Date();

            // Chuyển đổi ngày bắt đầu
            const startParts = appointment.thoiGianBatDau.split('-');
            if (startParts.length !== 3) return { days: 0, isUpcoming: false };
            const startDate = new Date(`${startParts[2]}-${startParts[1]}-${startParts[0]}`);

            // Chuyển đổi ngày kết thúc
            const endParts = appointment.thoiGianKetThuc.split('-');
            if (endParts.length !== 3) return { days: 0, isUpcoming: false };
            const endDate = new Date(`${endParts[2]}-${endParts[1]}-${endParts[0]}`);

            // Kiểm tra xem đang trong thời gian hưởng trợ cấp hay sắp đến
            if (today >= startDate && today <= endDate) {
                // Đang trong thời gian hưởng trợ cấp, tính số ngày còn lại
                const daysRemaining = Math.ceil(
                    (endDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
                );
                return { days: daysRemaining > 0 ? daysRemaining : 0, isUpcoming: false };
            } else if (today < startDate) {
                // Sắp đến thời gian hưởng trợ cấp, tính số ngày còn lại đến khi bắt đầu
                const daysUntilStart = Math.ceil(
                    (startDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
                );
                return { days: daysUntilStart, isUpcoming: true };
            }

            return { days: 0, isUpcoming: false };
        } catch (error) {
            console.error('Lỗi khi tính toán ngày:', error);
            return { days: 0, isUpcoming: false };
        }
    };

    const { days, isUpcoming } = calculateDays();

    return (
        <Modal
            maskClosable={false}
            visible={visible}
            onClose={onClose}
            className="appointment-notification-modal"
        >
            <div
                className="appointment-notification"
                ref={contentRef}
                onTouchStart={onTouchStart}
                onTouchMove={onTouchMove}
                onTouchEnd={onTouchEnd}
            >
                <div className="appointment-notification__header">
                    <div className="appointment-notification__animation">
                        <Lottie
                            animationData={calendarAnimation}
                            loop={true}
                            style={{ height: 100, width: 300 }}
                        />
                    </div>
                    <h3 className="appointment-notification__title">Trợ cấp thất nghiệp</h3>
                    <div className="appointment-notification__subtitle">
                        {isUpcoming ? (
                            <>
                                Bạn sắp đến thời gian hưởng trợ cấp, còn{' '}
                                <span className="highlight">{days}</span> ngày nữa
                            </>
                        ) : (
                            <>
                                Bạn đang trong thời gian hưởng trợ cấp, còn lại{' '}
                                <span className="highlight">{days}</span> ngày
                            </>
                        )}
                    </div>
                </div>

                <div className="appointment-notification__content">
                    <div className="appointment-notification__name">{appointment.hoTen}</div>

                    <div className="appointment-notification__info">
                        <div className="appointment-notification__info-item">
                            <FaCalendarAlt className="appointment-notification__info-icon" />
                            <span>Bắt đầu: {startDateFormatted}</span>
                        </div>
                        <div className="appointment-notification__info-item">
                            <FaClock className="appointment-notification__info-icon" />
                            <span>Kết thúc: {endDateFormatted}</span>
                        </div>
                        <div className="appointment-notification__info-item">
                            <FaMapMarkerAlt className="appointment-notification__info-icon" />
                            <span>Trung tâm Dịch vụ việc làm Khánh Hòa</span>
                        </div>
                        <div className="appointment-notification__info-item appointment-notification__info-item--notes">
                            <FaMoneyBillWave className="appointment-notification__info-icon" />
                            <span>
                                Mức trợ cấp:{' '}
                                {appointment.mucTroCapHangThang
                                    ? formatCurrency(appointment.mucTroCapHangThang)
                                    : 'Chưa xác định'}
                            </span>
                        </div>
                    </div>
                </div>

                {totalNotifications > 1 && (
                    <div className="appointment-notification__pagination">
                        <button
                            className="appointment-notification__nav-button"
                            onClick={prevAppointment}
                            disabled={totalNotifications <= 1}
                        >
                            <FaChevronLeft />
                        </button>
                        <div className="appointment-notification__pagination-text">
                            {currentIndex + 1} / {totalNotifications}
                        </div>
                        <button
                            className="appointment-notification__nav-button"
                            onClick={nextAppointment}
                            disabled={totalNotifications <= 1}
                        >
                            <FaChevronRight />
                        </button>
                    </div>
                )}

                <div className="appointment-notification__actions">
                    <button
                        className="appointment-notification__button appointment-notification__button--secondary"
                        onClick={onClose}
                    >
                        Đóng
                    </button>
                    <button
                        className="appointment-notification__button appointment-notification__button--primary"
                        onClick={handleViewDetails}
                    >
                        Xem tất cả lịch hẹn
                    </button>
                </div>
            </div>
        </Modal>
    );
};

export default AppointmentNotification;
