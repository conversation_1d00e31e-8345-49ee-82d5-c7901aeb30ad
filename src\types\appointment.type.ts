export interface Appointment {
    id: string;
    title: string;
    description: string;
    date: string; // ISO string format
    time: string; // HH:MM format
    status: AppointmentStatus;
    type: AppointmentType;
    location?: string;
    notes?: string;
    createdAt: string;
    updatedAt: string;
}

export enum AppointmentStatus {
    PENDING = 'pending',
    CONFIRMED = 'confirmed',
    COMPLETED = 'completed',
    CANCELLED = 'cancelled'
}

export enum AppointmentType {
    UNEMPLOYMENT_BENEFIT = 'unemployment_benefit',
    CONSULTATION = 'consultation',
    DOCUMENT_SUBMISSION = 'document_submission',
    OTHER = 'other'
}

export interface AppointmentFormData {
    title: string;
    description: string;
    date: string;
    time: string;
    type: AppointmentType;
    location?: string;
    notes?: string;
}
