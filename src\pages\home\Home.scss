.home {
    &-container {
        position: relative;
        min-height: 100vh;
        transition: background 0.8s ease-in-out;

        &[data-theme='coffee'] {
            background: linear-gradient(
                135deg,
                rgba(198, 177, 163, 0.08) 0%,
                rgba(142, 106, 84, 0.05) 50%,
                rgba(87, 65, 47, 0.08) 100%
            );
            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: radial-gradient(
                    circle at center,
                    rgba(225, 213, 204, 0.04) 0%,
                    rgba(171, 145, 127, 0.03) 50%,
                    rgba(114, 93, 77, 0.05) 100%
                );
                pointer-events: none;
            }
        }

        &[data-theme='tea'] {
            background: linear-gradient(
                135deg,
                rgba(220, 233, 213, 0.08) 0%,
                rgba(183, 207, 171, 0.05) 50%,
                rgba(147, 177, 131, 0.08) 100%
            );
            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: radial-gradient(
                    circle at center,
                    rgba(226, 237, 220, 0.04) 0%,
                    rgba(196, 214, 186, 0.03) 50%,
                    rgba(164, 188, 150, 0.05) 100%
                );
                pointer-events: none;
            }
        }
    }

    .floating-products {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 0;
        pointer-events: none;

        .floating-product {
            position: absolute;
            width: 60px;
            height: auto;
            opacity: 0.6;
            animation: float 15s infinite ease-in-out;
        }
    }
    &__hero {
        width: 100%;
        height: 56.25vw;
        max-height: 100vh;
        position: relative;
        border-radius: 0 0 20px 20px;
        overflow: hidden;
        &-gradient {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1;
            background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.7) 100%);
        }
        &-banner {
            width: 100%;
            height: 100%;
            background-position: center;
            background-repeat: no-repeat;
            background-size: cover;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
        }
        &-title {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -45%);
            color: #fff;
            text-align: center;
            z-index: 2;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 10px;
            min-width: 260px;
            & > span {
                font-size: 18px;
                font-weight: 500;
                display: block;
                margin-bottom: 6px;
            }
            & > h1 {
                font-size: 40px;
                font-weight: 600;
                margin-bottom: 10px;
                // font-family: 'Beyond', sans-serif;
            }
            & > p {
                font-size: 16px;
                font-weight: 400;
            }
        }
        .swiper {
            width: 100%;
            height: 100%;
        }

        .swiper-slide {
            position: relative;
        }

        :global(.swiper-pagination) {
            bottom: 30px !important;
        }

        :global(.swiper-pagination-bullet) {
            width: 10px;
            height: 10px;
            background: rgba(255, 255, 255, 0.5);
            opacity: 1;
            transition: all 0.3s ease;
        }

        :global(.swiper-pagination-bullet-active) {
            background: #fff;
            width: 20px;
            border-radius: 5px;
        }
    }
    &__register {
        padding: 20px;
        width: 100%;
        &-home {
            position: relative;
            z-index: 1;
        }
        &-title {
            font-size: 18px;
            font-weight: 600;
            color: #000;
            text-align: center;
            margin-bottom: 20px;
            z-index: 1;
            position: relative;
        }
        &-warning {
            font-size: 14px;
            color: #6f7071;
            text-align: center;
            margin-bottom: 15px;
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 10px;
            font-style: italic;
            z-index: 1;
            position: relative;
        }
    }
    &__phone {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 10px;
        &-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            font-size: 16px;
            font-weight: 500;
            color: #000;
        }
        &-list {
            top: -20px;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: flex-start;
            gap: 10px;
            background-color: #fff;
            padding: 20px;
            border-radius: 10px;
        }
    }
}

.input-focused {
    .floating-products {
        position: absolute;
        height: auto;
    }
}

.swiper-pagination-bullet-active {
    background-color: #fff;
    z-index: 9;
    position: relative;
}

// Added styles for components that previously used inline styles
.loading-text {
    text-align: center;
    font-weight: 600;
    margin-bottom: 5px;
}

.register-button {
    width: 100%;
    margin-top: 15px;
}

@keyframes float {
    0%,
    100% {
        transform: translateY(0) rotate(0deg);
    }
    25% {
        transform: translateY(-20px) rotate(5deg);
    }
    50% {
        transform: translateY(0) rotate(0deg);
    }
    75% {
        transform: translateY(20px) rotate(-5deg);
    }
}

.header-title-ctn {
    display: flex;
    align-items: center;
    gap: 10px;
    & > img {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        object-fit: cover;
    }
    & > div {
        display: flex;
        flex-direction: column;
        & > h5 {
            font-size: 16px;
            font-weight: 600;
            margin: 0;
        }
        & > p {
            font-size: 14px;
            font-weight: 400;
            margin: 0;
            position: relative;
            top: -4px;
        }
    }
}
