import React, { useState } from 'react';
import { Modal, Input, Button, useSnackbar } from 'zmp-ui';
import { jobRegistrationApi, CancelJobRegistrationData } from 'apis/jobRegistrationApi';
import './CancelRegistrationModal.scss';
import { FaTimesCircle, FaExclamationTriangle } from 'react-icons/fa';

interface CancelRegistrationModalProps {
    visible: boolean;
    onClose: () => void;
    registrationId: number;
    maDinhDanh: string;
    onSuccess: () => void;
}

const CancelRegistrationModal: React.FC<CancelRegistrationModalProps> = ({
    visible,
    onClose,
    registrationId,
    maDinhDanh,
    onSuccess,
}) => {
    const [reason, setReason] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { openSnackbar } = useSnackbar();

    const handleCancel = () => {
        setReason('');
        onClose();
    };

    const handleSubmit = async () => {
        if (!reason.trim()) {
            openSnackbar({
                text: 'Vui lòng nhập lý do hủy đăng ký',
                type: 'error',
                duration: 3000,
            });
            return;
        }

        try {
            setIsSubmitting(true);
            const data: CancelJobRegistrationData = {
                maDangKy: registrationId,
                maDinhDanh,
                lyDo: reason,
            };

            const response = await jobRegistrationApi.cancelRegistration(data);

            if (response.success === '1') {
                openSnackbar({
                    text: 'Hủy đăng ký thành công',
                    type: 'success',
                    duration: 3000,
                });
                setReason('');
                onSuccess();
                onClose();
            } else {
                openSnackbar({
                    text: response.message || 'Hủy đăng ký không thành công',
                    type: 'error',
                    duration: 3000,
                });
            }
        } catch (error) {
            openSnackbar({
                text: 'Đã xảy ra lỗi khi hủy đăng ký',
                type: 'error',
                duration: 3000,
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <Modal
            visible={visible}
            title="Hủy đăng ký việc làm"
            onClose={handleCancel}
            className="cancel-registration-modal"
            showClose
        >
            <div className="modal-content">
                <div className="modal-icon">
                    <FaExclamationTriangle />
                </div>
                <p className="modal-description">
                    Bạn đang hủy đăng ký việc làm này. Vui lòng nhập lý do hủy đăng ký:
                </p>
                <Input
                    type="text"
                    placeholder="Nhập lý do hủy đăng ký"
                    value={reason}
                    onChange={(e) => setReason(e.target.value)}
                    className="reason-input"
                />
                <div className="modal-actions">
                    <Button className="cancel-button" onClick={handleCancel} disabled={isSubmitting}>
                        Hủy bỏ
                    </Button>
                    <Button
                        className="submit-button"
                        onClick={handleSubmit}
                        loading={isSubmitting}
                        disabled={isSubmitting}
                    >
                        Xác nhận
                    </Button>
                </div>
            </div>
        </Modal>
    );
};

export default CancelRegistrationModal;
