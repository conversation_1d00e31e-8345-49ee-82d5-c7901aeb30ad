import { useState, useEffect } from 'react';
import axios from 'axios';
import * as cheerio from 'cheerio';
import { JobItem } from './useCrawlData';
import { fetchWithServerProxy } from './useServerProxy';

const PROXY_URLS = [
    'https://corsproxy.io/?',
    'https://cors-anywhere.herokuapp.com/',
    'https://api.allorigins.win/raw?url=',
    'https://thingproxy.freeboard.io/fetch/',
    'https://cors.bridged.cc/',
];
const TARGET_URL = 'https://thongtinvieclamkhanhhoa.vn';

const fetchWithFallbackProxies = async (url: string) => {
    let lastError: Error | unknown;

    try {
        return await fetchWithServerProxy(url);
    } catch (error) {
        console.error('Server proxy failed, falling back to public proxies:', error);
        lastError = error;
    }

    for (const proxyUrl of PROXY_URLS) {
        try {
            const response = await axios.get(`${proxyUrl}${encodeURIComponent(url)}`, {
                timeout: 10000,
                headers: {
                    'User-Agent':
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                    Referer: 'https://www.google.com/',
                },
            });
            return response.data;
        } catch (error) {
            console.error(`Error with proxy ${proxyUrl}:`, error);
            lastError = error;
        }
    }

    throw lastError || new Error('All proxies failed');
};

export interface JobDetailItem extends JobItem {
    description?: string;
    requirements?: string;
    benefits?: string;
    contactInfo?: string;
    deadline?: string;
    jobInfo?: {
        position?: string;
        salary?: string;
        salaryDetail?: string;
        location?: string;
        type?: string;
        experience?: string;
        education?: string;
        gender?: string;
        quantity?: string;
        level?: string;
        workingTime?: string;
        industry?: string;
        language?: string;
        computerSkills?: string;
    };
}

export const useCrawlJobDetail = (url: string) => {
    const [data, setData] = useState<JobDetailItem | null>(null);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchData = async () => {
            if (!url) {
                setLoading(false);
                setError('URL không hợp lệ');
                return;
            }

            try {
                setLoading(true);
                setError(null);

                const html = await fetchWithFallbackProxies(url);

                const $ = cheerio.load(html);

                let id: string;
                if (url.includes('?Id=')) {
                    id = url.split('?Id=').pop() || 'detail';
                } else if (url.includes('/Id=')) {
                    id = url.split('Id=').pop() || 'detail';
                } else {
                    id = url.split('/').pop() || 'detail';
                }

                let title = '';
                const titleSelectors = [
                    '.recruitment-detail-title',
                    '.job-title',
                    'h1.title',
                    '.contentMain h1',
                ];

                for (const selector of titleSelectors) {
                    const titleElement = $(selector).first();
                    if (titleElement.length > 0) {
                        title = titleElement.text().trim();
                        if (title) break;
                    }
                }

                let company = '';
                const companySelectors = ['.company-name', '.recruitment-company', '.job-company'];

                for (const selector of companySelectors) {
                    const companyElement = $(selector).first();
                    if (companyElement.length > 0) {
                        company = companyElement.text().trim();
                        if (company) break;
                    }
                }

                let image = '';
                const imageSelectors = ['.company-logo img', '.recruitment-logo img', '.job-logo img'];

                for (const selector of imageSelectors) {
                    const imgElement = $(selector).first();
                    if (imgElement.length > 0) {
                        image = imgElement.attr('src') || '';
                        if (image) break;
                    }
                }

                if (image && image.startsWith('/')) {
                    image = `${TARGET_URL}${image}`;
                } else if (image && !image.startsWith('http')) {
                    image = `${TARGET_URL}/${image}`;
                }

                const jobInfo: JobDetailItem['jobInfo'] = {};
                const infoElement = $('.recruitment_detail_work_info').first();

                let description = '';
                let requirements = '';
                let benefits = '';
                let contactInfo = '';
                let deadline = '';
                if (infoElement.length > 0) {
                    const jobTitle = infoElement.find('#ctl00_ContentPlaceHolder_hTenCongViec').text().trim();
                    if (jobTitle) {
                        const positionMatch = jobTitle.match(/Vị trí công việc: (.+)/);
                        if (positionMatch && positionMatch[1]) {
                            jobInfo.position = positionMatch[1].trim();
                        } else {
                            jobInfo.position = jobTitle;
                        }
                    }

                    infoElement.find('.box1').each((_: number, box: any) => {
                        const boxTitle = $(box).find('.box_title').text().trim().toLowerCase();
                        const boxText = $(box).find('.box_text').text().trim();

                        if (boxTitle.includes('mức lương')) {
                            jobInfo.salary = boxText;
                        } else if (boxTitle.includes('địa điểm') || boxTitle.includes('nơi làm việc')) {
                            jobInfo.location = boxText;
                        } else if (boxTitle.includes('hình thức làm việc')) {
                            jobInfo.type = boxText;
                        } else if (boxTitle.includes('yêu cầu kinh nghiệm')) {
                            jobInfo.experience = boxText;
                        } else if (boxTitle.includes('yêu cầu bằng cấp')) {
                            jobInfo.education = boxText;
                        } else if (boxTitle.includes('yêu cầu giới tính')) {
                            jobInfo.gender = boxText;
                        } else if (boxTitle.includes('số lượng cần tuyển')) {
                            jobInfo.quantity = boxText;
                        } else if (boxTitle.includes('hạn nộp')) {
                            deadline = boxText;
                        } else if (boxTitle.includes('chức danh công việc')) {
                            if (!jobInfo.position) {
                                jobInfo.position = boxText;
                            }
                        }
                    });
                }

                if (!jobInfo.salary) {
                    const salaryText = $('.textSalary').text().trim();
                    if (salaryText) {
                        jobInfo.salary = salaryText.replace('Mức lương:', '').trim();
                    }
                }

                if (!jobInfo.location) {
                    const locationText = $('.textArea').text().trim();
                    if (locationText) {
                        jobInfo.location = locationText.replace('Khu vực:', '').trim();
                    }
                }

                const contentElement = $('.recruitment_detail_work_content').first();
                if (contentElement.length > 0) {
                    contentElement.find('li.card5').each((_: number, card: any) => {
                        const cardTitle = $(card).find('.card_title').text().trim().toUpperCase();
                        const cardBody = $(card).find('.card_body').html() || '';

                        if (cardTitle.includes('MÔ TẢ') || cardTitle.includes('NỘI DUNG')) {
                            description = cardBody;
                        } else if (cardTitle.includes('YÊU CẦU')) {
                            requirements = cardBody;
                        } else if (cardTitle.includes('QUYỀN LỢI') || cardTitle.includes('CHẾ ĐỘ')) {
                            benefits = cardBody;
                        }
                    });

                    const contactBlock = contentElement.find('.mBlock1');
                    if (contactBlock.length > 0) {
                        const contactTitle = contactBlock.find('.title2').text().trim();
                        if (contactTitle.includes('THÔNG TIN LIÊN HỆ')) {
                            contactInfo = contactBlock.html() || '';
                        }
                    }
                }

                if (!description && !requirements && !benefits) {
                    const jobDescription = $('.job-description').html() || '';
                    if (jobDescription) {
                        description = jobDescription;
                    }
                }

                const jobDetail: JobDetailItem = {
                    id,
                    title: title || 'Chi tiết việc làm',
                    company,
                    location: jobInfo.location || '',
                    salary: jobInfo.salary || '',
                    image,
                    link: url,
                    description,
                    requirements,
                    benefits,
                    contactInfo,
                    deadline,
                    jobInfo,
                };

                setData(jobDetail);
                setLoading(false);
            } catch (err) {
                console.error('Error fetching job detail:', err);
                setError('Có lỗi xảy ra khi tải dữ liệu. Vui lòng thử lại sau.');
                setLoading(false);
            }
        };

        fetchData();
    }, [url]);

    return { data, loading, error };
};
