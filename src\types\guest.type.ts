export interface IGuestFromCrm {
    id: number;
    maKhach: string;
    danhXung?: string | null;
    hoTen: string;
    gioiTinh?: string | null;
    birthday?: string | null;
    quocTich?: string | null;
    diaChi: string;
    dienThoai: string;
    email: string;
    congTy?: string | null;
    tag: string[];
    zaloId?: string | null;
    fbId?: string | null;
    type: number;
    maDatPhong?: string | null;
    loaiPhong?: string | null;
    khachSan?: string | null;
    ngayDen?: string | null;
    ngayDi?: string | null;
    ngayXacNhan?: string | null;
    action?: string | null;
    tenLoai?: string | null;
    ngayTao?: string | null;
    isPublic: boolean;
    owner: string;
    chatContactId: number;
    avatar?: string | null;
    ngonNgu: string;
    maDinhDanh?: string | null;
    cccd?: string | null;
}
