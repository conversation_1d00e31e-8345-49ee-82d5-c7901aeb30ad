import { useState, useEffect } from 'react';
import { useRecoilState, useRecoilValue, useSetRecoilState } from 'recoil';
import { appointmentsState } from 'state/appointmentState';
import { phieuKetQuaApi } from 'apis/getPhieuKetQuaApi';
import { hasMaDinhDanhState, phieuKetQuaState } from 'state/phieuKetQuaState';
import { isMemberState, userInfoState } from 'state';

export const useAppointments = () => {
    const [appointments, setAppointments] = useRecoilState(appointmentsState);
    const [maDinhDanh, setMaDinhDanh] = useState<string>('');
    const phieuKetQua = useRecoilValue(phieuKetQuaState);
    const userInfo = useRecoilValue(userInfoState);
    const isMember = useRecoilValue(isMemberState);
    const setPhieuKetQua = useSetRecoilState(phieuKetQuaState);
    const setHasMaDinhDanh = useSetRecoilState(hasMaDinhDanhState);

    useEffect(() => {
        if (phieuKetQua && phieuKetQua.length > 0) {
            setAppointments(phieuKetQua);
        }
    }, [phieuKetQua, setAppointments]);

    useEffect(() => {
        const autoFetchAppointments = async () => {
            if (isMember && userInfo) {
                const userMaDinhDanh = String(userInfo.maDinhDanh || userInfo.cccd || '');

                if (userMaDinhDanh && userMaDinhDanh.length > 0) {
                    try {
                        const response = await phieuKetQuaApi.getByMaDinhDanh(userMaDinhDanh);
                        if (response.success === '1' && response.data && response.data.length > 0) {
                            setPhieuKetQua(response.data);
                            setHasMaDinhDanh(true);
                        }
                    } catch (error) {}
                }
            }
        };

        autoFetchAppointments();
    }, [isMember, userInfo, setPhieuKetQua, setHasMaDinhDanh]);

    const fetchAppointmentsByMaDinhDanh = async (maDinhDanh: string) => {
        try {
            const response = await phieuKetQuaApi.getByMaDinhDanh(maDinhDanh);
            if (response.success === '1' && response.data.length > 0) {
                setAppointments(response.data);
                return response.data;
            }
            return [];
        } catch (error) {
            return [];
        }
    };

    return {
        appointments,
        fetchAppointmentsByMaDinhDanh,
        setMaDinhDanh,
        maDinhDanh,
    };
};
