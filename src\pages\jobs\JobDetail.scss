.job-detail-page {
    background-color: #fff;
    .job-detail-container {
        padding: 16px;
        position: relative;
    }

    // Header
    .job-detail-header {
        margin-bottom: 16px;
    }

    .job-detail-company {
        display: flex;
        align-items: center;
        gap: 12px;
        @media (max-width: 480px) {
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }

        .company-logo {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            overflow: hidden;
            background-color: #f0f0f0;
            border: 1px solid #eee;
            flex-shrink: 0;

            @media (max-width: 480px) {
                width: 70px;
                height: 70px;
            }

            img {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }

            .image-placeholder {
                width: 100%;
                height: 100%;
                background: #f0f0f0;
            }
        }

        .company-info {
            flex: 1;

            @media (max-width: 480px) {
                width: 100%;
                text-align: center;
            }
        }
    }

    .job-detail-title {
        font-size: 18px;
        font-weight: 600;
        color: #213982;
        margin-bottom: 4px;
        line-height: 1.3;
    }

    .company-name {
        font-size: 16px;
        font-weight: 500;
        color: #444;
        margin-bottom: 6px;
    }

    .job-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-top: 6px;

        @media (max-width: 480px) {
            justify-content: center;
        }

        .job-deadline {
            display: flex;
            align-items: center;
            gap: 4px;
            background-color: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 13px;
            color: #e53935;
            font-weight: 500;
            border: 1px solid #eee;

            .meta-icon {
                color: #e53935;
                font-size: 12px;
            }
        }
    }

    // Body
    .job-detail-body {
        display: flex;
        flex-direction: column;
        gap: 25px;

        @media (max-width: 480px) {
            gap: 20px;
        }
    }

    // Section Title
    .section-title {
        font-size: 20px;
        font-weight: 600;
        color: #213982;
        margin-bottom: 20px;
        position: relative;
        padding-bottom: 10px;

        @media (max-width: 480px) {
            font-size: 18px;
            margin-bottom: 16px;
            padding-bottom: 8px;
        }

        &:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background-color: #213982;
            border-radius: 3px;

            @media (max-width: 480px) {
                width: 40px;
                height: 2px;
            }
        }
    }

    // Info Cards
    .job-detail-info-cards {
        margin-bottom: 24px;
    }

    .info-cards-row {
        display: grid;
        grid-template-columns: 1fr;
        gap: 8px;
        margin-bottom: 8px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .info-card {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 12px;
        display: flex;
        align-items: center;
        gap: 12px;
        border: 1px solid #eee;
    }

    .info-card-icon {
        width: 32px;
        height: 32px;
        min-width: 32px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #213982;
        font-size: 16px;
        background-color: rgba(33, 57, 130, 0.1);
    }

    .info-card-content {
        flex: 1;
        overflow: hidden;
    }

    .info-card-title {
        font-size: 13px;
        color: #666;
        margin: 0 0 3px;
    }

    .info-card-value {
        font-size: 15px;
        font-weight: 500;
        color: #333;
        margin: 0;
        line-height: 1.3;
    }

    // Content Sections
    .job-detail-section {
        background-color: #fff;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 16px;
        border: 1px solid #eee;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .section-content {
        font-size: 14px;
        line-height: 1.5;
        color: #333;

        p {
            margin-bottom: 12px;

            &:last-child {
                margin-bottom: 0;
            }
        }

        ul,
        ol {
            margin-bottom: 12px;
            padding-left: 16px;

            &:last-child {
                margin-bottom: 0;
            }

            li {
                margin-bottom: 6px;

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }

        img {
            max-width: 100%;
            height: auto;
            margin: 12px 0;
            border-radius: 4px;
        }

        a {
            color: #213982;
            text-decoration: none;

            &:hover {
                text-decoration: underline;
            }
        }
    }

    // Actions
    .job-detail-actions {
        margin-top: 20px;
        display: flex;
        justify-content: space-between;
        gap: 10px;
        padding-top: 16px;
        border-top: 1px solid #eee;

        @media (max-width: 480px) {
            flex-direction: column;
        }
    }

    .back-button {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        background-color: #213982;
        color: white;
        border: none;
        padding: 10px 16px;
        border-radius: 6px;
        font-weight: 500;
        font-size: 14px;
        cursor: pointer;

        @media (max-width: 480px) {
            width: 100%;
        }
    }

    .source-button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        background-color: #f8f9fa;
        color: #213982;
        border: 1px solid #213982;
        padding: 10px 16px;
        border-radius: 6px;
        font-weight: 500;
        font-size: 14px;
        text-decoration: none;
        cursor: pointer;

        @media (max-width: 480px) {
            width: 100%;
        }
    }

    // Loading and Error States
    .job-detail-loading,
    .job-detail-error {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 30px 16px;
        text-align: center;
        min-height: 200px;

        p {
            margin-top: 16px;
            color: #666;
            font-size: 14px;
        }
    }

    .loading-spinner {
        width: 32px;
        height: 32px;
        border: 3px solid rgba(33, 57, 130, 0.2);
        border-radius: 50%;
        border-top-color: #213982;
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to {
            transform: rotate(360deg);
        }
    }
}
