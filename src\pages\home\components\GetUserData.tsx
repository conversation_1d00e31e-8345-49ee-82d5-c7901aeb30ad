import welcome from 'assets/lottiers/welcome.json';
import { COLOR } from 'constants/STYLE';
import { useLocalStorage } from 'hooks/useLocalStorage';
import { useZaloPhone } from 'hooks/useZaloPhone';
import Lot<PERSON> from 'lottie-react';
import React from 'react';
import { FaPeopleArrows, FaRegAddressCard } from 'react-icons/fa';
import { LazyLoadImage } from 'react-lazy-load-image-component';
import { useRecoilState, useSetRecoilState } from 'recoil';
import { userInfoState, userNumberState, userZaloInfoState } from 'state';
import { authorize, getUserInfo } from 'zmp-sdk/apis';
import { Button, useSnackbar } from 'zmp-ui';
function GetUserData() {
    const { openSnackbar } = useSnackbar();
    const [userNumber, setUserNumber] = useRecoilState(userNumberState);
    const setRecoilState = useSetRecoilState(userInfoState);
    const setRecoilUserZaloInfoState = useSetRecoilState(userZaloInfoState);
    const [, setUserInfo] = useLocalStorage('userInfo', '');
    const handleUserInfo = async (userInfo: any) => {
        setUserInfo(userInfo);
        setRecoilState(userInfo);
        setRecoilUserZaloInfoState(userInfo);
    };
    const handleUserInfoFailure = () => {
        setRecoilState({});
    };
    const { getZaloPhoneNumber, isLoading } = useZaloPhone({
        onSuccess: (phoneNumber) => {
            setUserNumber(phoneNumber);
            localStorage.setItem('userPhoneNumber', phoneNumber);
        },
        onError: (error) => {
            openSnackbar({
                text: 'Đã xảy ra lỗi khi lấy số điện thoại',
                type: 'error',
            });
        },
    });

    const getUser = async () => {
        try {
            await getUserInfo({
                success: ({ userInfo }) => handleUserInfo(userInfo),
                fail: handleUserInfoFailure,
            });
        } catch (error) {
            setUserInfo('');
            setRecoilState({});
        }
    };

    const authorizedUser = async () => {
        try {
            const data = await authorize({
                scopes: ['scope.userInfo', 'scope.userPhonenumber'],
            });

            const promises: Promise<any>[] = [];
            if (data['scope.userInfo']) {
                promises.push(getUser());
            }
            if (data['scope.userPhonenumber']) {
                promises.push(getZaloPhoneNumber());
            }

            await Promise.all(promises);
        } catch (error) {
        } finally {
        }
    };
    return (
        <div className="home__phone">
            <Lottie
                animationData={welcome}
                loop={true}
                style={{
                    height: 230,
                    width: 230,
                }}
            />
            <div className="home__phone-list">
                <div className="home__phone-item">
                    <FaPeopleArrows color={COLOR.PRIMARY} />
                    <p>Đăng ký thành viên nhanh chóng</p>
                </div>
                {/* <div className="home__phone-item">
                    <IoQrCode color={COLOR.PRIMARY} />
                    <p>Tra cứu, lấy mã QR thành viên </p>
                </div> */}
                <div className="home__phone-item">
                    <FaRegAddressCard color={COLOR.PRIMARY} />
                    <p>Theo dõi thẻ các phiếu kết quả, lịch hẹn nhanh chóng</p>
                </div>
                <p>
                    Vui lòng đồng ý chia sẻ số điện thoại của bạn để liên kết với hệ thống định danh của của
                    chúng tôi.
                </p>
                <Button onClick={authorizedUser} style={{ width: '100%', marginTop: 10 }}>
                    Liên kết số điện thoại
                </Button>
                <div
                    style={{
                        width: '100%',
                        display: 'flex',
                        justifyContent: 'center',
                        marginTop: 10,
                    }}
                >
                    <LazyLoadImage
                        src={
                            'https://thongtinvieclamkhanhhoa.vn/assets/images/brand/trung-tam-dich-viec-lam-logo-header.svg'
                        }
                        alt="logo"
                        width={100}
                        height={100}
                        // effect="blur"
                    />
                </div>
            </div>
        </div>
    );
}

export default GetUserData;
