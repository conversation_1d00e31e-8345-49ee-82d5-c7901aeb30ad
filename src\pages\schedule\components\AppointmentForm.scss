.appointment-form {
    padding: 16px;
    
    .form-group {
        margin-bottom: 16px;
        
        &--half {
            width: calc(50% - 8px);
        }
    }
    
    .form-row {
        display: flex;
        gap: 16px;
        margin-bottom: 16px;
    }
    
    label {
        display: block;
        font-size: 14px;
        font-weight: 500;
        color: #333;
        margin-bottom: 6px;
        
        .required {
            color: #f44336;
        }
    }
    
    .form-control {
        width: 100%;
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 14px;
        transition: border-color 0.2s ease;
        background-color: white;
        
        &:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        &.is-invalid {
            border-color: #f44336;
        }
    }
    
    .invalid-feedback {
        font-size: 12px;
        color: #f44336;
        margin-top: 4px;
    }
    
    .form-actions {
        display: flex;
        justify-content: space-between;
        margin-top: 24px;
    }
    
    .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.2s ease;
        
        &-primary {
            background-color: var(--primary-color);
            color: white;
            
            &:hover:not(:disabled) {
                background-color: darken(#213982, 10%);
            }
            
            &:disabled {
                opacity: 0.7;
                cursor: not-allowed;
            }
        }
        
        &-secondary {
            background-color: #e0e0e0;
            color: #333;
            
            &:hover {
                background-color: darken(#e0e0e0, 10%);
            }
        }
    }
}
