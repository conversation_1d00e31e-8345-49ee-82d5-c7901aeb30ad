// Hàm chuẩn hóa text - loại bỏ dấu, ký tự đặc biệt
export function normalizeText(text: string): string {
    if (!text) return '';
    return text.toLowerCase()
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '') // Remove diacritics (á->a, ê->e)
        .replace(/[^\w\s]/g, ' ')       // Replace special chars with space
        .replace(/\s+/g, ' ')           // Multiple spaces to single space
        .trim();
}

// Hàm tách từ khóa quan trọng
export function extractKeywords(text: string): string[] {
    const normalized = normalizeText(text);
    const words = normalized.split(' ');
    
    // Loại bỏ stop words tiếng Việt
    const stopWords = ['cua', 'va', 'la', 'den', 'tu', 'voi', 'trong', 'ngoai', 'tren', 'duoi', 
                     'cho', 'boi', 'theo', 'nhu', 'hon', 'cung', 'roi', 'da', 'se', 'co', 
                     'khong', 'ma', 'hoac', 'neu', 'khi', 'vi', 'de', 'hay', 'that'];
    
    // Giữ lại từ có ý nghĩa (length >= 2 và không phải stop word)
    return words.filter(word => word.length >= 2 && !stopWords.includes(word));
}

interface AbbreviationResult {
    original: string;
    expanded: string;
    abbreviations: string[];
}

// Hàm tìm viết tắt thông minh
export function findAbbreviations(text: string): AbbreviationResult {
    const abbreviations: string[] = [];
    const normalized = normalizeText(text);
    
    // Regex tìm các từ viết hoa liên tiếp (VD: NVKD, CNTT)
    const upperCasePattern = /\b[A-Z]{2,}\b/g;
    const matches = text.match(upperCasePattern);
    if (matches) {
        abbreviations.push(...matches.map(m => m.toLowerCase()));
    }
    
    // Tìm pattern từ viết tắt phổ biến
    const commonAbbrevs = [
        { pattern: /\bnv\b/gi, expansion: 'nhan vien' },
        { pattern: /\bkt\b/gi, expansion: 'ke toan' },
        { pattern: /\bkd\b/gi, expansion: 'kinh doanh' },
        { pattern: /\bvp\b/gi, expansion: 'van phong' },
        { pattern: /\bcn\b/gi, expansion: 'cong nhan' },
        { pattern: /\bks\b/gi, expansion: 'ky su' },
        { pattern: /\btl\b/gi, expansion: 'to truong' },
        { pattern: /\bql\b/gi, expansion: 'quan ly' },
        { pattern: /\bpv\b/gi, expansion: 'phuc vu' }
    ];
    
    let expandedText = normalized;
    commonAbbrevs.forEach(abbrev => {
        expandedText = expandedText.replace(abbrev.pattern, abbrev.expansion);
    });
    
    return {
        original: normalized,
        expanded: expandedText,
        abbreviations: abbreviations
    };
}

interface ScoreDetail {
    score: number;
    reason: string;
}

interface MatchResult {
    score: number;
    reason: string;
    details: ScoreDetail[];
}

// Hàm tính điểm tương đồng dựa trên nhiều tiêu chí
export function calculateMatchScore(searchTerm: string, jobText: string, jobDescription: string = ''): MatchResult {
    const search = findAbbreviations(searchTerm);
    const job = findAbbreviations(jobText);
    const desc = findAbbreviations(jobDescription);
    
    let maxScore = 0;
    const scores: ScoreDetail[] = [];
    
    // 1. So sánh trực tiếp
    if (search.original === job.original) {
        scores.push({ score: 100, reason: 'Khớp hoàn toàn' });
    }
    
    // 2. So sánh với text đã mở rộng viết tắt
    if (search.expanded === job.expanded) {
        scores.push({ score: 95, reason: 'Khớp sau khi mở rộng viết tắt' });
    }
    
    // 3. Kiểm tra chứa từ khóa
    if (job.original.includes(search.original) || search.original.includes(job.original)) {
        const ratio = Math.min(search.original.length, job.original.length) / 
                     Math.max(search.original.length, job.original.length);
        scores.push({ score: 80 * ratio, reason: 'Chứa từ khóa chính' });
    }
    
    // 4. Kiểm tra với expanded text
    if (job.expanded.includes(search.expanded) || search.expanded.includes(job.expanded)) {
        scores.push({ score: 85, reason: 'Khớp text mở rộng' });
    }
    
    // 5. So sánh keywords
    const searchKeywords = extractKeywords(search.expanded);
    const jobKeywords = extractKeywords(job.expanded);
    const descKeywords = extractKeywords(desc.expanded);
    
    const allJobKeywords = [...new Set([...jobKeywords, ...descKeywords])];
    const commonKeywords = searchKeywords.filter(word => 
        allJobKeywords.some(jobWord => 
            jobWord.includes(word) || word.includes(jobWord)
        )
    );
    
    if (commonKeywords.length > 0) {
        const keywordScore = (commonKeywords.length / searchKeywords.length) * 70;
        scores.push({ 
            score: keywordScore, 
            reason: `Có ${commonKeywords.length} từ khóa chung: ${commonKeywords.join(', ')}` 
        });
    }
    
    // 6. Fuzzy matching với regex
    const fuzzyScore = calculateFuzzyMatch(search.expanded, job.expanded);
    if (fuzzyScore > 0) {
        scores.push({ score: fuzzyScore, reason: 'Tương đồng mờ' });
    }
    
    // 7. Kiểm tra pattern matching
    const patternScore = checkPatternMatch(search.original, job.original, desc.original);
    if (patternScore > 0) {
        scores.push({ score: patternScore, reason: 'Khớp pattern' });
    }
    
    // Lấy điểm cao nhất
    if (scores.length > 0) {
        const bestMatch = scores.reduce((prev, current) => 
            prev.score > current.score ? prev : current
        );
        return {
            score: Math.round(bestMatch.score),
            reason: bestMatch.reason,
            details: scores
        };
    }
    
    return { score: 0, reason: 'Không khớp', details: [] };
}

// Hàm tính toán fuzzy matching
function calculateFuzzyMatch(str1: string, str2: string): number {
    const words1 = str1.split(' ');
    const words2 = str2.split(' ');
    
    let matchCount = 0;
    for (const word1 of words1) {
        for (const word2 of words2) {
            if (word1.length >= 3 && word2.length >= 3) {
                // Kiểm tra substring
                if (word1.includes(word2) || word2.includes(word1)) {
                    matchCount++;
                    break;
                }
                // Kiểm tra edit distance
                if (editDistance(word1, word2) <= 2) {
                    matchCount++;
                    break;
                }
            }
        }
    }
    
    return words1.length > 0 ? (matchCount / words1.length) * 60 : 0;
}

// Hàm tính edit distance (Levenshtein distance)
function editDistance(str1: string, str2: string): number {
    const matrix: number[][] = [];
    
    for (let i = 0; i <= str2.length; i++) {
        matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
        matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
        for (let j = 1; j <= str1.length; j++) {
            if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                matrix[i][j] = matrix[i - 1][j - 1];
            } else {
                matrix[i][j] = Math.min(
                    matrix[i - 1][j - 1] + 1,
                    matrix[i][j - 1] + 1,
                    matrix[i - 1][j] + 1
                );
            }
        }
    }
    
    return matrix[str2.length][str1.length];
}

// Hàm kiểm tra pattern matching với regex
function checkPatternMatch(searchTerm: string, jobTitle: string, jobDesc: string): number {
    const search = normalizeText(searchTerm);
    const title = normalizeText(jobTitle);
    const desc = normalizeText(jobDesc);
    
    // Tạo regex pattern từ search term
    const searchWords = search.split(' ').filter(w => w.length >= 2);
    if (searchWords.length === 0) return 0;
    
    let score = 0;
    
    // Pattern 1: Tìm các từ theo thứ tự bất kỳ
    const flexiblePattern = new RegExp(searchWords.join('.*'), 'i');
    if (flexiblePattern.test(title) || flexiblePattern.test(desc)) {
        score = Math.max(score, 50);
    }
    
    // Pattern 2: Tìm ít nhất 70% từ khóa
    const foundWords = searchWords.filter(word => {
        const wordPattern = new RegExp(`\\b${word}`, 'i');
        return wordPattern.test(title) || wordPattern.test(desc);
    });
    
    if (foundWords.length >= Math.ceil(searchWords.length * 0.7)) {
        score = Math.max(score, (foundWords.length / searchWords.length) * 65);
    }
    
    return score;
}

export interface JobWithScore extends Record<string, any> {
    matchScore: number;
    matchReason: string;
    matchDetails: ScoreDetail[];
}

// Hàm tìm công việc phù hợp
export function findMatchingJobs<T extends Record<string, any>>(
    viTriCanTim: string, 
    congViecList: T[], 
    minScore: number = 40
): (T & JobWithScore)[] {
    const matches: (T & JobWithScore)[] = [];
    
    for (const congViec of congViecList) {
        const matchResult = calculateMatchScore(
            viTriCanTim, 
            congViec.tenCongViec, 
            congViec.moTa || ''
        );
        
        if (matchResult.score >= minScore) {
            matches.push({
                ...congViec,
                matchScore: matchResult.score,
                matchReason: matchResult.reason,
                matchDetails: matchResult.details
            });
        }
    }
    
    // Sắp xếp theo điểm số giảm dần
    return matches.sort((a, b) => b.matchScore - a.matchScore);
}
