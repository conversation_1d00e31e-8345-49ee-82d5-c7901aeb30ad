import { useState, useEffect } from 'react';
import axios from 'axios';
import * as cheerio from 'cheerio';
import { NewsItem } from './useCrawlData';
import { fetchWithServerProxy } from './useServerProxy';

const PROXY_URLS = [
    'https://corsproxy.io/?',
    'https://cors-anywhere.herokuapp.com/',
    'https://api.allorigins.win/raw?url=',
    'https://thingproxy.freeboard.io/fetch/',
    'https://cors.bridged.cc/',
];

const fetchWithFallbackProxies = async (url: string) => {
    let lastError: Error | unknown;

    try {
        return await fetchWithServerProxy(url);
    } catch (error) {
        console.error('Server proxy failed, falling back to public proxies:', error);
        lastError = error;
    }

    for (const proxyUrl of PROXY_URLS) {
        try {
            const response = await axios.get(`${proxyUrl}${encodeURIComponent(url)}`, {
                timeout: 10000,
                headers: {
                    'User-Agent':
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                    Referer: 'https://www.google.com/',
                },
            });
            return response.data;
        } catch (error) {
            console.error(`Error with proxy ${proxyUrl}:`, error);
            lastError = error;
        }
    }

    throw lastError || new Error('All proxies failed');
};

export const useCrawlNewsDetail = (url: string) => {
    const [data, setData] = useState<NewsItem | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [error, setError] = useState<Error | null>(null);

    useEffect(() => {
        const fetchData = async () => {
            if (!url) {
                setIsLoading(false);
                return;
            }

            try {
                setIsLoading(true);

                const html = await fetchWithFallbackProxies(url);
                const $ = cheerio.load(html);
                let title = '';
                const titleSelectors = [
                    '.title3',
                    'h1.title',
                    '.news-title',
                    '.article-title',
                    '.detail-title',
                    '.contentMain h1',
                    '.content h1',
                    'h1',
                ];

                for (const selector of titleSelectors) {
                    const titleElement = $(selector).first();
                    if (titleElement.length > 0) {
                        title = titleElement.text().trim();
                        if (title) break;
                    }
                }

                if (!title) {
                    const metaTitle =
                        $('meta[property="og:title"]').attr('content') ||
                        $('meta[name="title"]').attr('content') ||
                        $('title').text();
                    if (metaTitle) {
                        title = metaTitle.trim();
                    }
                }

                let date = '';
                const dateSelectors = [
                    '.date',
                    '.news-date',
                    '.article-date',
                    '.time',
                    '.datetime',
                    'time',
                    '.meta-date',
                ];

                for (const selector of dateSelectors) {
                    const dateElement = $(selector).first();
                    if (dateElement.length > 0) {
                        date = dateElement.text().trim();
                        if (date) break;
                    }
                }

                if (!date) {
                    const dateMatch = $('body')
                        .text()
                        .match(/Ngày:\s*([\d\/]+)/i);
                    if (dateMatch && dateMatch[1]) {
                        date = dateMatch[1].trim();
                    }
                }

                let image = '';
                const imageSelectors = [
                    '.news-image img',
                    '.article-image img',
                    '.detail-image img',
                    '.content img',
                    '.contentMain img',
                ];

                for (const selector of imageSelectors) {
                    const imgElement = $(selector).first();
                    if (imgElement.length > 0) {
                        image = imgElement.attr('src') || '';
                        if (image) break;
                    }
                }

                if (!image) {
                    image =
                        $('meta[property="og:image"]').attr('content') ||
                        $('meta[name="image"]').attr('content') ||
                        '';
                }

                if (!image) {
                    $('img').each((_, el) => {
                        const src = $(el).attr('src') || '';
                        if (
                            src &&
                            !src.includes('logo') &&
                            !src.includes('banner') &&
                            !src.includes('icon')
                        ) {
                            const width = parseInt($(el).attr('width') || '0');
                            const height = parseInt($(el).attr('height') || '0');

                            if (width > 100 || height > 100 || (!width && !height)) {
                                image = src;
                                return false; // break the loop
                            }
                        }
                        return true; // continue the loop
                    });
                }

                if (image && image.startsWith('/')) {
                    const urlObj = new URL(url);
                    image = `${urlObj.origin}${image}`;
                } else if (image && !image.startsWith('http')) {
                    const urlObj = new URL(url);
                    image = `${urlObj.origin}/${image}`;
                }

                $('script').remove();

                let contentHtml = '';

                const articleDetailContent = $('.article_detail_content').first();
                if (articleDetailContent.length > 0) {
                    articleDetailContent
                        .find('.sidebar, .comments, .related, .navigation, .footer, nav, header')
                        .remove();

                    contentHtml = articleDetailContent.html() || '';
                } else {
                    const contentColLeft = $('.content-col-left').first();
                    if (contentColLeft.length > 0) {
                        const nestedArticleDetail = contentColLeft.find('.article_detail_content').first();
                        if (nestedArticleDetail.length > 0) {
                            contentHtml = nestedArticleDetail.html() || '';
                        } else {
                            contentHtml = '<p class="no-content">Không có nội dung</p>';
                        }
                    } else {
                        contentHtml = '<p class="no-content">Không có nội dung</p>';
                    }
                }

                if (!contentHtml || contentHtml.trim() === '') {
                    contentHtml = '<p class="no-content">Không có nội dung</p>';
                }

                let imageList: string[] = [];
                let isImageGallery = false;
                let hasTextContent = false;
                let hasTextParagraphs = false;

                const galleryElement = $('.article_detail_content').first();
                if (galleryElement.length > 0) {
                    const images = galleryElement.find('img');

                    const paragraphs = galleryElement.find('p');
                    const paragraphsWithImages = paragraphs.filter(function () {
                        return $(this).find('img').length > 0;
                    });

                    const textParagraphs = paragraphs.filter(function () {
                        const text = $(this).clone().children('img').remove().end().text().trim();
                        return text.length > 20;
                    });

                    hasTextContent = textParagraphs.length > 0;
                    hasTextParagraphs = textParagraphs.length >= 2;

                    if (
                        images.length > 0 &&
                        (!hasTextContent ||
                            (paragraphsWithImages.length > 0 &&
                                paragraphsWithImages.length >= paragraphs.length * 0.7 &&
                                !hasTextParagraphs))
                    ) {
                        isImageGallery = true;

                        images.each((_, img) => {
                            const src = $(img).attr('src') || '';
                            if (src) {
                                let fullSrc = src;
                                if (src.startsWith('/')) {
                                    const urlObj = new URL(url);
                                    fullSrc = `${urlObj.origin}${src}`;
                                } else if (!src.startsWith('http')) {
                                    const urlObj = new URL(url);
                                    fullSrc = `${urlObj.origin}/${src}`;
                                }

                                if (!imageList.includes(fullSrc)) {
                                    imageList.push(fullSrc);
                                }
                            }
                        });
                    }
                }

                if (!isImageGallery && !hasTextContent && contentHtml.trim() === '') {
                    contentHtml = '<p class="no-content">Không có nội dung</p>';
                }

                if (contentHtml) {
                    contentHtml = contentHtml.replace(
                        /<img/g,
                        '<img class="news-detail-image" style="max-width:100%; height:auto; margin:10px 0;"'
                    );

                    contentHtml = contentHtml.replace(
                        /<p/g,
                        '<p class="news-detail-paragraph" style="margin-bottom:12px; line-height:1.5;"'
                    );

                    contentHtml = contentHtml.replace(
                        /<p align="center"/g,
                        '<p class="news-detail-paragraph text-center" style="margin-bottom:12px; line-height:1.5; text-align:center;"'
                    );

                    contentHtml = contentHtml.replace(
                        /<table/g,
                        '<table style="width:100%; border-collapse:collapse; margin:15px 0;"'
                    );
                    contentHtml = contentHtml.replace(
                        /<th/g,
                        '<th style="border:1px solid #ddd; padding:8px; background-color:#f2f2f2;"'
                    );
                    contentHtml = contentHtml.replace(
                        /<td/g,
                        '<td style="border:1px solid #ddd; padding:8px;"'
                    );

                    const urlObj = new URL(url);
                    contentHtml = contentHtml.replace(
                        /href=["\'](\/?[^"\']+)["\'](?![^<>]*?:\/\/)/g,
                        `href="${urlObj.origin}$1"`
                    );
                }

                if (isImageGallery && imageList.length > 0) {
                    let galleryHtml = `
                        <div class="image-gallery">
                            <div class="gallery-container">
                    `;

                    imageList.forEach((imgSrc, index) => {
                        galleryHtml += `
                            <div class="gallery-item">
                                <img src="${imgSrc}" alt="Hình ảnh ${index + 1}" class="gallery-image" />
                                <div class="image-number">${index + 1}/${imageList.length}</div>
                            </div>
                        `;
                    });

                    galleryHtml += `
                            </div>
                        </div>
                    `;

                    contentHtml = galleryHtml;
                }

                const newsItem: NewsItem = {
                    id: url.split('/').pop() || 'detail',
                    title: title || 'Chi tiết tin tức',
                    date,
                    image,
                    link: url,
                    content: contentHtml,
                    images: isImageGallery ? imageList : undefined,
                };

                setData(newsItem);
            } catch (err) {
                console.error('Error fetching news detail:', err);
                setError(err instanceof Error ? err : new Error('Unknown error occurred'));
            } finally {
                setIsLoading(false);
            }
        };

        fetchData();
    }, [url]);

    return { data, isLoading, error };
};
