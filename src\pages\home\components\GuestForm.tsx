import React from 'react';
import FormInput from 'components/Form/FormInput';
import { GuestFormProps } from '../types';

const GuestForm: React.FC<GuestFormProps> = ({ register }) => {
    return (
        <form>
            <FormInput
                readOnly={true}
                label="Mã khách"
                placeholder="Không có thông tin"
                name="maKhach"
                register={register}
            />
            <FormInput readOnly={true} label="Họ tên" placeholder="" name="hoTen" register={register} />
            <FormInput
                label="Số điện thoại"
                placeholder="Không có thông tin"
                name="dienThoai"
                register={register}
                readOnly={true}
            />
            <FormInput
                label="CMND/CCCD"
                placeholder="Không có thông tin"
                name="maDinhDanh"
                register={register}
                readOnly={true}
            />
            <FormInput
                readOnly={true}
                label="Email"
                placeholder="Không có thông tin"
                name="email"
                register={register}
            />
            <FormInput
                label="Địa chỉ"
                placeholder="Không có thông tin"
                name="diaChi"
                register={register}
                readOnly={true}
            />
        </form>
    );
};

export default GuestForm;
