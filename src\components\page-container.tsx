import React, { useEffect } from 'react';

import { clsx } from 'utils/clsx';
import { setNavigationBarColor } from 'zmp-sdk/apis';
import { Page } from 'zmp-ui';

type Props = {
    children: React.ReactNode;
    withHeader?: boolean;
    withBottomNav?: boolean;
    noInsetTop?: boolean;
    noInsetBottom?: boolean;
    dontNeedPaddingTop?: boolean;
    forChatbot?: boolean;
    resetScroll?: boolean;
    forNavbarTV?: boolean;
    className?: string;
};

export function PageContainer({
    children,
    withHeader,
    withBottomNav,
    noInsetTop,
    noInsetBottom,
    dontNeedPaddingTop,
    forChatbot,
    resetScroll = false,
    forNavbarTV,
    className,
}: Props) {
    useEffect(() => {
        setNavigationBarColor({
            textColor: 'black',
            color: '',
            statusBarColor: '#fff',
        });
    }, []);
    return (
        <Page hideScrollbar resetScroll={resetScroll} className={className}>
            <div
                className={clsx(
                    'page-container',
                    withHeader && 'with-header',
                    withBottomNav && 'with-bottom-nav',
                    noInsetTop && 'no-inset-top',
                    noInsetBottom && 'no-inset-bottom',
                    dontNeedPaddingTop && 'dont-need-padding-top',
                    forChatbot && 'for-chatbot',
                    forNavbarTV && 'for-navbar-tv'
                )}
            >
                {children}
            </div>
        </Page>
    );
}
