import { useState, useEffect } from 'react';
import { useRecoilValue } from 'recoil';
import { phieuKetQuaState } from 'state/phieuKetQuaState';
import { PhieuKetQua } from 'apis/getPhieuKetQuaApi';

export interface ZNSNotification {
    phieuKetQua: PhieuKetQua;
    nextNotificationDate: string;
    message: string;
}

export const useZNSNotifications = () => {
    const [currentNotification, setCurrentNotification] = useState<ZNSNotification | null>(null);
    const [isVisible, setIsVisible] = useState(false);
    const phieuKetQua = useRecoilValue(phieuKetQuaState);

    // Helper function để parse ngày từ string dd/mm/yyyy
    const parseDate = (dateStr: string): Date => {
        const [day, month, year] = dateStr.split('/');
        return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
    };

    // Helper function để format ngày thành dd/mm/yyyy
    const formatDate = (date: Date): string => {
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
    };

    // Helper function để tìm ngày gần nhất từ danh sách ngayGuiZNS
    const findNextNotificationDate = (ngayGuiZNS: string): string | null => {
        if (!ngayGuiZNS) return null;

        const today = new Date();
        today.setHours(0, 0, 0, 0); // Reset time để so sánh chỉ ngày

        const dates = ngayGuiZNS.split(';').map(dateStr => dateStr.trim());
        
        // Lọc và sắp xếp các ngày từ hôm nay trở đi
        const futureDates = dates
            .map(dateStr => ({
                original: dateStr,
                parsed: parseDate(dateStr)
            }))
            .filter(({ parsed }) => parsed >= today)
            .sort((a, b) => a.parsed.getTime() - b.parsed.getTime());

        return futureDates.length > 0 ? futureDates[0].original : null;
    };

    // Tạo nội dung thông báo
    const createNotificationMessage = (phieu: PhieuKetQua, nextDate: string): string => {
        return `THÔNG BÁO TÌNH TRẠNG VIỆC LÀM HÀNG THÁNG

Phòng Bảo hiểm thất nghiệp - Trung tâm Dịch vụ việc làm tỉnh Đắk Lắk thông báo:

Họ và tên: ${phieu.hoTen}
Năm sinh: ${phieu.ngaySinh}
Số CCCD: ${phieu.maDinhDanh}

Lịch thông báo việc làm: Từ ngày ${nextDate} đến ngày ${phieu.thoiGianKetThuc}

Đến Trung tâm Dịch vụ việc làm tỉnh Đắk Lắk (09 đường 10/3, phường Tân Lợi, thành phố Buôn Ma Thuột) để thông báo tình trạng việc làm hàng tháng theo quy định được ghi trong phụ lục ban hành kèm Quyết định Hưởng trợ cấp thất nghiệp của Chi cục Việc làm và Bảo hiểm xã hội Trung tâm dịch vụ việc làm tỉnh Đắk Lắk. Nếu Chị không đến thông báo tình trạng Việc làm sẽ bị tạm dừng Trợ cấp thất nghiệp theo quy định.`;
    };

    useEffect(() => {
        if (!phieuKetQua || phieuKetQua.length === 0) {
            setCurrentNotification(null);
            setIsVisible(false);
            return;
        }

        // Tìm phiếu kết quả có ngayGuiZNS và còn hiệu lực
        const validPhieu = phieuKetQua.find(phieu => {
            if (!phieu.ngayGuiZNS) return false;

            // Kiểm tra xem phiếu còn hiệu lực không
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            try {
                const endDateParts = phieu.thoiGianKetThuc.split('-');
                if (endDateParts.length !== 3) return false;

                const endDate = new Date(
                    parseInt(endDateParts[2]), 
                    parseInt(endDateParts[1]) - 1, 
                    parseInt(endDateParts[0])
                );
                endDate.setHours(23, 59, 59, 999);

                return today <= endDate;
            } catch (error) {
                return false;
            }
        });

        if (!validPhieu) {
            setCurrentNotification(null);
            setIsVisible(false);
            return;
        }

        // Tìm ngày thông báo gần nhất
        const nextDate = findNextNotificationDate(validPhieu.ngayGuiZNS!);
        
        if (!nextDate) {
            setCurrentNotification(null);
            setIsVisible(false);
            return;
        }

        // Tạo thông báo
        const notification: ZNSNotification = {
            phieuKetQua: validPhieu,
            nextNotificationDate: nextDate,
            message: createNotificationMessage(validPhieu, nextDate)
        };

        setCurrentNotification(notification);
        
        // Hiển thị thông báo sau 2 giây
        const timer = setTimeout(() => {
            setIsVisible(true);
        }, 2000);

        return () => clearTimeout(timer);
    }, [phieuKetQua]);

    const closeNotification = () => {
        setIsVisible(false);
    };

    const hideNotification = () => {
        setCurrentNotification(null);
        setIsVisible(false);
    };

    return {
        currentNotification,
        isVisible,
        closeNotification,
        hideNotification
    };
};
