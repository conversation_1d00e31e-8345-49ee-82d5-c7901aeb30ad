import { useState, useEffect } from 'react';
import { useRecoilValue } from 'recoil';
import { phieuKetQuaState } from 'state/phieuKetQuaState';
import { PhieuKetQua } from 'apis/getPhieuKetQuaApi';

export interface ZNSNotification {
    phieuKetQua: PhieuKetQua;
    nextNotificationDate: string;
    message: string;
}

export const useZNSNotifications = () => {
    const [currentNotification, setCurrentNotification] = useState<ZNSNotification | null>(null);
    const [isVisible, setIsVisible] = useState(false);
    const phieuKetQua = useRecoilValue(phieuKetQuaState);

    // Helper function để parse ngày từ string (hỗ trợ nhiều format)
    const parseDate = (dateStr: string): Date => {
        // Nếu là YYYY-MM-DD format (2025-06-03)
        if (dateStr.includes('-') && !dateStr.includes('T')) {
            return new Date(dateStr + 'T00:00:00');
        }
        // Nếu là ISO format (2025-06-03T00:00:00)
        if (dateStr.includes('T')) {
            return new Date(dateStr);
        }
        // Nếu là dd/mm/yyyy format
        const [day, month, year] = dateStr.split('/');
        return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
    };

    // Helper function để format ngày thành dd/mm/yyyy
    const formatDate = (date: Date): string => {
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
    };

    // Helper function để tìm ngày gần nhất từ danh sách ngayGuiZNS
    const findNextNotificationDate = (ngayGuiZNS: string[]): string | null => {
        if (!ngayGuiZNS || ngayGuiZNS.length === 0) return null;

        const today = new Date();
        today.setHours(0, 0, 0, 0); // Reset time để so sánh chỉ ngày

        const dates = ngayGuiZNS.map((dateStr) => dateStr.trim());

        // Lọc và sắp xếp các ngày từ hôm nay trở đi
        const futureDates = dates
            .map((dateStr) => ({
                original: dateStr,
                parsed: parseDate(dateStr),
            }))
            .filter(({ parsed }) => parsed >= today)
            .sort((a, b) => a.parsed.getTime() - b.parsed.getTime());

        if (futureDates.length > 0) {
            // Trả về ngày đã format thành dd/mm/yyyy để hiển thị
            return formatDate(futureDates[0].parsed);
        }

        return null;
    };
    const createNotificationMessage = (phieu: PhieuKetQua, nextDate: string): string => {
        return `THÔNG BÁO TÌNH TRẠNG VIỆC LÀM HÀNG THÁNG

Phòng Bảo hiểm thất nghiệp - Trung tâm Dịch vụ việc làm tỉnh Đắk Lắk thông báo:

Họ và tên: ${phieu.hoTen}
Năm sinh: ${phieu.ngaySinh}
Số CCCD: ${phieu.maDinhDanh}

Lịch thông báo việc làm: Từ ngày ${nextDate} đến ngày ${phieu.thoiGianKetThuc}

Đến Trung tâm Dịch vụ việc làm tỉnh Đắk Lắk (09 đường 10/3, phường Tân Lợi, thành phố Buôn Ma Thuột) để thông báo tình trạng việc làm hàng tháng theo quy định được ghi trong phụ lục ban hành kèm Quyết định Hưởng trợ cấp thất nghiệp của Chi cục Việc làm và Bảo hiểm xã hội Trung tâm dịch vụ việc làm tỉnh Đắk Lắk. Nếu Chị không đến thông báo tình trạng Việc làm sẽ bị tạm dừng Trợ cấp thất nghiệp theo quy định.`;
    };

    useEffect(() => {
        console.log('🔍 ZNS Debug - phieuKetQua:', phieuKetQua);

        if (!phieuKetQua || phieuKetQua.length === 0) {
            console.log('❌ ZNS Debug - Không có phieuKetQua');
            setCurrentNotification(null);
            setIsVisible(false);
            return;
        }

        // Kiểm tra xem đã hiển thị ZNS notification chưa
        const hasShownZNS = localStorage.getItem('hasShownZNSNotification');
        console.log('🔍 ZNS Debug - hasShownZNS:', hasShownZNS);

        if (hasShownZNS === 'true') {
            console.log('❌ ZNS Debug - Đã hiển thị rồi, không hiện nữa');
            setCurrentNotification(null);
            setIsVisible(false);
            return;
        }

        // Tìm phiếu kết quả có ngayGuiZNS và còn hiệu lực
        console.log('🔍 ZNS Debug - Tìm phiếu hợp lệ...');

        const validPhieu = phieuKetQua.find((phieu) => {
            console.log('🔍 ZNS Debug - Kiểm tra phiếu:', {
                id: phieu.id,
                hoTen: phieu.hoTen,
                ngayGuiZNS: phieu.ngayGuiZNS,
                thoiGianKetThuc: phieu.thoiGianKetThuc,
            });

            if (!phieu.ngayGuiZNS) {
                console.log('❌ ZNS Debug - Phiếu không có ngayGuiZNS');
                return false;
            }

            // Kiểm tra xem phiếu còn hiệu lực không
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            try {
                const endDateParts = phieu.thoiGianKetThuc.split('-');
                if (endDateParts.length !== 3) {
                    console.log('❌ ZNS Debug - Format ngày kết thúc không đúng');
                    return false;
                }

                const endDate = new Date(
                    parseInt(endDateParts[2]),
                    parseInt(endDateParts[1]) - 1,
                    parseInt(endDateParts[0])
                );
                endDate.setHours(23, 59, 59, 999);

                const isValid = today <= endDate;
                console.log('🔍 ZNS Debug - Kiểm tra hiệu lực:', {
                    today: today.toDateString(),
                    endDate: endDate.toDateString(),
                    isValid,
                });

                return isValid;
            } catch (error) {
                console.log('❌ ZNS Debug - Lỗi parse ngày:', error);
                return false;
            }
        });

        if (!validPhieu) {
            console.log('❌ ZNS Debug - Không tìm thấy phiếu hợp lệ');
            setCurrentNotification(null);
            setIsVisible(false);
            return;
        }

        console.log('✅ ZNS Debug - Tìm thấy phiếu hợp lệ:', validPhieu.hoTen);

        // Tìm ngày thông báo gần nhất
        console.log('🔍 ZNS Debug - ngayGuiZNS:', validPhieu.ngayGuiZNS);
        const nextDate = findNextNotificationDate(validPhieu.ngayGuiZNS!);

        if (!nextDate) {
            console.log('❌ ZNS Debug - Không tìm thấy ngày thông báo gần nhất');
            setCurrentNotification(null);
            setIsVisible(false);
            return;
        }

        console.log('✅ ZNS Debug - Ngày thông báo gần nhất:', nextDate);

        // Tạo thông báo
        const notification: ZNSNotification = {
            phieuKetQua: validPhieu,
            nextNotificationDate: nextDate,
            message: createNotificationMessage(validPhieu, nextDate),
        };

        setCurrentNotification(notification);
        console.log('✅ ZNS Debug - Đã tạo notification, sẽ hiển thị sau 1 giây');

        // Hiển thị thông báo sau 1 giây và đánh dấu đã hiển thị
        const timer = setTimeout(() => {
            console.log('✅ ZNS Debug - Hiển thị thông báo!');
            setIsVisible(true);
            // Lưu trạng thái đã hiển thị vào localStorage
            localStorage.setItem('hasShownZNSNotification', 'true');
        }, 1000);

        return () => clearTimeout(timer);
    }, [phieuKetQua]);

    const closeNotification = () => {
        setIsVisible(false);
    };

    const hideNotification = () => {
        setCurrentNotification(null);
        setIsVisible(false);
    };

    // Function để reset trạng thái đã hiển thị (dùng để test hoặc khi cần hiển thị lại)
    const resetShownStatus = () => {
        localStorage.removeItem('hasShownZNSNotification');
    };

    return {
        currentNotification,
        isVisible,
        closeNotification,
        hideNotification,
        resetShownStatus,
    };
};
