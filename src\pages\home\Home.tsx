import React, { useEffect, useState, useCallback } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { useRecoilValue, useSetRecoilState } from 'recoil';
import { Autoplay, Pagination } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Header, Page } from 'zmp-ui';
import * as yup from 'yup';
import 'swiper/css';

import { memberApi } from 'apis/getMemberApi';
import { phieuKetQuaApi } from 'apis/getPhieuKetQuaApi';
import { isMemberState, userInfoState, userNumberState } from 'state';
import { hasMaDinhDanhState, phieuKetQuaState } from 'state/phieuKetQuaState';
import { heroSlides } from './data/staticData';

import customMessage from 'components/ModalConfirm/customMessage';
import GetUserData from './components/GetUserData';
import StaticData from './components/StaticData';

import { IGuestFromCrm } from 'types/guest.type';
import { IFormInputs } from './types';

import RegisterForm from './components/RegisterForm';
import GuestForm from './components/GuestForm';
import LoadingSkeleton from './components/LoadingSkeleton';

import './Home.scss';
import { Banner1 } from 'assets/pngs';
import JobList from 'pages/home/<USER>/JobList';
import { PageContainer } from 'components/page-container';

const validationSchema = yup
    .object({
        hoTen: yup.string().required('Vui lòng nhập họ tên'),
        email: yup.string(),
        dienThoai: yup
            .string()
            .required('Vui lòng nhập số điện thoại')
            .matches(/^[0-9]{10}$/, 'Số điện thoại không hợp lệ'),
        diaChi: yup.string(),
        maDinhDanh: yup.string(),
    })
    .required();

const Home = () => {
    const userNumber = useRecoilValue(userNumberState);
    const userData = useRecoilValue(userInfoState);

    const { data, isFetching, refetch } = useQuery({
        queryKey: ['member', userNumber],
        queryFn: () => memberApi.getMemberByPhoneNumber(userNumber),
        enabled: !!userNumber,
        refetchOnMount: true,
    });

    const setIsMember = useSetRecoilState(isMemberState);
    const setPhieuKetQua = useSetRecoilState(phieuKetQuaState);
    const setHasMaDinhDanh = useSetRecoilState(hasMaDinhDanhState);
    const setUserInfo = useSetRecoilState(userInfoState);

    useEffect(() => {
        if (data) {
            setIsMember(true);

            localStorage.setItem('userInfo', JSON.stringify(data));
            setUserInfo(data);
        }
    }, [data, setIsMember, setUserInfo]);

    const {
        setValue,
        register,
        handleSubmit,
        formState: { errors },
    } = useForm<IFormInputs>({
        resolver: yupResolver(validationSchema),
    });
    const { register: registerGuest, setValue: setValueGuest } = useForm<IGuestFromCrm>({
        defaultValues: data,
    });

    const mutation = useMutation({
        mutationFn: (data: IFormInputs) => {
            const timestamp = new Date().getTime();
            const maKhach = `MINIAPPMEMBER_${timestamp}`;
            const guestData = {
                guests: [
                    {
                        ...data,
                        maKhach,
                        tags: 'pos',
                        cccd: data.maDinhDanh,
                    },
                ],
            };
            return memberApi.createGuest(guestData);
        },
        onSuccess: (res: any) => {
            if (res.success === '1') {
                customMessage.success('Đăng ký thành công');
                refetch();
            } else {
                customMessage.error('Đăng ký thất bại');
            }
        },
        onError: (error) => {
            console.error('Lỗi:', error);
            customMessage.error('Đăng ký thất bại');
        },
    });

    const onSubmit = (data: IFormInputs) => {
        mutation.mutate(data);
    };

    const fetchPhieuKetQua = useCallback(
        async (maDinhDanh: string) => {
            if (!maDinhDanh || maDinhDanh.length === 0) {
                return;
            }

            try {
                const response = await phieuKetQuaApi.getByMaDinhDanh(maDinhDanh);
                if (response.success === '1' && response.data && response.data.length > 0) {
                    setPhieuKetQua(response.data);
                    setHasMaDinhDanh(true);
                }
            } catch (error) {}
        },
        [setPhieuKetQua, setHasMaDinhDanh]
    );

    useEffect(() => {
        if (data) {
            setValueGuest('maKhach', data.maKhach);
            setValueGuest('hoTen', data.hoTen);
            setValueGuest('email', data.email);
            setValueGuest('dienThoai', data.dienThoai);
            setValueGuest('diaChi', data.diaChi);

            const identificationValue = data.cccd || data.maDinhDanh || '';
            setValueGuest('maDinhDanh', identificationValue);
        }
    }, [data, setValueGuest]);

    useEffect(() => {
        if (data) {
            const identificationValue = String(data.cccd || data.maDinhDanh || '');

            if (identificationValue && identificationValue.length > 0) {
                fetchPhieuKetQua(identificationValue);
            }
        }
    }, [data, fetchPhieuKetQua]);

    useEffect(() => {
        if (userNumber) {
            setValue('dienThoai', userNumber as string);
        }
    }, [userData, userNumber, setValue]);

    const handleMaDinhDanhChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setValue('maDinhDanh', value);

        if (value && value.length > 0) {
            setTimeout(() => {
                fetchPhieuKetQua(value);
            }, 500);
        }
    };

    return (
        <PageContainer className="home" withHeader={false} dontNeedPaddingTop={true} withBottomNav>
            {/* <Header
                showBackIcon={false}
                backgroundColor="#52b361"
                textColor="#ffffff"
                title={
                    (
                        <div className="header-title-ctn">
                            <img src={Banner1} alt="logo" />
                            <div>
                                <h5>Bảo Việt</h5>
                                <p>Member | 0792289045</p>
                            </div>
                        </div>
                    ) as any
                }
                className="no-divider"
            /> */}
            {/* <div
                style={{
                    width: '100%',
                    height: 20,
                    background: '#52b361',
                    borderRadius: '0 0 50px 50px',
                }}
            ></div> */}
            <div className="home__hero">
                <Swiper
                    // onSwiper={(swiper) => setActiveTab(swiper.realIndex)}
                    // onRealIndexChange={(swiper) => setActiveTab(swiper.realIndex)}
                    modules={[Autoplay, Pagination]}
                    autoplay={{
                        delay: 8000,
                        disableOnInteraction: false,
                    }}
                    pagination={{
                        clickable: true,
                        renderBullet: function (_index, className) {
                            return `<span class="${className}"></span>`;
                        },
                    }}
                    loop={true}
                >
                    {heroSlides.map((slide, index) => (
                        <SwiperSlide key={index}>
                            <div className="home__hero-gradient"></div>
                            <div
                                className="home__hero-banner"
                                style={{ backgroundImage: `url(${slide.background})` }}
                            ></div>
                            <div className="home__hero-title">
                                <span>{slide.title}</span>
                                <h1>{(userData as any)?.name ?? 'Quý Khách'}</h1>
                                <p>{slide.message}</p>
                            </div>
                        </SwiperSlide>
                    ))}
                </Swiper>
            </div>
            <div className="home__register home__register-home">
                {isFetching ? (
                    <LoadingSkeleton />
                ) : userNumber ? (
                    <>
                        {!data ? (
                            <>
                                <div className="home__register-warning">
                                    Hiện bạn chưa là thành viên! Hãy đăng ký ngay để xem được thông tin của
                                    mình.
                                </div>
                                <div className="home__register-title">
                                    <h1>Đăng ký thành viên</h1>
                                </div>
                                <div className="home__register-form">
                                    <RegisterForm
                                        onSubmit={handleSubmit(onSubmit)}
                                        register={register}
                                        errors={errors}
                                        onMaDinhDanhChange={handleMaDinhDanhChange}
                                    />
                                </div>
                            </>
                        ) : (
                            <>
                                <div className="home__register-title">
                                    <h1>Thông tin thành viên</h1>
                                </div>
                                <div className="home__register-form">
                                    <GuestForm register={registerGuest} />
                                </div>
                            </>
                        )}
                    </>
                ) : (
                    <GetUserData />
                )}
                <JobList />
                <StaticData />
            </div>
        </PageContainer>
    );
};

export default Home;
