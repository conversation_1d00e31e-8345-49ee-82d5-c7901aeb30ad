import { defineConfig } from 'vite';
import tsconfigPaths from 'vite-tsconfig-paths';
import react from '@vitejs/plugin-react';

// https://vitejs.dev/config/
export default () => {
    return defineConfig({
        root: './src',
        // root: './src',
        base: '',
        plugins: [
            react(),
            tsconfigPaths(),
            {
                name: 'override-config',
                config: () => ({
                    build: {
                        target: 'esnext',
                    },
                }),
            },
        ],
    });
};

// export default defineConfig({
//     plugins: [react(), tsconfigPaths()],
// });
