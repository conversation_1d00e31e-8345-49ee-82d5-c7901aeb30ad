import React from 'react';
import { useCrawlData } from '../../../hooks/useCrawlData';
import { LazyLoadImage } from 'react-lazy-load-image-component';
import 'react-lazy-load-image-component/src/effects/blur.css';
import { useNavigate } from 'react-router-dom';

interface NewsSectionProps {
    type: 'marketReports' | 'centerActivities';
    title: string;
}

const NewsSection: React.FC<NewsSectionProps> = ({ type, title }) => {
    const { data, isLoading } = useCrawlData();
    const navigate = useNavigate();

    const newsItems = data?.[type] || [];

    const handleItemClick = (link: string) => {
        if (link && link !== 'N/A') {
            // Nếu là link nội bộ, sử dụng navigate
            if (link.startsWith('/')) {
                navigate(link);
            } else {
                // Nếu là link ngoài, mở trong tab mới
                window.open(link, '_blank');
            }
        }
    };

    if (isLoading) {
        return (
            <div className="news-section">
                <div className="news-section__header">
                    <span>{title}</span>
                </div>
                <div className="news-section__content">
                    <div className="loading">Đang tải dữ liệu...</div>
                </div>
            </div>
        );
    }

    return (
        <div className="news-section">
            <div className="news-section__header">
                <span>{title}</span>
            </div>
            <div className="news-section__content">
                {newsItems.length > 0 ? (
                    newsItems.map((item, index) => (
                        <div
                            key={`news-${type}-${index}`}
                            className="news-section__item"
                            onClick={() => handleItemClick(item.link)}
                        >
                            <div className="news-section__item__image">
                                <LazyLoadImage
                                    width="100%"
                                    height="100%"
                                    src={item.image}
                                    alt={item.title}
                                    effect="blur"
                                />
                            </div>
                            <div className="news-section__item__content">
                                <h3 className="news-section__item__title">{item.title}</h3>
                                <p className="news-section__item__date">{item.date}</p>
                            </div>
                        </div>
                    ))
                ) : (
                    <div className="no-data">Không có dữ liệu</div>
                )}
            </div>
        </div>
    );
};

export default NewsSection;
