.appointment-notification-modal {
    .zaui-modal-dialog {
        border-radius: 20px;
        overflow: hidden;
        max-width: 92%;
        margin: 0 auto;
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
    }
}

.appointment-notification {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(245, 247, 250, 0.98) 100%);
    border-radius: 20px;
    padding: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;

    &__header {
        text-align: center;
        margin-bottom: 20px;
    }

    &__animation {
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    &__title {
        font-size: 22px;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 8px;
    }

    &__subtitle {
        font-size: 15px;
        color: #555;

        .highlight {
            color: #f96d01;
            font-weight: 700;
            font-size: 18px;
        }
    }

    &__content {
        width: 100%;
        margin-bottom: 10px;
    }

    &__name {
        font-size: 18px;
        font-weight: 600;
        color: #222;
        margin-bottom: 15px;
        text-align: center;
        padding: 0 10px;
    }

    &__info {
        background-color: rgba(255, 255, 255, 0.8);
        border-radius: 16px;
        padding: 16px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
        border: 1px solid rgba(33, 57, 130, 0.1);
    }

    &__info-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 12px;

        &:last-child {
            margin-bottom: 0;
        }

        &--notes {
            background-color: rgba(33, 150, 243, 0.08);
            padding: 10px;
            border-radius: 8px;
            margin-top: 8px;
        }
    }

    &__info-icon {
        color: var(--primary-color);
        font-size: 16px;
        margin-right: 12px;
        margin-top: 2px;
        flex-shrink: 0;
    }

    &__pagination {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 15px;
        width: 100%;
    }

    &__pagination-text {
        font-size: 14px;
        font-weight: 600;
        color: #555;
        margin: 0 15px;
    }

    &__nav-button {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f0f2f5;
        border: 1px solid #ddd;
        color: #555;
        cursor: pointer;
        transition: all 0.2s ease;

        &:active {
            background-color: #e5e7ea;
            transform: scale(0.95);
        }

        &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
    }

    &__actions {
        display: flex;
        justify-content: space-between;
        width: 100%;
        gap: 12px;
        margin-top: 5px;
    }

    &__button {
        flex: 1;
        padding: 14px;
        border-radius: 12px;
        font-weight: 600;
        font-size: 15px;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        &--primary {
            background-color: var(--primary-color);
            color: white;

            &:active {
                transform: scale(0.98);
                box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
            }
        }

        &--secondary {
            background-color: #f0f2f5;
            color: #444;
            border: 1px solid #ddd;

            &:active {
                background-color: #e5e7ea;
            }
        }
    }
}
