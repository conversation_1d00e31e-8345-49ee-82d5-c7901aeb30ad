import axios, { AxiosError, AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import API_URL from 'constants/API_URL';

const onRequest = (config: InternalAxiosRequestConfig<any>): InternalAxiosRequestConfig<any> => {
    return config;
};

const onRequestError = (error: AxiosError): Promise<AxiosError> => {
    return Promise.reject(error);
};

const onResponse = (response: AxiosResponse): AxiosResponse => {
    return response.data;
};

const onResponseError = (error: AxiosError): Promise<AxiosError> => {
    return Promise.reject(error);
};

function setupInterceptorsTo(axiosInstance: AxiosInstance): AxiosInstance {
    axiosInstance.interceptors.request.use(onRequest, onRequestError);
    axiosInstance.interceptors.response.use(onResponse, onResponseError);
    return axiosInstance;
}

const axiosInstance = axios.create({
    baseURL: import.meta.env.VITE_MAIN_API_URL,
    timeout: 20000,
    headers: {
        'Content-Type': 'application/json',
        Authorization:
            'Bearer ' +
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiIzODYwZWM5ZS02Njc1LTRmZWQtYTRhYi1mNWQwNTE3OGNmZTEiLCJVc2VybmFtZSI6IndlYmFwcCIsIlR5cGUiOiJBVVRIIiwiZXhwIjoxNzY0MjM3OTI4LCJpc3MiOiJPbmV4dXMifQ.L4wNaTJiKnpt8uXaGOlOuc6OUY5SAeQfd_FdLi0VV6o',
        // BranchId: '2427087d-d840-4c53-a3f9-3d09e9b8e01f',
        // CustomerId: '56',
    },
});

axiosInstance.interceptors.request.use((config) => {
    if (config.method === 'post' && config.url === API_URL.getOtpCode) {
        config.headers['Content-Type'] = 'multipart/form-data';
    }
    return config;
});

export const axiosClient = setupInterceptorsTo(axiosInstance);
