import { atom } from 'recoil';
import { PhieuKetQua } from 'apis/getPhieuKetQuaApi';

export const appointmentNotificationState = atom<{
    appointment: PhieuKetQua | null;
    appointments: PhieuKetQua[];
    currentIndex: number;
    isVisible: boolean;
}>({
    key: 'appointmentNotificationState',
    default: {
        appointment: null,
        appointments: [],
        currentIndex: 0,
        isVisible: false,
    },
});

const sessionKey = 'hasShownNotification_' + new Date().toDateString();
export const hasShownNotificationState = atom<boolean>({
    key: 'hasShownNotificationState',
    default: false,
});
