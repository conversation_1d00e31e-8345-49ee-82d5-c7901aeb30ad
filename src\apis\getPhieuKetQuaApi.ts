import { axiosClient } from './axiosClient';

export interface PhieuKetQuaResponse {
    success: string;
    data: PhieuKetQua[];
}

export interface PhieuKetQua {
    id: number;
    idKhach: number;
    hoTen: string;
    maDinhDanh: string;
    ngaySinh: string;
    ngayCap: string;
    soBHXH: string | null;
    diaChi: string;
    taiKhoan: string;
    nganHang: string;
    tongSoThangDongBHXH: number | null;
    mucTroCapHangThang: number | null;
    soThangDuocHuongTroCap: number | null;
    noiNhanTroCap: string;
    thoiGianBatDau: string;
    thoiGianKetThuc: string;
    ngayTao: string;
    ngayCapNhat: string;
    ngayGuiZNS?: string[]; // Thêm trường mới: ["18/06/2025","21/06/2025","26/06/2025"]
}

// Helper function để tạo fake ngayGuiZNS với format ISO date
const generateFakeNgayGuiZNS = (): string[] => {
    const today = new Date();
    const dates: string[] = [];

    // Tạo ngày hôm nay và 2 ngày trong tương lai để test
    const futureDays = [0, 3, 7]; // Hôm nay, 3 ngày nữa, 7 ngày nữa

    futureDays.forEach((days) => {
        const futureDate = new Date(today);
        futureDate.setDate(today.getDate() + days);
        futureDate.setHours(0, 0, 0, 0); // Set time to 00:00:00

        // Format thành ISO string: 2025-06-03T00:00:00
        dates.push(futureDate.toISOString().split('.')[0]); // Bỏ milliseconds
    });

    return dates; // Trả về array với format ISO date
};

export const phieuKetQuaApi = {
    getByMaDinhDanh: async (maDinhDanh: string): Promise<PhieuKetQuaResponse> => {
        const maDinhDanhStr = String(maDinhDanh);
        const url = `/api/Job/PhieuKetQua/GetByMaDinhDanh?MaDinhDanh=${maDinhDanhStr}`;
        try {
            const response = (await axiosClient.get(url)) as PhieuKetQuaResponse;

            // Fake data ngayGuiZNS cho mỗi item trong response
            if (response.success === '1' && response.data && response.data.length > 0) {
                response.data = response.data.map((item) => ({
                    ...item,
                    ngayGuiZNS: generateFakeNgayGuiZNS(),
                }));
            }

            return response;
        } catch (error) {
            throw error;
        }
    },
};
