import { axiosClient } from './axiosClient';

export interface PhieuKetQuaResponse {
    success: string;
    data: PhieuKetQua[];
}

export interface PhieuKetQua {
    id: number;
    idKhach: number;
    hoTen: string;
    maDinhDanh: string;
    ngaySinh: string;
    ngayCap: string;
    soBHXH: string | null;
    diaChi: string;
    taiKhoan: string;
    nganHang: string;
    tongSoThangDongBHXH: number | null;
    mucTroCapHangThang: number | null;
    soThangDuocHuongTroCap: number | null;
    noiNhanTroCap: string;
    thoiGianBatDau: string;
    thoiGianKetThuc: string;
    ngayTao: string;
    ngayCapNhat: string;
    ngayGuiZNS?: string; // Thêm trường mới: "18/06/2025;21/06/2025;26/06/2025"
}

// Helper function để tạo fake ngayGuiZNS
const generateFakeNgayGuiZNS = (): string => {
    const today = new Date();
    const dates: string[] = [];

    // Tạo 3 ngày trong tương lai (18, 21, 26 ngày từ hôm nay)
    const futureDays = [18, 21, 26];

    futureDays.forEach((days) => {
        const futureDate = new Date(today);
        futureDate.setDate(today.getDate() + days);

        const day = futureDate.getDate().toString().padStart(2, '0');
        const month = (futureDate.getMonth() + 1).toString().padStart(2, '0');
        const year = futureDate.getFullYear();

        dates.push(`${day}/${month}/${year}`);
    });

    return dates.join(';');
};

export const phieuKetQuaApi = {
    getByMaDinhDanh: async (maDinhDanh: string): Promise<PhieuKetQuaResponse> => {
        const maDinhDanhStr = String(maDinhDanh);
        const url = `/api/CRM/PhieuKetQua/GetByMaDinhDanh?MaDinhDanh=${maDinhDanhStr}`;
        try {
            const response = (await axiosClient.get(url)) as PhieuKetQuaResponse;

            // Fake data ngayGuiZNS cho mỗi item trong response
            if (response.success === '1' && response.data && response.data.length > 0) {
                response.data = response.data;
            }

            return response;
        } catch (error) {
            throw error;
        }
    },
};
