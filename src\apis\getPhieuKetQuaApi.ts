import { axiosClient } from './axiosClient';

export interface PhieuKetQuaResponse {
    success: string;
    data: PhieuKetQua[];
}

export interface PhieuKetQua {
    id: number;
    idKhach: number;
    hoTen: string;
    maDinhDanh: string;
    ngaySinh: string;
    ngayCap: string;
    soBHXH: string | null;
    diaChi: string;
    taiKhoan: string;
    nganHang: string;
    tongSoThangDongBHXH: number | null;
    mucTroCapHangThang: number | null;
    soThangDuocHuongTroCap: number | null;
    noiNhanTroCap: string;
    thoiGianBatDau: string;
    thoiGianKetThuc: string;
    ngayTao: string;
    ngayCapNhat: string;
    ngayGuiZNS?: string[]; // Thêm trường mới: ["18/06/2025","21/06/2025","26/06/2025"]
}

// Helper function để tạo fake ngayGuiZNS với format ISO date
const generateFakeNgayGuiZNS = (): string[] => {
    // Tạo đúng như ví dụ của bạn: hôm nay 3/6, c<PERSON> lịch 3/6, 5/6, 9/6
    return [
        '2025-06-03', // Hôm nay
        '2025-06-05', // 2 ngày nữa
        '2025-06-09', // 6 ngày nữa
    ];
};

export const phieuKetQuaApi = {
    getByMaDinhDanh: async (maDinhDanh: string): Promise<PhieuKetQuaResponse> => {
        const maDinhDanhStr = String(maDinhDanh);
        const url = `/api/Job/PhieuKetQua/GetByMaDinhDanh?MaDinhDanh=${maDinhDanhStr}`;
        try {
            const response = (await axiosClient.get(url)) as PhieuKetQuaResponse;

            // Fake data ngayGuiZNS cho mỗi item trong response
            if (response.success === '1' && response.data && response.data.length > 0) {
                response.data = response.data.map((item) => ({
                    ...item,
                    ngayGuiZNS: generateFakeNgayGuiZNS(),
                }));
            }

            return response;
        } catch (error) {
            throw error;
        }
    },
};
