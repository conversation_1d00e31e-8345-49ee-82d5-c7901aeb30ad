import { axiosClient } from './axiosClient';

export interface PhieuKetQuaResponse {
    success: string;
    data: PhieuKetQua[];
}

export interface PhieuKetQua {
    id: number;
    idKhach: number;
    hoTen: string;
    maDinhDanh: string;
    ngaySinh: string;
    ngayCap: string;
    soBHXH: string | null;
    diaChi: string;
    taiKhoan: string;
    nganHang: string;
    tongSoThangDongBHXH: number | null;
    mucTroCapHangThang: number | null;
    soThangDuocHuongTroCap: number | null;
    noiNhanTroCap: string;
    thoiGianBatDau: string;
    thoiGianKetThuc: string;
    ngayTao: string;
    ngayCapNhat: string;
}

export const phieuKetQuaApi = {
    getByMaDinhDanh: async (maDinhDanh: string): Promise<PhieuKetQuaResponse> => {
        const maDinhDanhStr = String(maDinhDanh);
        const url = `/api/CRM/PhieuKetQua/GetByMaDinhDanh?MaDinhDanh=${maDinhDanhStr}`;
        try {
            const response = (await axiosClient.get(url)) as PhieuKetQuaResponse;
            return response;
        } catch (error) {
            throw error;
        }
    },
};
