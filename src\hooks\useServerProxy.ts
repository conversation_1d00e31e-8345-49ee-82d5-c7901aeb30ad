import axios from 'axios';

// Server proxy configuration
const SERVER_PROXY_URL = 'http://localhost:3001/proxy';

/**
 * Fetches data from a URL using a local server proxy
 * This approach avoids CORS issues and country blocking
 *
 * @param url The URL to fetch data from
 * @returns The HTML content from the URL
 */
export const fetchWithServerProxy = async (url: string): Promise<string> => {
    try {
        // First check if the server is running
        try {
            await axios.get('http://localhost:3001/health', { timeout: 2000 });
        } catch (healthError) {
            console.error('Proxy server is not running. Please start the backend server first.');
            throw new Error('Proxy server is not running. Please start the backend server first.');
        }

        // If server is running, proceed with the request
        const response = await axios.get(SERVER_PROXY_URL, {
            params: { url },
            timeout: 15000, // 15 seconds timeout
        });

        return response.data;
    } catch (error) {
        console.error('Error fetching with server proxy:', error);
        throw error;
    }
};
