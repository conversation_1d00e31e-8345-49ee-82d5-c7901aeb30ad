.zns-notification-modal {
    .zm-modal-content {
        max-height: 90vh;
        overflow-y: auto;
    }
}

.zns-notification-content {
    padding: 0;

    .zns-header {
        display: flex;
        align-items: center;
        padding: 16px;
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        color: white;
        border-radius: 8px 8px 0 0;
        margin: -16px -16px 16px -16px;

        .zns-logo {
            margin-right: 12px;

            .logo-placeholder {
                width: 48px;
                height: 48px;
                border-radius: 50%;
                background: white;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 24px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }
        }

        .zns-title {
            flex: 1;

            h3 {
                margin: 0;
                font-size: 14px;
                font-weight: 600;
                line-height: 1.2;
                text-align: center;
            }
        }
    }

    .zns-message {
        padding: 0 16px 16px;

        .message-text {
            font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 13px;
            line-height: 1.5;
            color: #333;
            white-space: pre-wrap;
            word-wrap: break-word;
            margin: 0;
            background: #f8f9fa;
            padding: 16px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
    }

    .zns-actions {
        padding: 0 16px 16px;

        .contact-btn {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            padding: 12px;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
            transition: all 0.3s ease;

            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 16px rgba(0, 123, 255, 0.4);
            }

            &:active {
                transform: translateY(0);
            }
        }
    }
}

// Responsive design
@media (max-width: 480px) {
    .zns-notification-content {
        .zns-header {
            padding: 12px;

            .zns-logo .logo-placeholder {
                width: 40px;
                height: 40px;
                font-size: 20px;
            }

            .zns-title h3 {
                font-size: 12px;
            }
        }

        .zns-message .message-text {
            font-size: 12px;
            padding: 12px;
        }

        .zns-actions .contact-btn {
            font-size: 14px;
            padding: 10px;
        }
    }
}
