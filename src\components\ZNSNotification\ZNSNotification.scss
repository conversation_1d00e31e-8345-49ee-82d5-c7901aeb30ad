.zns-notification-modal {
    .zm-modal-content {
        max-height: 95vh;
        overflow-y: auto;
        border-radius: 16px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .zm-modal-mask {
        background: rgba(0, 0, 0, 0.6);
        backdrop-filter: blur(4px);
    }
}

.zns-notification-content {
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

    .zns-header {
        display: flex;
        align-items: center;
        padding: 20px 16px;
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        color: white;
        border-radius: 16px 16px 0 0;
        margin: -16px -16px 0 -16px;
        position: relative;
        overflow: hidden;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .zns-logo {
            margin-right: 16px;
            z-index: 1;

            .logo-placeholder {
                width: 56px;
                height: 56px;
                border-radius: 50%;
                background: white;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 28px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                border: 3px solid rgba(255, 255, 255, 0.9);
            }
        }

        .zns-title {
            flex: 1;
            z-index: 1;

            .title-main {
                font-size: 16px;
                font-weight: 700;
                line-height: 1.2;
                text-align: center;
                margin-bottom: 4px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }

            .title-sub {
                font-size: 14px;
                font-weight: 600;
                line-height: 1.2;
                text-align: center;
                opacity: 0.95;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
        }
    }

    .zns-notification-title {
        padding: 20px 16px 16px;
        text-align: center;

        h2 {
            margin: 0;
            font-size: 18px;
            font-weight: 700;
            color: #1e3c72;
            line-height: 1.3;
        }
    }

    .zns-personal-info {
        padding: 0 16px 20px;
        background: #f8f9fa;
        margin: 0 16px 20px;
        border-radius: 12px;
        border: 1px solid #e9ecef;

        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 12px 16px;
            border-bottom: 1px solid #e9ecef;

            &:last-child {
                border-bottom: none;
            }

            .label {
                font-weight: 600;
                color: #495057;
                font-size: 14px;
                min-width: 120px;
                flex-shrink: 0;
            }

            .value {
                font-weight: 500;
                color: #212529;
                font-size: 14px;
                text-align: right;
                flex: 1;
                margin-left: 12px;
            }
        }
    }

    .zns-main-content {
        padding: 0 16px 20px;

        p {
            font-size: 15px;
            line-height: 1.6;
            color: #495057;
            margin: 0 0 16px 0;
            text-align: justify;
        }

        .warning-box {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 16px;
            font-size: 14px;
            line-height: 1.5;
            color: #856404;
            box-shadow: 0 2px 8px rgba(255, 193, 7, 0.2);
        }
    }

    .zns-actions {
        padding: 0 16px 20px;

        .contact-btn {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            border: none;
            border-radius: 12px;
            font-weight: 700;
            font-size: 16px;
            padding: 16px;
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                transition: left 0.5s;
            }

            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);

                &::before {
                    left: 100%;
                }
            }

            &:active {
                transform: translateY(0);
            }
        }
    }
}

// Responsive design
@media (max-width: 480px) {
    .zns-notification-modal {
        .zm-modal-content {
            margin: 10px;
            max-height: calc(100vh - 20px);
        }
    }

    .zns-notification-content {
        .zns-header {
            padding: 16px 12px;

            .zns-logo .logo-placeholder {
                width: 48px;
                height: 48px;
                font-size: 24px;
            }

            .zns-title {
                .title-main {
                    font-size: 14px;
                }

                .title-sub {
                    font-size: 12px;
                }
            }
        }

        .zns-notification-title {
            padding: 16px 12px 12px;

            h2 {
                font-size: 16px;
            }
        }

        .zns-personal-info {
            margin: 0 12px 16px;
            padding: 0 12px 16px;

            .info-row {
                flex-direction: column;
                align-items: flex-start;
                padding: 10px 12px;

                .label {
                    min-width: auto;
                    margin-bottom: 4px;
                    font-size: 13px;
                }

                .value {
                    text-align: left;
                    margin-left: 0;
                    font-size: 13px;
                    font-weight: 600;
                    color: #007bff;
                }
            }
        }

        .zns-main-content {
            padding: 0 12px 16px;

            p {
                font-size: 14px;
            }

            .warning-box {
                font-size: 13px;
                padding: 12px;
            }
        }

        .zns-actions {
            padding: 0 12px 16px;

            .contact-btn {
                font-size: 15px;
                padding: 14px;
            }
        }
    }
}

// Extra small devices
@media (max-width: 360px) {
    .zns-notification-content {
        .zns-header {
            .zns-logo .logo-placeholder {
                width: 44px;
                height: 44px;
                font-size: 22px;
            }

            .zns-title {
                .title-main {
                    font-size: 13px;
                }

                .title-sub {
                    font-size: 11px;
                }
            }
        }

        .zns-notification-title h2 {
            font-size: 15px;
        }

        .zns-personal-info .info-row {
            .label,
            .value {
                font-size: 12px;
            }
        }

        .zns-main-content {
            p {
                font-size: 13px;
            }

            .warning-box {
                font-size: 12px;
            }
        }
    }
}
