.job-registration-page {
    padding: 16px;
    background-color: #f5f7fa;
    min-height: 100vh;

    .get-user-data-container {
        // margin-top: 20px;
    }

    .job-registration-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        gap: 12px;

        h1 {
            font-size: 20px;
            font-weight: 600;
            margin: 0;
            flex: 1;
        }

        .back-button {
            background: none;
            color: #333;
            padding: 8px;
            min-width: auto;
            height: auto;
        }
    }

    .job-summary {
        background-color: white;
        border-radius: 12px;
        padding: 16px;
        margin-bottom: 20px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

        h2 {
            font-size: 18px;
            font-weight: 600;
            margin: 0 0 12px 0;
            color: #213982;
        }

        .job-info-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            color: #4b5563;
            font-size: 14px;

            .job-icon {
                margin-right: 8px;
                color: #6b7280;
            }
        }
    }

    .registration-form {
        background-color: white;
        border-radius: 12px;
        padding: 16px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        position: relative;

        .form-group {
            margin-bottom: 16px;
        }

        .required-field {
            color: #ef4444;
            margin-left: 2px;
        }

        .submit-button {
            width: 100%;
            margin-top: 20px;
            background-color: #213982;

            &:hover {
                background-color: #1a2d69;
            }

            &:disabled {
                opacity: 0.7;
            }
        }

        .form-submitting-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 10;
            border-radius: 12px;

            p {
                margin-top: 16px;
                color: #213982;
                font-weight: 500;
            }

            .loading-spinner {
                width: 40px;
                height: 40px;
                border: 4px solid rgba(33, 57, 130, 0.2);
                border-radius: 50%;
                border-top-color: #213982;
                animation: spin 1s ease-in-out infinite;
            }
        }
    }

    .loading-container,
    .error-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 60px 20px;
        text-align: center;
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        margin-top: 20px;

        p {
            margin-top: 16px;
            color: #4b5563;
            font-size: 15px;
        }
    }

    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(33, 57, 130, 0.2);
        border-radius: 50%;
        border-top-color: #213982;
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to {
            transform: rotate(360deg);
        }
    }
}
