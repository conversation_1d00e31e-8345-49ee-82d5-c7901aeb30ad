import React from 'react';
import { useZNSNotifications } from 'hooks/useZNSNotifications';

// Component này chỉ dùng để test trong development
// <PERSON><PERSON> thể xóa khi deploy production
const ZNSTestButton: React.FC = () => {
    const { resetShownStatus } = useZNSNotifications();

    // Chỉ hiển thị trong development mode
    if (process.env.NODE_ENV === 'production') {
        return null;
    }

    const handleReset = () => {
        resetShownStatus();
        console.log('🔄 Reset ZNS sessionStorage');
        // Reload trang để test lại
        setTimeout(() => {
            window.location.reload();
        }, 500);
    };

    const handleClearSession = () => {
        sessionStorage.clear();
        console.log('🧹 Cleared all sessionStorage');
        alert('Đã xóa sessionStorage. Refresh để test!');
    };

    return (
        <div
            style={{
                position: 'fixed',
                bottom: '80px',
                right: '20px',
                zIndex: 9999,
                display: 'flex',
                flexDirection: 'column',
                gap: '10px',
            }}
        >
            {/* Reset button */}
            <div
                style={{
                    background: 'linear-gradient(135deg, #ff4444, #cc3333)',
                    color: 'white',
                    padding: '10px 16px',
                    borderRadius: '20px',
                    fontSize: '12px',
                    fontWeight: '600',
                    cursor: 'pointer',
                    boxShadow: '0 4px 12px rgba(255, 68, 68, 0.3)',
                    border: '2px solid rgba(255, 255, 255, 0.2)',
                    transition: 'all 0.3s ease',
                    textAlign: 'center',
                }}
                onClick={handleReset}
                onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'translateY(-2px)';
                    e.currentTarget.style.boxShadow = '0 6px 16px rgba(255, 68, 68, 0.4)';
                }}
                onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(255, 68, 68, 0.3)';
                }}
            >
                🔄 Reset ZNS
            </div>

            {/* Clear session button */}
            <div
                style={{
                    background: 'linear-gradient(135deg, #4444ff, #3333cc)',
                    color: 'white',
                    padding: '8px 12px',
                    borderRadius: '16px',
                    fontSize: '11px',
                    fontWeight: '600',
                    cursor: 'pointer',
                    boxShadow: '0 4px 12px rgba(68, 68, 255, 0.3)',
                    border: '2px solid rgba(255, 255, 255, 0.2)',
                    transition: 'all 0.3s ease',
                    textAlign: 'center',
                }}
                onClick={handleClearSession}
                onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'translateY(-2px)';
                    e.currentTarget.style.boxShadow = '0 6px 16px rgba(68, 68, 255, 0.4)';
                }}
                onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(68, 68, 255, 0.3)';
                }}
            >
                🧹 Clear Session
            </div>
        </div>
    );
};

export default ZNSTestButton;
