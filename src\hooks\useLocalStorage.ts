import { useState } from 'react';

export const useLocalStorage = (key = '', initialValue = '' as any) => {
    const [state, setState] = useState(() => {
        try {
            const item = window.localStorage.getItem(key);
            return item ? JSON.parse(item) : initialValue;
        } catch (error) {
            return initialValue;
        }
    });
    const setLocalStorageState = (newState) => {
        try {
            const newStateValue = typeof newState === 'function' ? newState(state) : newState;
            window.localStorage.setItem(key, JSON.stringify(newStateValue));
            setState(newStateValue);
        } catch (error) {}
    };
    return [state, setLocalStorageState];
};
