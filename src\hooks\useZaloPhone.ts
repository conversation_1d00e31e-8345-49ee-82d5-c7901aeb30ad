import { useState } from 'react';
import { getPhoneNumber } from 'zmp-sdk/apis';
import { getZaloUserInfo } from 'apis/zaloApi';
import { formatPhoneNumber } from 'utils/common';
import { useRecoilValue, useSetRecoilState } from 'recoil';
import { userNumberState, zaloAccessTokenState } from 'state';

interface UseZaloPhoneProps {
    onSuccess?: (phoneNumber: string) => void;
    onError?: (error: any) => void;
}

export const useZaloPhone = ({ onSuccess, onError }: UseZaloPhoneProps = {}) => {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<any>(null);
    const zaloAccessToken = useRecoilValue(zaloAccessTokenState);
    const userNumber = useRecoilValue(userNumberState);
    const getZaloPhoneNumber = async () => {
        setError(null);
        if (userNumber) {
            onSuccess?.(userNumber);
            return;
        }
        setIsLoading(true);
        try {
            const result = await getPhoneNumber({
                success: async ({ token }) => {
                    try {
                        const phoneNumberRes = await getZaloUserInfo(zaloAccessToken, token ?? '');
                        const formattedPhone = formatPhoneNumber.toNational(
                            phoneNumberRes?.data?.number ?? ''
                        );

                        onSuccess?.(formattedPhone);
                        return formattedPhone;
                    } catch (err) {
                        throw err;
                    } finally {
                        setIsLoading(false);
                    }
                },
                fail: (error) => {
                    throw error;
                },
            });

            return result;
        } catch (err) {
            setError(err);
            onError?.(err);
            console.error('Error getting Zalo phone number:', err);
            return null;
        } finally {
            setIsLoading(false);
        }
    };

    return {
        getZaloPhoneNumber,
        isLoading,
        error,
    };
};
