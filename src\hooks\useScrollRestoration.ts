import { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';

interface ScrollPositions {
    [key: string]: number;
}

// Lấy dữ liệu vị trí scroll từ sessionStorage hoặc tạo mới nếu chưa có
const getScrollPositions = (): ScrollPositions => {
    const savedPositions = sessionStorage.getItem('scrollPositions');
    return savedPositions ? JSON.parse(savedPositions) : {};
};

// Lưu trữ vị trí scroll cho mỗi đường dẫn
let scrollPositions: ScrollPositions = getScrollPositions();

// Lưu vị trí scroll vào sessionStorage
const saveScrollPositions = () => {
    sessionStorage.setItem('scrollPositions', JSON.stringify(scrollPositions));
};

export const useScrollRestoration = () => {
    const location = useLocation();
    const [isInitialized, setIsInitialized] = useState(false);

    // Tạo key duy nhất cho mỗi đường dẫn
    const getScrollKey = () => {
        // Sử dụng pathname + search để phân biệt các trang có query params
        return `${location.pathname}${location.search}`;
    };

    // Khởi tạo scrollPositions từ sessionStorage
    useEffect(() => {
        if (!isInitialized) {
            scrollPositions = getScrollPositions();
            setIsInitialized(true);
        }
    }, [isInitialized]);

    // Lưu vị trí scroll khi rời khỏi trang
    useEffect(() => {
        const scrollKey = getScrollKey();

        // Lưu vị trí scroll khi component unmount
        return () => {
            console.log(`Saving scroll position for ${scrollKey}: ${window.scrollY}`);
            scrollPositions[scrollKey] = window.scrollY;
            saveScrollPositions();
        };
    }, [location]);

    // Khôi phục vị trí scroll khi quay lại trang
    useEffect(() => {
        const scrollKey = getScrollKey();

        // Sử dụng setTimeout để đảm bảo DOM đã được render
        const timeoutId = setTimeout(() => {
            if (scrollPositions[scrollKey] !== undefined) {
                console.log(`Restoring scroll position for ${scrollKey}: ${scrollPositions[scrollKey]}`);
                window.scrollTo({
                    top: scrollPositions[scrollKey],
                    behavior: 'auto',
                });
            } else {
                // Nếu không có vị trí scroll được lưu, scroll lên đầu trang
                console.log(`No saved scroll position for ${scrollKey}, scrolling to top`);
                window.scrollTo({
                    top: 0,
                    behavior: 'auto',
                });
            }
        }, 300); // Đợi 300ms để đảm bảo DOM đã được render

        return () => clearTimeout(timeoutId);
    }, [location]);

    // Lưu vị trí scroll khi người dùng rời khỏi trang
    useEffect(() => {
        const handleBeforeUnload = () => {
            const scrollKey = getScrollKey();
            console.log(`Saving scroll position before unload for ${scrollKey}: ${window.scrollY}`);
            scrollPositions[scrollKey] = window.scrollY;
            saveScrollPositions();
        };

        // Lưu vị trí scroll khi người dùng cuộn trang
        const handleScroll = () => {
            const scrollKey = getScrollKey();
            scrollPositions[scrollKey] = window.scrollY;
            // Không lưu vào sessionStorage mỗi lần cuộn để tránh hiệu suất kém
        };

        window.addEventListener('beforeunload', handleBeforeUnload);
        window.addEventListener('scroll', handleScroll, { passive: true });

        // Lưu vị trí scroll mỗi 2 giây
        const intervalId = setInterval(() => {
            saveScrollPositions();
        }, 2000);

        return () => {
            window.removeEventListener('beforeunload', handleBeforeUnload);
            window.removeEventListener('scroll', handleScroll);
            clearInterval(intervalId);
        };
    }, [location]);

    return null;
};
