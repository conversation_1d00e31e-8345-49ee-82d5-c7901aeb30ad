import axios from 'axios';

export const getZaloUserInfo = async (accessToken: string, code: string) => {
    try {
        const response = await axios({
            method: 'GET',
            url: 'https://graph.zalo.me/v2.0/me/info',
            headers: {
                access_token: accessToken,
                code: code,
                secret_key: import.meta.env.VITE_ZALO_SECRET_KEY,
            },
        });
        return response.data;
    } catch (error) {
        console.error('Error fetching Zalo user info:', error);
        throw error;
    }
};
