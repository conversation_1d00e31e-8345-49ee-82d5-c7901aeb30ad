import { axiosClient } from './axiosClient';

export interface TuVanViecLamItem {
    id: number;
    soDienThoai: string;
    hoTen?: string;
    diaChi?: string;
    ngayTao?: string;
    ngayCapNhat?: string;
    ghiChu?: string;
    trangThai?: number;
    viTriCanTimCongViec?: string;
    mucLuongMongMuon?: string;
    kinhNghiem?: string;
    trinhDoHocVan?: string;
    khuVucLamViec?: string;
    gioiTinh?: string;
    ngaySinh?: string;
    soQuyetDinh?: string;
    ngayCapQD?: string;
    trinhDo?: string;
    congViecDaLam?: string;
    stt?: number;
}

export interface TuVanViecLamResponse {
    success: string;
    message?: string;
    data: TuVanViecLamItem[];
}

export const tuVanViecLamApi = {
    getBySoDienThoai: async (soDienThoai: string): Promise<TuVanViecLamResponse> => {
        try {
            const response = await axiosClient.get(`/api/Job/TuVanViecLam/GetBySoDienThoai/${soDienThoai}`);
            return response;
        } catch (error) {
            throw error;
        }
    },
};
