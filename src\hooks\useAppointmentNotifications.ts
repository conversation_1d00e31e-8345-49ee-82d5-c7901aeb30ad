import { useState, useEffect } from 'react';
import { useRecoilValue } from 'recoil';
import { sortedAppointmentsState } from 'state/appointmentState';
import { PhieuKetQua } from 'apis/getPhieuKetQuaApi';

export const useAppointmentNotifications = () => {
    const [currentNotification, setCurrentNotification] = useState<PhieuKetQua | null>(null);
    const [isNotificationVisible, setIsNotificationVisible] = useState(false);
    const appointments = useRecoilValue(sortedAppointmentsState);

    useEffect(() => {
        if (!appointments || appointments.length === 0) {
            return;
        }

        const today = new Date();

        const upcomingAppointments = appointments.filter((appointment) => {
            try {
                if (!appointment.thoiGianBatDau || !appointment.thoiGianKetThuc) {
                    return false;
                }

                const endParts = appointment.thoiGianKetThuc.split('-');

                if (endParts.length !== 3) {
                    return false;
                }

                const endDate = new Date(`${endParts[2]}-${endParts[1]}-${endParts[0]}`);

                return today <= endDate;
            } catch (error) {
                return false;
            }
        });

        if (upcomingAppointments.length > 0) {
            upcomingAppointments.sort((a, b) => {
                const aStartParts = a.thoiGianBatDau.split('-');
                const bStartParts = b.thoiGianBatDau.split('-');

                const aStartDate = new Date(`${aStartParts[2]}-${aStartParts[1]}-${aStartParts[0]}`);
                const bStartDate = new Date(`${bStartParts[2]}-${bStartParts[1]}-${bStartParts[0]}`);

                return aStartDate.getTime() - bStartDate.getTime();
            });

            const appointment = upcomingAppointments[0];

            const timer = setTimeout(() => {
                setCurrentNotification(appointment);
                setIsNotificationVisible(true);
            }, 1500);

            return () => clearTimeout(timer);
        }
    }, [appointments]);

    const closeNotification = () => {
        setIsNotificationVisible(false);
    };

    return {
        currentNotification,
        isNotificationVisible,
        closeNotification,
    };
};
