import { Appointment, AppointmentFormData, AppointmentStatus, AppointmentType } from 'types/appointment.type';
import { v4 as uuidv4 } from 'uuid';

// Mock data for appointments
const mockAppointments: Appointment[] = [
    {
        id: '1',
        title: '<PERSON><PERSON><PERSON> hồ sơ trợ cấp thất nghiệp',
        description:
            '<PERSON><PERSON><PERSON> hồ sơ và các giấy tờ cần thiết để hưởng trợ cấp thất nghiệp theo quy định của Luật Việc làm.',
        date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 2 days from now (for testing notification)
        time: '09:30',
        status: AppointmentStatus.CONFIRMED,
        type: AppointmentType.UNEMPLOYMENT_BENEFIT,
        location: 'Trung tâm Dịch vụ việc làm Khánh Hòa - 87 Hoàng Hoa Thám, <PERSON><PERSON>',
        notes: 'Mang theo: CMND/CCCD, sổ bảo hiểm xã hội, quyế<PERSON> đị<PERSON> thô<PERSON> vi<PERSON>, đ<PERSON><PERSON> đề nghị hưởng trợ cấp thất nghiệp (theo mẫu), sơ yếu lý lịch có xác nhận của UBND.',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
    },
    {
        id: '2',
        title: 'Tư vấn chính sách trợ cấp thất nghiệp',
        description:
            'Buổi tư vấn về các chính sách và quyền lợi khi hưởng trợ cấp thất nghiệp, hỗ trợ học nghề và tìm việc làm mới.',
        date: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 10 days from now
        time: '14:00',
        status: AppointmentStatus.PENDING,
        type: AppointmentType.CONSULTATION,
        location: 'Trung tâm Dịch vụ việc làm Khánh Hòa - 87 Hoàng Hoa Thám, Nha Trang',
        notes: 'Chuẩn bị sẵn các câu hỏi về quyền lợi bảo hiểm thất nghiệp và các chương trình hỗ trợ đào tạo nghề.',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
    },
    {
        id: '3',
        title: 'Nhận quyết định hưởng trợ cấp thất nghiệp',
        description:
            'Nhận quyết định hưởng trợ cấp thất nghiệp và hướng dẫn các bước tiếp theo trong quá trình nhận trợ cấp.',
        date: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 15 days from now
        time: '10:15',
        status: AppointmentStatus.CONFIRMED,
        type: AppointmentType.DOCUMENT_SUBMISSION,
        location: 'Trung tâm Dịch vụ việc làm Khánh Hòa - 87 Hoàng Hoa Thám, Nha Trang',
        notes: 'Mang theo CMND/CCCD và biên nhận hồ sơ đã nộp trước đó.',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
    },
    {
        id: '4',
        title: 'Báo cáo tình hình tìm việc làm',
        description:
            'Báo cáo định kỳ hàng tháng về tình hình tìm việc làm theo quy định để tiếp tục nhận trợ cấp thất nghiệp.',
        date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
        time: '08:30',
        status: AppointmentStatus.PENDING,
        type: AppointmentType.UNEMPLOYMENT_BENEFIT,
        location: 'Trung tâm Dịch vụ việc làm Khánh Hòa - 87 Hoàng Hoa Thám, Nha Trang',
        notes: 'Chuẩn bị báo cáo tình hình tìm việc làm trong tháng và các minh chứng kèm theo (nếu có).',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
    },
    {
        id: '5',
        title: 'Tham gia phiên giao dịch việc làm',
        description:
            'Tham gia phiên giao dịch việc làm do Trung tâm Dịch vụ việc làm Khánh Hòa tổ chức với sự tham gia của nhiều doanh nghiệp.',
        date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 7 days from now
        time: '08:00',
        status: AppointmentStatus.CONFIRMED,
        type: AppointmentType.CONSULTATION,
        location: 'Nhà Văn hóa Lao động tỉnh Khánh Hòa - 22 Hoàng Hoa Thám, Nha Trang',
        notes: 'Mang theo hồ sơ xin việc, CV và các văn bằng chứng chỉ liên quan.',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
    },
];

// Helper function to simulate API delay
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

// Mock API service
export const appointmentApi = {
    // Get all appointments for a user
    getAppointments: async (userPhone: string): Promise<Appointment[]> => {
        await delay(500); // Simulate network delay
        // In a real implementation, we would filter by user ID
        return [...mockAppointments];
    },

    // Get a single appointment by ID
    getAppointmentById: async (id: string): Promise<Appointment | undefined> => {
        await delay(300);
        return mockAppointments.find((appointment) => appointment.id === id);
    },

    // Create a new appointment
    createAppointment: async (
        appointmentData: AppointmentFormData,
        userPhone: string
    ): Promise<Appointment> => {
        await delay(700);
        const newAppointment: Appointment = {
            id: uuidv4(),
            ...appointmentData,
            status: AppointmentStatus.PENDING,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
        };

        // In a real implementation, we would save to a database
        mockAppointments.push(newAppointment);
        return newAppointment;
    },

    // Update an existing appointment
    updateAppointment: async (
        id: string,
        appointmentData: Partial<Appointment>
    ): Promise<Appointment | undefined> => {
        await delay(500);
        const index = mockAppointments.findIndex((appointment) => appointment.id === id);
        if (index === -1) return undefined;

        const updatedAppointment = {
            ...mockAppointments[index],
            ...appointmentData,
            updatedAt: new Date().toISOString(),
        };

        mockAppointments[index] = updatedAppointment;
        return updatedAppointment;
    },

    // Delete an appointment
    deleteAppointment: async (id: string): Promise<boolean> => {
        await delay(400);
        const index = mockAppointments.findIndex((appointment) => appointment.id === id);
        if (index === -1) return false;

        mockAppointments.splice(index, 1);
        return true;
    },

    // Get upcoming appointments (for notifications)
    getUpcomingAppointments: async (userPhone: string, daysThreshold: number = 2): Promise<Appointment[]> => {
        await delay(300);
        const today = new Date();
        const thresholdDate = new Date();
        thresholdDate.setDate(today.getDate() + daysThreshold);

        return mockAppointments.filter((appointment) => {
            const appointmentDate = new Date(appointment.date);
            return (
                appointmentDate >= today &&
                appointmentDate <= thresholdDate &&
                appointment.status !== AppointmentStatus.CANCELLED
            );
        });
    },
};
