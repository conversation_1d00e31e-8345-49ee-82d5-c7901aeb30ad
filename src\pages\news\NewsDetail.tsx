import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useCrawlNewsDetail } from '../../hooks/useCrawlNewsDetail';
import { LazyLoadImage } from 'react-lazy-load-image-component';
import { Header, Page } from 'zmp-ui';
import { FaCalendarAlt, FaArrowLeft } from 'react-icons/fa';
import './NewsDetail.scss';
import { PageContainer } from 'components/page-container';
import { replaceAllUrls } from '../../utils/common';

const NewsDetail: React.FC = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const url = location.state?.url || '';

    const { data, isLoading, error } = useCrawlNewsDetail(url);
    const handleBack = () => {
        navigate(-1);
    };

    if (isLoading) {
        return (
            <PageContainer className="news-detail-page" withHeader resetScroll={true}>
                <Header title="Chi tiết tin tức" showBackIcon onBackClick={handleBack} />
                <div className="news-detail-loading">
                    <div className="loading-spinner"></div>
                    <p>Đang tải dữ liệu...</p>
                </div>
            </PageContainer>
        );
    }

    if (error || !data) {
        return (
            <PageContainer className="news-detail-page" withHeader resetScroll={true}>
                <Header title="Chi tiết tin tức" showBackIcon onBackClick={handleBack} />
                <div className="news-detail-error">
                    <p>Đã xảy ra lỗi khi tải dữ liệu. Vui lòng thử lại sau.</p>
                    <button className="back-button" onClick={handleBack}>
                        <FaArrowLeft /> Quay lại
                    </button>
                </div>
            </PageContainer>
        );
    }

    const BASE_URL = 'https://thongtinvieclamkhanhhoa.vn';

    return (
        <PageContainer className="news-detail-page" withHeader resetScroll={true}>
            <Header title="Chi tiết tin tức" showBackIcon onBackClick={handleBack} />
            <div className="news-detail-container">
                <h1 className="news-detail-title">{data.title}</h1>

                <div className="news-detail-meta">
                    {data.date && (
                        <span className="news-detail-date">
                            <FaCalendarAlt /> {data.date}
                        </span>
                    )}
                    <span className="news-detail-source">Nguồn: thongtinvieclamkhanhhoa.vn</span>
                </div>

                {data.images && data.images.length > 0 ? (
                    <div className="news-detail-gallery">
                        <div className="gallery-container">
                            {data.images.map((imgSrc, index) => (
                                <div key={`gallery-img-${index}`} className="gallery-item">
                                    <LazyLoadImage
                                        src={imgSrc}
                                        alt={`Hình ảnh ${index + 1}`}
                                        effect="blur"
                                        width="100%"
                                        className="gallery-image"
                                        placeholder={<div className="image-placeholder"></div>}
                                    />
                                    <div className="image-number">
                                        {index + 1}/{data.images?.length || 0}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                ) : (
                    data.image && (
                        <div className="news-detail-image">
                            <LazyLoadImage
                                src={data.image}
                                alt={data.title}
                                effect="blur"
                                width="100%"
                                placeholder={<div className="image-placeholder"></div>}
                            />
                        </div>
                    )
                )}

                {data.content && data.content.trim() !== '' ? (
                    <div className="news-detail-content-wrapper">
                        <div
                            className="news-detail-content"
                            dangerouslySetInnerHTML={{
                                __html: replaceAllUrls(data.content, BASE_URL),
                            }}
                        />
                    </div>
                ) : !data.images || data.images.length === 0 ? (
                    <div className="news-detail-content-wrapper">
                        <div className="news-detail-content">
                            <p className="no-content">Không có nội dung</p>
                        </div>
                    </div>
                ) : null}

                <div className="news-detail-actions">
                    <button className="back-button" onClick={handleBack}>
                        <FaArrowLeft /> Quay lại
                    </button>
                    <a href={data.link} target="_blank" rel="noopener noreferrer" className="source-button">
                        Xem bài gốc
                    </a>
                </div>
            </div>
        </PageContainer>
    );
};

export default NewsDetail;
