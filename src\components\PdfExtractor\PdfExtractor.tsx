import React, { useState, useRef, useEffect } from 'react';
import * as pdfjsLib from 'pdfjs-dist';
import { TextItem } from 'pdfjs-dist/types/src/display/api';

interface ExtractedField {
    name: string;
    value: string;
}

interface PdfExtractorProps {
    onFieldsExtracted?: (fields: ExtractedField[]) => void;
}

const PdfExtractor: React.FC<PdfExtractorProps> = ({ onFieldsExtracted }) => {
    const [file, setFile] = useState<File | null>(null);
    const [extractedText, setExtractedText] = useState<string>('');
    const [extractedFields, setExtractedFields] = useState<ExtractedField[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    useEffect(() => {
        pdfjsLib.GlobalWorkerOptions.workerSrc = `https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/build/pdf.worker.min.js`;
    }, []);

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files && event.target.files[0]) {
            setFile(event.target.files[0]);
            setExtractedText('');
            setExtractedFields([]);
            setError(null);
        }
    };

    const extractTextFromPdf = async () => {
        if (!file) {
            setError('Vui lòng chọn file PDF');
            return;
        }

        setIsLoading(true);
        setError(null);

        try {
            const arrayBuffer = await file.arrayBuffer();

            const loadingTask = pdfjsLib.getDocument({
                data: arrayBuffer,
                cMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/cmaps/',
                cMapPacked: true,
            });

            const pdf = await loadingTask.promise;

            let fullText = '';

            for (let i = 1; i <= pdf.numPages; i++) {
                const page = await pdf.getPage(i);
                const textContent = await page.getTextContent();
                const pageText = textContent.items.map((item: TextItem) => item.str).join(' ');

                fullText += pageText + '\n';
            }

            setExtractedText(fullText);

            const fields = extractFieldsFromText(fullText);
            setExtractedFields(fields);

            if (onFieldsExtracted) {
                onFieldsExtracted(fields);
            }
        } catch (err: any) {
            console.error('Lỗi khi đọc file PDF:', err);
            setError(
                `Có lỗi xảy ra khi đọc file PDF: ${err.message || 'Lỗi không xác định'}. Vui lòng thử lại.`
            );
        } finally {
            setIsLoading(false);
        }
    };

    // Hàm này sẽ phân tích văn bản để trích xuất các trường
    // Bạn cần tùy chỉnh hàm này dựa trên cấu trúc của file PDF của bạn
    const extractFieldsFromText = (text: string): ExtractedField[] => {
        const fields: ExtractedField[] = [];

        // Ví dụ: Trích xuất họ tên (giả sử định dạng "Họ tên: [giá trị]")
        const nameMatch = text.match(/Họ tên:?\s*([^\n]+)/i);
        if (nameMatch && nameMatch[1]) {
            fields.push({ name: 'Họ tên', value: nameMatch[1].trim() });
        }

        // Trích xuất số điện thoại (giả sử định dạng "Điện thoại: [giá trị]" hoặc "SĐT: [giá trị]")
        const phoneMatch = text.match(/(?:Điện thoại|SĐT|Số điện thoại):?\s*([0-9\s]+)/i);
        if (phoneMatch && phoneMatch[1]) {
            fields.push({ name: 'Số điện thoại', value: phoneMatch[1].trim() });
        }

        // Trích xuất email (giả sử định dạng "Email: [giá trị]")
        const emailMatch = text.match(/Email:?\s*([^\s@]+@[^\s@]+\.[^\s@]+)/i);
        if (emailMatch && emailMatch[1]) {
            fields.push({ name: 'Email', value: emailMatch[1].trim() });
        }

        // Trích xuất địa chỉ (giả sử định dạng "Địa chỉ: [giá trị]")
        const addressMatch = text.match(/Địa chỉ:?\s*([^\n]+)/i);
        if (addressMatch && addressMatch[1]) {
            fields.push({ name: 'Địa chỉ', value: addressMatch[1].trim() });
        }

        // Thêm các trường khác tùy theo nhu cầu

        return fields;
    };

    const resetForm = () => {
        setFile(null);
        setExtractedText('');
        setExtractedFields([]);
        setError(null);
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    return (
        <div className="pdf-extractor">
            <h2>Trích xuất dữ liệu từ PDF</h2>

            <div className="file-upload">
                <input type="file" accept=".pdf" onChange={handleFileChange} ref={fileInputRef} />
                <p>{file ? `File đã chọn: ${file.name}` : 'Chưa chọn file'}</p>
            </div>

            <div className="actions">
                <button onClick={extractTextFromPdf} disabled={!file || isLoading}>
                    {isLoading ? 'Đang xử lý...' : 'Trích xuất dữ liệu'}
                </button>
                <button onClick={resetForm}>Làm mới</button>
            </div>

            {error && <div className="error">{error}</div>}

            {extractedFields.length > 0 && (
                <div className="extracted-fields">
                    <h3>Các trường đã trích xuất:</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>Tên trường</th>
                                <th>Giá trị</th>
                            </tr>
                        </thead>
                        <tbody>
                            {extractedFields.map((field, index) => (
                                <tr key={index}>
                                    <td>{field.name}</td>
                                    <td>{field.value}</td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            )}

            {extractedText && (
                <div className="extracted-text">
                    <h3>Văn bản đầy đủ:</h3>
                    <pre>{extractedText}</pre>
                </div>
            )}
        </div>
    );
};

export default PdfExtractor;
