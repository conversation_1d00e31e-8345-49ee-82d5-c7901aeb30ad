const fs = require('fs');
const path = require('path');
const cheerio = require('cheerio');

// Function to analyze the homepage HTML
function analyzeHomepage() {
    try {
        // Read the homepage HTML file
        const filePath = path.join(__dirname, 'homepage.html');
        const html = fs.readFileSync(filePath, 'utf8');
        
        // Load the HTML with cheerio
        const $ = cheerio.load(html);
        
        console.log('=== ANALYZING HOMEPAGE ===');
        
        // Find news and events sections
        console.log('\n=== NEWS AND EVENTS SECTIONS ===');
        $('h2, h3, h4, .section-title, .title').each((i, el) => {
            const text = $(el).text().trim();
            if (text.includes('Tin') || text.includes('Sự kiện') || text.includes('Hoạt động')) {
                console.log(`Found section title: "${text}" with tag ${el.tagName}`);
                console.log(`Parent classes: ${$(el).parent().attr('class')}`);
                console.log(`Grandparent classes: ${$(el).parent().parent().attr('class')}`);
                console.log('---');
            }
        });
        
        // Find news items
        console.log('\n=== NEWS ITEMS ===');
        $('a').each((i, el) => {
            const href = $(el).attr('href');
            if (href && (href.includes('/tin-tuc/') || href.includes('/news/'))) {
                console.log(`Found news link: ${href}`);
                console.log(`Text: ${$(el).text().trim()}`);
                console.log(`Classes: ${$(el).attr('class')}`);
                console.log(`Parent classes: ${$(el).parent().attr('class')}`);
                console.log('---');
            }
        });
        
        // Find center activities
        console.log('\n=== CENTER ACTIVITIES ===');
        $('a').each((i, el) => {
            const href = $(el).attr('href');
            if (href && (href.includes('/hoat-dong/') || href.includes('/activity/'))) {
                console.log(`Found activity link: ${href}`);
                console.log(`Text: ${$(el).text().trim()}`);
                console.log(`Classes: ${$(el).attr('class')}`);
                console.log(`Parent classes: ${$(el).parent().attr('class')}`);
                console.log('---');
            }
        });
        
        // Find all sections that might contain news or activities
        console.log('\n=== POTENTIAL NEWS CONTAINERS ===');
        $('.news, .events, .activities, .list, .grid, .container, .section').each((i, el) => {
            console.log(`Found potential container: ${$(el).attr('class')}`);
            const links = $(el).find('a').length;
            const images = $(el).find('img').length;
            console.log(`Contains ${links} links and ${images} images`);
            console.log('---');
        });
        
    } catch (error) {
        console.error('Error analyzing homepage:', error.message);
    }
}

// Function to analyze the news detail HTML
function analyzeNewsDetail() {
    try {
        // Read the news detail HTML file
        const filePath = path.join(__dirname, 'news-detail.html');
        const html = fs.readFileSync(filePath, 'utf8');
        
        // Load the HTML with cheerio
        const $ = cheerio.load(html);
        
        console.log('\n=== ANALYZING NEWS DETAIL PAGE ===');
        
        // Find the news title
        console.log('\n=== NEWS TITLE ===');
        $('h1, h2, h3, .title, .news-title').each((i, el) => {
            console.log(`Potential title: "${$(el).text().trim()}" with tag ${el.tagName}`);
            console.log(`Classes: ${$(el).attr('class')}`);
            console.log('---');
        });
        
        // Find the news date
        console.log('\n=== NEWS DATE ===');
        $('.date, .time, .datetime, .news-date, time').each((i, el) => {
            console.log(`Potential date: "${$(el).text().trim()}" with tag ${el.tagName}`);
            console.log(`Classes: ${$(el).attr('class')}`);
            console.log('---');
        });
        
        // Find the news content
        console.log('\n=== NEWS CONTENT ===');
        $('.content, .news-content, .detail-content, article, .article-content').each((i, el) => {
            console.log(`Potential content container with tag ${el.tagName}`);
            console.log(`Classes: ${$(el).attr('class')}`);
            console.log(`First 100 chars: "${$(el).text().trim().substring(0, 100)}..."`);
            console.log('---');
        });
        
        // Find the news image
        console.log('\n=== NEWS IMAGES ===');
        $('img').each((i, el) => {
            const src = $(el).attr('src');
            const alt = $(el).attr('alt');
            if (src) {
                console.log(`Image src: ${src}`);
                console.log(`Image alt: ${alt || 'No alt text'}`);
                console.log(`Parent classes: ${$(el).parent().attr('class')}`);
                console.log('---');
            }
        });
        
    } catch (error) {
        console.error('Error analyzing news detail:', error.message);
    }
}

// Run the analysis
analyzeHomepage();
analyzeNewsDetail();
