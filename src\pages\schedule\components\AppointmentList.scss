.appointment-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 16px;
}

.appointment-item {
    display: flex;
    background: white;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    position: relative;
    overflow: hidden;
    
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 6px;
        height: 100%;
        border-radius: 3px 0 0 3px;
    }
    
    &:active {
        transform: scale(0.98);
    }
    
    &--confirmed {
        &::before {
            background-color: #4caf50;
        }
    }
    
    &--pending {
        &::before {
            background-color: #ff9800;
        }
    }
    
    &--cancelled {
        opacity: 0.7;
        
        &::before {
            background-color: #f44336;
        }
    }
    
    &--completed {
        &::before {
            background-color: #2196f3;
        }
    }
    
    &__icon-container {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
    }
    
    &__icon {
        font-size: 24px;
        
        &--confirmed {
            color: #4caf50;
        }
        
        &--pending {
            color: #ff9800;
        }
        
        &--cancelled {
            color: #f44336;
        }
        
        &--completed {
            color: #2196f3;
        }
    }
    
    &__content {
        flex: 1;
    }
    
    &__title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 4px;
    }
    
    &__date {
        font-size: 14px;
        color: #666;
        margin-bottom: 4px;
    }
    
    &__location {
        font-size: 13px;
        color: #888;
    }
}

.appointment-list-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 16px;
    text-align: center;
    
    &__icon {
        font-size: 64px;
        color: #ccc;
        margin-bottom: 16px;
    }
    
    &__text {
        font-size: 16px;
        color: #666;
        margin-bottom: 24px;
    }
    
    &__button {
        background-color: var(--primary-color);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 12px 24px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.2s ease;
        
        &:hover {
            background-color: darken(#213982, 10%);
        }
    }
}
