.main-layout {
    width: 100vw;
    // height: 100vh;

    .floating-products {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none; // <PERSON><PERSON> có thể click xuyên qua
        z-index: -1;
        overflow: hidden;
        background-color: #fff !important;

        .floating-product {
            position: absolute;
            width: 80px;
            height: 80px;
            opacity: 0;
            // filter: drop-shadow(0 5px 15px rgba(0, 0, 0, 0.1));
            animation: appearProduct 0.8s ease-out forwards;

            // 2 item đầu chỉ xuất hiện, không float
            &:nth-child(1),
            &:nth-child(2) {
                display: none;
            }

            // Gi<PERSON>a bên trái
            &:nth-child(3) {
                top: 37%;
                left: -2%;
                width: 50px;
                animation: appearProduct 0.8s 0.2s ease-out forwards, floatLeft 12s ease-in-out infinite;
            }

            // Gi<PERSON><PERSON> bên phải
            &:nth-child(4) {
                top: 43%;
                right: -3%;
                width: 68px;
                animation: appearProduct 0.8s 0.4s ease-out forwards, floatRight 10s 0.2s ease-in-out infinite;
            }

            // D<PERSON><PERSON><PERSON> bên trái
            &:nth-child(5) {
                bottom: 36%;
                left: 36%;
                width: 62px;
                animation: appearProduct 0.8s 0.6s ease-out forwards, floatLeft 11s 0.4s ease-in-out infinite;
            }

            // Dưới bên phải
            &:nth-child(6) {
                bottom: 25%;
                right: -2%;
                width: 58px;
                animation: appearProduct 0.8s 0.8s ease-out forwards, floatRight 13s 0.6s ease-in-out infinite;
            }

            &:nth-child(7) {
                bottom: 9%;
                left: -1%;
                width: 60px;
                animation: appearProduct 0.8s 1s ease-out forwards, floatLeft 12s 0.8s ease-in-out infinite;
            }

            &:nth-child(8) {
                bottom: 0%;
                right: -1%;
                width: 60px;
                animation: appearProduct 0.8s 1.2s ease-out forwards, floatRight 11s 1s ease-in-out infinite;
            }
        }
    }
}

.menu {
    position: fixed;
    bottom: 40px;
    left: 0;
    right: 0;
    width: 310px;
    height: 60px;
    border-radius: 100px;
    background-color: #fff;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 99;
    box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
    // animation: slideUp 0.5s ease-out, fadeIn 0.5s ease-out;
    // transform-origin: bottom center;
    &__item {
        color: #868f9f;
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0px;
        font-size: 22px;
        transition: all 0.3s ease;
        position: relative;

        &-icon {
            font-size: 20px;
        }

        &-label {
            font-size: 11px;
            text-align: center;
        }

        // &::after {
        //     content: '';
        //     position: absolute;
        //     bottom: -8px;
        //     left: 50%;
        //     transform: translateX(-50%) scale(0);
        //     width: 5px;
        //     height: 5px;
        //     border-radius: 50%;
        //     background-color: var(--primary-color);
        //     transition: transform 0.3s ease;
        // }

        // &:hover {
        //     transform: translateY(-3px);
        // }

        &.active {
            color: var(--primary-color);

            &::after {
                transform: translateX(-50%) scale(1);
            }
        }
    }
}

@keyframes slideUp {
    from {
        transform: translateY(100px);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

// Thay thế keyframe floating cũ bằng 2 keyframe mới
@keyframes floatLeft {
    0% {
        transform: translate(0, 0) rotate(0deg);
    }
    25% {
        transform: translate(8px, -12px) rotate(2deg);
    }
    50% {
        transform: translate(3px, -18px) rotate(-1deg);
    }
    75% {
        transform: translate(-5px, -8px) rotate(-2deg);
    }
    100% {
        transform: translate(0, 0) rotate(0deg);
    }
}

@keyframes floatRight {
    0% {
        transform: translate(0, 0) rotate(0deg);
    }
    25% {
        transform: translate(-8px, -15px) rotate(-2deg);
    }
    50% {
        transform: translate(-3px, -20px) rotate(1deg);
    }
    75% {
        transform: translate(5px, -10px) rotate(2deg);
    }
    100% {
        transform: translate(0, 0) rotate(0deg);
    }
}

// Thêm media query để điều chỉnh trên mobile
@media (max-width: 768px) {
    .floating-product {
        width: 100px; // Giảm kích thước trên mobile

        &:nth-child(1) {
            top: 5%;
            left: 5%;
        }

        &:nth-child(2) {
            top: 15%;
            right: 5%;
        }

        &:nth-child(3) {
            bottom: 20%;
            left: 10%;
        }
    }
}

// Hiệu ứng glass morphism (tùy chọn)
.floating-product {
    &::before {
        content: '';
        position: absolute;
        inset: -10px;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(5px);
        border-radius: 20px;
        z-index: -1;
    }
}

// Thêm gradient shadow (tùy chọn)
.floating-product {
    filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.15)) drop-shadow(0 0 30px rgba(131, 58, 180, 0.2));
}

// // Thêm hiệu ứng blur khi scroll (tùy chọn)
// .main-layout.scrolling .floating-product {
//     filter: blur(1px) drop-shadow(0 5px 15px rgba(0, 0, 0, 0.1));
//     transition: filter 0.3s ease;
// }

// Thêm keyframe mới cho hiệu ứng xuất hiện
@keyframes appearProduct {
    0% {
        opacity: 0;
        transform: scale(0.8) translateY(20px);
    }
    100% {
        opacity: 0.85;
        transform: scale(1) translateY(0);
    }
}
