const axios = require('axios');
const fs = require('fs');
const path = require('path');

// URL to fetch
const url = 'https://thongtinvieclamkhanhhoa.vn';

// Function to fetch and save HTML
async function fetchAndSaveHTML() {
    try {
        console.log(`Fetching HTML from ${url}...`);
        
        const response = await axios.get(url, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Referer': 'https://www.google.com/'
            }
        });
        
        // Save the HTML to a file
        const filePath = path.join(__dirname, 'homepage.html');
        fs.writeFileSync(filePath, response.data);
        
        console.log(`HTML saved to ${filePath}`);
        
        // Also fetch a news detail page
        console.log('Fetching a news detail page...');
        const newsUrl = 'https://thongtinvieclamkhanhhoa.vn/tin-tuc/31116';
        
        const newsResponse = await axios.get(newsUrl, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Referer': 'https://www.google.com/'
            }
        });
        
        // Save the news detail HTML to a file
        const newsFilePath = path.join(__dirname, 'news-detail.html');
        fs.writeFileSync(newsFilePath, newsResponse.data);
        
        console.log(`News detail HTML saved to ${newsFilePath}`);
        
    } catch (error) {
        console.error('Error fetching HTML:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response headers:', error.response.headers);
        }
    }
}

// Run the function
fetchAndSaveHTML();
