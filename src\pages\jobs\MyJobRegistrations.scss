.my-job-registrations-page {
    padding: 16px;
    background-color: #f5f7fa;
    min-height: 100vh;

    .page-header {
        margin-bottom: 20px;
        text-align: center;

        h1 {
            font-size: 20px;
            font-weight: 600;
            margin: 0;
            color: #333;
            position: relative;
            padding-bottom: 10px;

            &::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 60px;
                height: 3px;
                background: linear-gradient(90deg, #3b82f6, #2563eb);
                border-radius: 3px;
            }
        }
    }

    .search-container {
        margin-bottom: 20px;

        .search-form {
            background-color: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .search-input-container {
            display: flex;
            gap: 8px;
            margin-top: 8px;
        }

        .search-input {
            flex: 1;
        }

        .search-button {
            background-color: #213982;

            &:hover {
                background-color: #1a2d69;
            }
        }
    }

    .job-registrations-list {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }

    .job-registration-card {
        background-color: white;
        border-radius: 8px;
        margin-bottom: 12px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        overflow: hidden;
        border: 1px solid #e5e7eb;
    }

    .job-registration-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        border-bottom: 1px solid #f3f4f6;

        .job-title {
            font-size: 15px;
            font-weight: 600;
            margin: 0;
            color: #111827;
            flex: 1;
        }

        .job-status {
            padding: 4px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 8px;
        }

        .status-registered {
            background-color: #eff6ff;
            color: #1d4ed8;
            border: 1px solid #93c5fd;
        }

        .status-canceled {
            background-color: #fef2f2;
            color: #b91c1c;
            border: 1px solid #fca5a5;
        }

        .status-approved {
            background-color: #ecfdf5;
            color: #047857;
            border: 1px solid #6ee7b7;
        }

        .status-default {
            background-color: #f3f4f6;
            color: #4b5563;
            border: 1px solid #d1d5db;
        }
    }

    .job-registration-body {
        padding: 12px 16px;

        .job-info-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            color: #6b7280;
            font-size: 13px;

            .job-icon {
                margin-right: 8px;
                color: #9ca3af;
                font-size: 12px;
            }
        }

        .job-note {
            margin-top: 8px;
            padding-top: 8px;
            border-top: 1px dashed #e5e7eb;
            display: block;

            strong {
                margin-right: 4px;
                color: #4b5563;
            }
        }
    }

    .job-registration-actions {
        padding: 10px 16px;
        border-top: 1px solid #f0f0f0;
        display: flex;
        justify-content: flex-end;

        .cancel-registration-btn {
            background-color: #fef2f2;
            color: #b91c1c;
            border: 1px solid #fecaca;
            border-radius: 6px;
            padding: 7px 12px;
            font-size: 13px;
            font-weight: 400;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.2s ease;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

            &:hover {
                background-color: #fee2e2;
            }

            &:active {
                background-color: #fecaca;
            }

            .btn-icon {
                font-size: 12px;
            }
        }
    }

    .loading-container,
    .error-container,
    .empty-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 20px;
        text-align: center;
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(0, 0, 0, 0.1);
        border-radius: 50%;
        border-top-color: #213982;
        animation: spin 1s ease-in-out infinite;
        margin-bottom: 16px;
    }

    @keyframes spin {
        to {
            transform: rotate(360deg);
        }
    }
}
