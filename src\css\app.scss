@font-face {
    font-family: 'Beyond';
    src: url('../assets/font/FontsFree-Net-Beyond.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}
:root {
    --header-height: 68px;
    --section-padding: 15px;
    --border-radius-8: 8px;
    --border-radius-12: 12px;
    --border-radius-16: 16px;
    --section-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;

    // --primary-color: #4cacd3;
    --primary-color: #213982;
    --primary-color-rgb: 33, 57, 130;
    --secondary-color: #f0f2f5;
    --money-color: #f96d01;
    --text-black: #212529;
    --text-gray: #6f7071;

    --zmp-primary-color: #213982;
    --zaui-light-color-primary: #213982;
    --zaui-dark-color-primary: #213982;
}

* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

html,
body {
    // background-color: #fff;
    user-select: none;
    --inset-top: max(var(--zaui-safe-area-inset-top, 24px), 26px);
    --inset-bottom: var(--zaui-safe-area-inset-bottom, 24px);
    color: var(--color-text);
    background-color: #fff;
}

.inset-top {
    padding-top: max(var(--zaui-safe-area-inset-top, 24px), 26px);
}

.inset-bottom {
    padding-bottom: var(--zaui-safe-area-inset-bottom, 16px);
}

.page-container {
    --header-height: 0px;
    --inset-top: max(var(--zaui-safe-area-inset-top, 24px), 26px);

    padding-top: calc(var(--inset-top, 24px) + var(--header-height));
    display: flex;
    flex-direction: column;
    min-height: 100%;
    // background-color: #fff;
}

.dont-need-padding-top {
    padding-top: 0 !important;
}

.with-header {
    --header-height: 44px;
}

.with-bottom-nav {
    --inset-bottom: var(--zaui-safe-area-inset-bottom, 16px);
    padding-bottom: calc(var(--inset-bottom, 16px) + var(--bottom-nav-height));
}
.for-chatbot {
    padding-bottom: 106px;
}
.for-navbar-tv {
    padding-bottom: 70px;
    & #television-navigation {
        position: fixed;
        bottom: 0;
    }
}

.no-inset-top {
    --inset-top: 0px;
}

.no-inset-bottom {
    --inset-bottom: 0px;
}

.hide-scrollbar {
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
        display: none;
    }
}

.zaui-header {
    height: calc(max(var(--zaui-safe-area-inset-top, 24px), 26px) + 44px) !important;
    padding: calc(max(var(--zaui-safe-area-inset-top, 24px), 26px) + 16px) 12px 16px 12px !important;
    // background-color: var(--primary-color);
    padding-right: 100px !important;
    // background-image: url('../assets/svg/sub-bg.svg');
    // background-size: cover;
    // background-repeat: no-repeat;
    // background-position: center;
    &.no-divider::after {
        display: none !important;
    }
    &.have-shadow {
        box-shadow: 0 2px 8px 0 rgba(99, 99, 99, 0.2);
    }
    &.icon-back-white {
        .zaui-icon {
            color: #fff;
        }
    }
}

.primary-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: var(--color-primary) var(--color-primary);

    &::-webkit-scrollbar-thumb {
        background-color: var(--color-primary);
    }
}

.text-truncate {
    display: -webkit-box;
    max-width: 100%;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    word-wrap: break-word;
}

.text-truncate-1 {
    -webkit-line-clamp: 1;
}

.text-truncate-2 {
    -webkit-line-clamp: 2;
}

.text-truncate-3 {
    -webkit-line-clamp: 3;
}

.zaui-sheet-content-handler-wrapper {
    height: 36px !important;
}

.zaui-modal-content-main {
    padding: 0 !important;
}
.zaui-modal-content-title {
    display: none;
}
.zaui-modal-content-description {
    display: none;
}
.zaui-modal-content {
    border-radius: 10px !important;
}

.zaui-input-wrapper {
    border-radius: 10px !important;
    position: relative !important;
    margin-top: 15px !important;
}
.zaui-input-label {
    position: absolute;
    top: -5px;
    left: 10px;
    background-color: #fff;
    z-index: 1;
    font-size: 13px;
    padding: 0 5px;
    color: #6f7071;
    display: block;
    border-radius: 100px;
}
.zaui-input-affix-wrapper {
    border-color: #f0f0f0;
}
.zaui-btn {
    border-radius: 10px;
}
.form-error {
    color: red;
    font-size: 14px;
}
.form-input {
    &-error {
        input {
            border-color: red !important;
        }
    }
    &__field-required {
        color: red;
        margin-left: 5px;
    }
}

.form-input__field {
    display: flex;
    flex-direction: column;
    gap: 8px;
    position: relative;
    margin-top: 25px;
    label {
        font-size: 14px;
        color: #6f7071;
        position: absolute;
        top: -10px;
        left: 10px;
        background-color: #fff;
        z-index: 1;
        padding: 0 5px;
        border-radius: 100px;
    }
    & input {
        width: 100%;
        height: 50px;
        border: 1px solid #f0f0f0;
        border-radius: 10px;
        padding: 0 12px;
        outline: none;

        &:focus {
            border-color: var(--primary-color);
            border-width: 1px;
            border-style: solid;
        }
    }
}
