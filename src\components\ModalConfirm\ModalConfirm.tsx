import React from 'react';
import './ModalConfirm.scss';
import { Button, Modal } from 'zmp-ui';
import Lottie from 'lottie-react';
import successAnimation from 'assets/lottiers/success.json';
import errorAnimation from 'assets/lottiers/error.json';
export const IOSIcons = {
    error: errorAnimation,
    success: successAnimation,
};
interface CustomModalProps {
    type?: 'error' | 'success' | 'info' | 'warning';
    title: string;
    content: string;
    open: boolean;
    onOk?: () => void;
    onCancel?: () => void;
    centered?: boolean;
}

const CustomModal = ({
    type = 'error',
    title,
    content,
    open,
    onOk,
    onCancel,
    centered = true,
}: CustomModalProps) => {
    return (
        <Modal width={400} visible={true} onClose={onCancel} className="custom-modal">
            <div className="modal-content-wrapper">
                <Lottie
                    animationData={IOSIcons[type as keyof typeof IOSIcons]}
                    loop={false}
                    style={{ height: type === 'success' ? 130 : 100, width: type === 'success' ? 130 : 100 }}
                />

                <p
                    className="modal-message"
                    style={{ position: 'relative', top: type === 'success' ? -10 : 0 }}
                >
                    {content}
                </p>
                <div className="modal-button-wrapper">
                    <button className="modal-button" onClick={onOk}>
                        Đồng ý
                    </button>
                </div>
            </div>
        </Modal>
    );
};

export default CustomModal;
