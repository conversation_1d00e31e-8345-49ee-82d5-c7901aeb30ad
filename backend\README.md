# Proxy Server cho thongtinvieclamkhanhhoa.vn

Server proxy này được tạo ra để giúp tránh các vấn đề CORS và country blocking khi cào dữ liệu từ trang web thongtinvieclamkhanhhoa.vn.

## Cài đặt

1. Cài đặt các dependencies:
   ```
   cd backend
   npm install
   ```

## Chạy server

### Chế độ phát triển (với nodemon để tự động khởi động lại khi có thay đổi)
```
npm run dev
```

### Chế độ sản xuất
```
npm start
```

Server sẽ chạy tại http://localhost:3001

## API Endpoints

### GET /proxy
Chuyển tiếp request đến URL được chỉ định.

**Query Parameters:**
- `url` (bắt buộc): URL của trang web cần cào dữ liệu

**Ví dụ:**
```
GET http://localhost:3001/proxy?url=https://thongtinvieclamkhanhhoa.vn
```

### GET /health
Kiểm tra trạng thái hoạt động của server.

**Ví dụ:**
```
GET http://localhost:3001/health
```

**Phản hồi:**
```json
{
  "status": "ok",
  "message": "Proxy server is running"
}
```

## Cách hoạt động

Server này hoạt động như một proxy trung gian:

1. Nhận request từ frontend với URL cần cào dữ liệu
2. Thực hiện request đến URL đó với các headers giống trình duyệt thông thường
3. Nhận phản hồi từ trang web đích
4. Trả về dữ liệu cho frontend

Điều này giúp tránh các vấn đề CORS vì request đến trang web đích được thực hiện từ server, không phải từ trình duyệt của người dùng.
