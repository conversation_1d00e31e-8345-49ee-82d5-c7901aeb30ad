.phieu-ket-qua-item {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 16px;
    overflow: hidden;
    
    &__header {
        background-color: var(--primary-color);
        color: white;
        padding: 16px;
        text-align: center;
    }
    
    &__title {
        font-size: 18px;
        font-weight: 600;
        margin: 0;
    }
    
    &__body {
        padding: 16px;
    }
    
    &__info {
        display: flex;
        align-items: flex-start;
        margin-bottom: 12px;
        
        &:last-child {
            margin-bottom: 0;
        }
    }
    
    &__icon {
        color: var(--primary-color);
        font-size: 18px;
        margin-right: 12px;
        margin-top: 2px;
    }
    
    &__info-content {
        flex: 1;
    }
    
    &__label {
        color: #666;
        display: block;
        font-size: 14px;
        margin-bottom: 2px;
    }
    
    &__value {
        color: #333;
        font-size: 16px;
        font-weight: 500;
    }
}
