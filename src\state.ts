import { atom, selector } from 'recoil';
import { getUserInfo } from 'zmp-sdk';
import { getAccessToken } from 'zmp-sdk/apis';

export const userState = selector({
    key: 'user',
    get: () =>
        getUserInfo({
            avatarType: 'normal',
        }),
});

export const displayNameState = atom({
    key: 'displayName',
    default: '',
});

export const userNumberState = atom({
    key: 'userNumber',
    default: localStorage.getItem('userPhoneNumber') || '0792289045',
});

export const userInfoState = atom<any>({
    key: 'userInfo',
    default: localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo')!) : null,
});
export const userZaloInfoState = atom<any>({
    key: 'userZaloInfo',
    default: localStorage.getItem('userZaloInfo') ? JSON.parse(localStorage.getItem('userZaloInfo')!) : null,
});

export const zaloAccessTokenState = selector({
    key: 'zaloAccessToken',
    get: () => getAccessToken(),
});

export const isMemberState = atom({
    key: 'isMember',
    default: false,
});
