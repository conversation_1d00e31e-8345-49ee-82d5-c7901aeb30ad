import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Box, Button, Input, Page, Text, useSnackbar } from 'zmp-ui';
import { FaArrowLeft, FaBuilding, FaCalendarAlt, FaMoneyBillWave } from 'react-icons/fa';
import { useJobList, JobItem } from 'hooks/useJobList';
import { jobRegistrationApi, JobRegistrationData } from 'apis/jobRegistrationApi';
import { useRecoilValue } from 'recoil';
import { isMemberState, userInfoState, userNumberState } from 'state';
import GetUserData from '../home/<USER>/GetUserData';
import './JobRegistration.scss';
import { PageContainer } from 'components/page-container';

const JobRegistration = () => {
    const { id } = useParams<{ id: string }>();
    const navigate = useNavigate();
    const { data: jobsData, isLoading } = useJobList();
    const [job, setJob] = useState<JobItem | null>(null);
    const userNumber = useRecoilValue(userNumberState);
    const userInfo = useRecoilValue(userInfoState);
    const isMember = useRecoilValue(isMemberState);

    const [formData, setFormData] = useState<JobRegistrationData>({
        maDinhDanh: userInfo?.cccd || userInfo?.maDinhDanh || '',
        maCongViec: parseInt(id || '0'),
        hoTen: userInfo?.hoTen || '',
        soDienThoai: userNumber || '',
        diaChi: userInfo?.diaChi || '',
        ghiChu: '',
    });
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { openSnackbar } = useSnackbar();

    useEffect(() => {
        if (jobsData && id) {
            const jobId = parseInt(id);
            const foundJob = jobsData.data.find((j) => j.id === jobId);
            if (foundJob) {
                setJob(foundJob);
            }
        }
    }, [jobsData, id]);

    useEffect(() => {
        if (isMember && userInfo) {
            setFormData((prev) => ({
                ...prev,
                maDinhDanh: userInfo.maDinhDanh || userInfo.cccd || '',
                hoTen: userInfo.hoTen || '',
                diaChi: userInfo.diaChi || '',
            }));
        }
    }, [isMember, userInfo]);

    // Cập nhật số điện thoại từ userNumberState
    useEffect(() => {
        if (userNumber) {
            setFormData((prev) => ({
                ...prev,
                soDienThoai: userNumber,
            }));
        }
    }, [userNumber]);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setFormData((prev) => ({
            ...prev,
            [name]: value,
        }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        // Validate các trường bắt buộc
        if (!formData.maDinhDanh) {
            openSnackbar({
                text: 'Vui lòng nhập mã định danh',
                type: 'error',
                duration: 3000,
            });
            return;
        }

        if (!formData.hoTen) {
            openSnackbar({
                text: 'Vui lòng nhập họ tên',
                type: 'error',
                duration: 3000,
            });
            return;
        }

        if (!formData.soDienThoai) {
            openSnackbar({
                text: 'Vui lòng cung cấp số điện thoại',
                type: 'error',
                duration: 3000,
            });
            return;
        }

        if (!formData.diaChi) {
            openSnackbar({
                text: 'Vui lòng nhập địa chỉ',
                type: 'error',
                duration: 3000,
            });
            return;
        }

        try {
            setIsSubmitting(true);
            const response = await jobRegistrationApi.register(formData);

            if (response.success === '1') {
                openSnackbar({
                    text: 'Đăng ký việc làm thành công',
                    type: 'success',
                    duration: 3000,
                });
                setTimeout(() => {
                    navigate(-1);
                }, 1500);
            } else {
                openSnackbar({
                    text: response.message || 'Đăng ký không thành công',
                    type: 'error',
                    duration: 3000,
                });
            }
        } catch (error) {
            openSnackbar({
                text: 'Đã xảy ra lỗi khi đăng ký',
                type: 'error',
                duration: 3000,
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    const formatDate = (dateString: string) => {
        if (!dateString) return '';
        const parts = dateString.split('/');
        if (parts.length !== 3) return dateString;
        return `${parts[0]}/${parts[1]}/${parts[2]}`;
    };

    if (isLoading) {
        return (
            <PageContainer className="job-registration-page">
                <div className="job-registration-header">
                    <Button
                        className="back-button"
                        onClick={() => navigate(-1)}
                        icon={<FaArrowLeft />}
                        size="small"
                    />
                    <h1>Đăng ký việc làm</h1>
                </div>
                <div className="loading-container">
                    <div className="loading-spinner"></div>
                    <p>Đang tải thông tin việc làm...</p>
                </div>
            </PageContainer>
        );
    }

    if (!job) {
        return (
            <PageContainer className="job-registration-page">
                <div className="job-registration-header">
                    <Button
                        className="back-button"
                        onClick={() => navigate(-1)}
                        icon={<FaArrowLeft />}
                        size="small"
                    />
                    <h1>Đăng ký việc làm</h1>
                </div>
                <div className="error-container">
                    <p>Không tìm thấy thông tin việc làm</p>
                    <Button onClick={() => navigate(-1)}>Quay lại</Button>
                </div>
            </PageContainer>
        );
    }

    const hasPhoneNumber = !!userNumber;

    return (
        <PageContainer className="job-registration-page">
            <div className="job-registration-header">
                <Button
                    className="back-button"
                    onClick={() => navigate(-1)}
                    icon={<FaArrowLeft />}
                    size="small"
                />
                <h1>Đăng ký việc làm</h1>
            </div>

            {!hasPhoneNumber ? (
                <div className="get-user-data-container">
                    <GetUserData />
                </div>
            ) : (
                <>
                    <div className="job-summary">
                        <h2>{job.tenCongViec}</h2>
                        <div className="job-info-item">
                            <FaBuilding className="job-icon" />
                            <span>{job.congTy}</span>
                        </div>
                        <div className="job-info-item">
                            <FaMoneyBillWave className="job-icon" />
                            <span>{job.mucLuong || 'Thỏa thuận'}</span>
                        </div>
                        <div className="job-info-item">
                            <FaCalendarAlt className="job-icon" />
                            <span>Hạn nộp: {formatDate(job.thoiHanDen)}</span>
                        </div>
                    </div>

                    <form onSubmit={handleSubmit} className="registration-form">
                        <Box className="form-group">
                            <Text bold>
                                Mã định danh (CCCD) <span className="required-field">*</span>
                            </Text>
                            <Input
                                name="maDinhDanh"
                                value={formData.maDinhDanh}
                                onChange={handleInputChange}
                                placeholder="Nhập mã định danh"
                                required
                                disabled={isSubmitting}
                            />
                        </Box>

                        <Box className="form-group">
                            <Text bold>
                                Họ tên <span className="required-field">*</span>
                            </Text>
                            <Input
                                name="hoTen"
                                value={formData.hoTen}
                                onChange={handleInputChange}
                                placeholder="Nhập họ tên"
                                required
                                disabled={isSubmitting}
                            />
                        </Box>

                        <Box className="form-group">
                            <Text bold>
                                Số điện thoại <span className="required-field">*</span>
                            </Text>
                            <Input
                                name="soDienThoai"
                                value={formData.soDienThoai}
                                onChange={handleInputChange}
                                placeholder="Nhập số điện thoại"
                                readOnly
                                required
                            />
                        </Box>

                        <Box className="form-group">
                            <Text bold>
                                Địa chỉ <span className="required-field">*</span>
                            </Text>
                            <Input
                                name="diaChi"
                                value={formData.diaChi}
                                onChange={handleInputChange}
                                placeholder="Nhập địa chỉ"
                                required
                                disabled={isSubmitting}
                            />
                        </Box>

                        <Box className="form-group">
                            <Text bold>Ghi chú</Text>
                            <Input
                                name="ghiChu"
                                value={formData.ghiChu}
                                onChange={handleInputChange}
                                placeholder="Nhập ghi chú (nếu có)"
                                disabled={isSubmitting}
                            />
                        </Box>

                        <Button
                            className="submit-button"
                            htmlType="submit"
                            disabled={isSubmitting}
                            loading={isSubmitting}
                        >
                            {isSubmitting ? 'Đang đăng ký...' : 'Đăng ký'}
                        </Button>

                        {isSubmitting && (
                            <div className="form-submitting-overlay">
                                <div className="loading-spinner"></div>
                                <p>Đang xử lý đăng ký...</p>
                            </div>
                        )}
                    </form>
                </>
            )}
        </PageContainer>
    );
};

export default JobRegistration;
