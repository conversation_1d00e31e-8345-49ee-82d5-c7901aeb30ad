// .floating-product {
//     position: relative;
//     animation: floating 6s ease-in-out infinite;
//     background-color: #fff;
//     img {
//         width: 100%;
//         height: auto;
//         filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.15));
//         transition: transform 0.3s ease;

//         &:hover {
//             transform: scale(1.05);
//         }
//     }
// }

// // Animation chính cho hiệu ứng lơ lửng
// @keyframes floating {
//     0% {
//         transform: translateY(0px);
//     }
//     50% {
//         transform: translateY(-20px);
//     }
//     100% {
//         transform: translateY(0px);
//     }
// }

// // Thêm hiệu ứng xoay nhẹ (tùy chọn)
// .floating-product:nth-child(even) {
//     animation: floating-rotate 7s ease-in-out infinite;
// }

// @keyframes floating-rotate {
//     0% {
//         transform: translateY(0px) rotate(0deg);
//     }
//     50% {
//         transform: translateY(-20px) rotate(5deg);
//     }
//     100% {
//         transform: translateY(0px) rotate(0deg);
//     }
// }
