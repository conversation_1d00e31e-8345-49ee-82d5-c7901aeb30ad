import API_URL from 'constants/API_URL';
import { axiosClient } from 'apis/axiosClient';

export const memberApi = {
    getMemberByPhoneNumber: async (phoneNumber: string): Promise<any> => {
        const res: any = await axiosClient.get(API_URL.get_member_by_phone_number, {
            params: {
                phoneNumber,
            },
        });
        return res;
    },
    getMemberPrepaidCard: async (memberId: string): Promise<any> => {
        const res: any = await axiosClient.get(API_URL.get_member_prepaid_card, {
            params: {
                maKhach: memberId,
            },
        });
        return res?.data;
    },

    getCardHistory: async (memberId: string, cardId?: string): Promise<any> => {
        const res: any = await axiosClient.get(API_URL.get_card_history, {
            params: {
                maKhach: memberId,
                transactionType: 'BILL',
            },
        });
        return res?.data;
    },

    createGuest: async (data: any): Promise<any> => {
        const res: any = await axiosClient.post(API_URL.create_guest, JSON.stringify(data));
        return res;
    },

    getOtpCode: async (phoneNumber: string): Promise<any> => {
        const formData = new FormData();
        formData.append('phoneNumber', phoneNumber);
        const res: any = await axiosClient.post(API_URL.getOtpCode, formData);
        return res;
    },
};
