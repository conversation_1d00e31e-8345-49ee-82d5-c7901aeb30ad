import { useState, useEffect } from 'react';
import { useRecoilValue } from 'recoil';
import { isMemberState, userInfoState } from 'state';
import { jobRegistrationApi } from 'apis/jobRegistrationApi';

export const useRegisteredJobs = () => {
    const [registeredJobIds, setRegisteredJobIds] = useState<number[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const userInfo = useRecoilValue(userInfoState);
    const isMember = useRecoilValue(isMemberState);

    useEffect(() => {
        const fetchRegisteredJobs = async () => {
            if (!isMember || !userInfo) {
                setIsLoading(false);
                return;
            }

            const userMaDinhDanh = userInfo.maDinhDanh || userInfo.cccd || '';
            if (!userMaDinhDanh) {
                setIsLoading(false);
                return;
            }

            try {
                setIsLoading(true);
                const response = await jobRegistrationApi.getByMaDinhDanh(userMaDinhDanh);

                if (response.success === '1' && response.data) {
                    const jobIds = response.data.map((item) => item.maCongViec);
                    setRegisteredJobIds(jobIds);
                } else {
                    setRegisteredJobIds([]);
                }
            } catch (error) {
                setRegisteredJobIds([]);
            } finally {
                setIsLoading(false);
            }
        };

        fetchRegisteredJobs();
    }, [isMember, userInfo]);

    const isJobRegistered = (jobId: number): boolean => {
        return registeredJobIds.includes(jobId);
    };

    return {
        isJobRegistered,
        isLoading,
        registeredJobIds,
    };
};
