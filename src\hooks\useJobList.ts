import { useQuery } from '@tanstack/react-query';
import { axiosClient } from 'apis/axiosClient';

export interface JobItem {
    id: number;
    congTy: string;
    tenCongViec: string;
    soLuong: number;
    moTa: string;
    chucVu: string;
    trinhDoHocVan: string;
    trinhDoCMKT: string;
    ngoaiNgu: string;
    tinHoc: string;
    kinhNghiem: string;
    loaiHopDong: string;
    hinhThucLamViec: string;
    mucDich: string;
    mucLuong: string;
    doiTuongUuTien: string;
    hinhThucTuyenDung: string;
    thoiHanTu: string;
    thoiHanDen: string;
    email: string;
    soDienThoai: string;
}

export interface JobListResponse {
    success: string;
    data: JobItem[];
}

const fetchJobs = async (): Promise<JobListResponse> => {
    try {
        const response = await axiosClient.get('api/Job/JobPosting/ListJob');
        return response;
    } catch (error) {
        throw error;
    }
};

export const useJobList = () => {
    return useQuery(['jobList'], fetchJobs, {
        staleTime: 5 * 60 * 1000,
        cacheTime: 30 * 60 * 1000,
        refetchOnWindowFocus: false,
    });
};
