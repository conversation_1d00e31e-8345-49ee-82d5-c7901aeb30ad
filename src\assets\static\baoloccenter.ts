const BaoLocCenterHTML = `
<body
    class="home page-template page-template-elementor_header_footer page page-id-2788 wp-custom-logo base-theme elementor-default elementor-template-full-width elementor-kit-7 elementor-page elementor-page-2788 e--ua-isTouchDevice e--ua-appleWebkit e--ua-safari e--ua-webkit show_contactfix"
    data-elementor-device-mode="mobile"
>
    <div class="boxed_wrapper">
        <div id="search-popup" class="search-popup">
            <div class="close-search"><span>x</span></div>
            <div class="popup-inner">
                <div class="overlay-layer"></div>
                <div class="search-form">
                    <form role="search" method="get" action="https://baoloccenter.com/">
                        <div class="form-group">
                            <fieldset>
                                <input
                                    type="search"
                                    class="form-control"
                                    id="search-form-678f1a476fc72"
                                    placeholder="T<PERSON><PERSON> kiếm ở đây..."
                                    value=""
                                    name="s"
                                    required="required"
                                />
                                <input type="submit" value="Tìm kiếm ngay..." class="theme-btn style-four" />
                            </fieldset>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <header class="main-header header-style-one fixed-header">
            <!--Start Header Top-->
            <div class="header-top">
                <div class="auto-container">
                    <div class="outer-box">
                        <div class="header-top__left">
                            <div class="header-contact-info text-right-rtl">
                                <ul>
                                    <li>
                                        <div class="icon">
                                            <span class="flaticon-pin-1"></span>
                                        </div>
                                        <div class="text">
                                            <h6>280 Trần Phú, P.Lộc Sơn, TP Bảo Lộc, Lâm Đồng</h6>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="icon">
                                            <span class="flaticon-clock"></span>
                                        </div>
                                        <div class="text">
                                            <h6>Mở cửa: 9:00 - 21:30</h6>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="icon">
                                            <span class="flaticon-phone-call-2"></span>
                                        </div>
                                        <div class="text">
                                            <h6><a href="tel:0816 59 1616">0816 59 1616</a></h6>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="header-top__right">
                            <div class="header-social-link">
                                <div class="social-link">
                                    <ul class="clearfix"></ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--End Header Top-->

            <!--Start Header-->
            <div class="header">
                <div class="header-shape-bg"></div>
                <div class="auto-container">
                    <div class="outer-box">
                        <div class="header-left">
                            <div class="logo">
                                <a
                                    href="https://baoloccenter.com/"
                                    class="custom-logo-link"
                                    rel="home"
                                    aria-current="page"
                                    ><img
                                        fetchpriority="high"
                                        width="2074"
                                        height="1229"
                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/LOGO-BAO-LOC-CENTER-0409.png"
                                        class="custom-logo"
                                        alt="BAOLOC CENTER"
                                        decoding="async"
                                /></a>
                            </div>
                        </div>
                        <div class="header-middle">
                            <div class="nav-outer style1 clearfix">
                                <!--Mobile Navigation Toggler-->
                                <div class="mobile-nav-toggler">
                                    <div class="inner">
                                        <span class="icon-bar"></span>
                                        <span class="icon-bar"></span>
                                        <span class="icon-bar"></span>
                                    </div>
                                </div>
                                <!-- Main Menu -->
                                <nav class="main-menu style1 navbar-expand-md navbar-light">
                                    <div
                                        class="collapse navbar-collapse show clearfix"
                                        id="navbarSupportedContent"
                                    >
                                        <ul id="menu-primary-menu" class="navigation clearfix">
                                            <li
                                                id="menu-item-2926"
                                                class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-2788 current_page_item menu-item-2926"
                                            >
                                                <a href="https://baoloccenter.com/" aria-current="page"
                                                    >Trang chủ</a
                                                >
                                            </li>
                                            <li
                                                id="menu-item-1027"
                                                class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1027"
                                            >
                                                <a href="https://baoloccenter.com/gio-hoat-dong/"
                                                    >Giờ hoạt động</a
                                                >
                                            </li>
                                            <li
                                                id="menu-item-1485"
                                                class="menu-item menu-item-type-post_type menu-item-object-page menu-item-has-children menu-item-1485"
                                            >
                                                <a href="https://baoloccenter.com/thuong-hieu/"
                                                    >Thương hiệu</a
                                                >
                                                <ul class="sub-menu">
                                                    <li
                                                        id="menu-item-3649"
                                                        class="menu-item menu-item-type-post_type menu-item-object-page menu-item-3649"
                                                    >
                                                        <a href="https://baoloccenter.com/thuong-hieu/"
                                                            >Thương hiệu</a
                                                        >
                                                    </li>
                                                    <li
                                                        id="menu-item-3648"
                                                        class="menu-item menu-item-type-post_type menu-item-object-page menu-item-3648"
                                                    >
                                                        <a href="https://baoloccenter.com/so-do-tang/"
                                                            >Sơ đồ tầng</a
                                                        >
                                                    </li>
                                                </ul>
                                                <div class="dropdown-btn">
                                                    <span class="fa fa-angle-right"></span>
                                                </div>
                                            </li>
                                            <li
                                                id="menu-item-612"
                                                class="menu-item menu-item-type-post_type menu-item-object-page menu-item-612"
                                            >
                                                <a href="https://baoloccenter.com/khuyen-mai/">Khuyến mãi</a>
                                            </li>
                                            <li
                                                id="menu-item-1076"
                                                class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1076"
                                            >
                                                <a href="https://baoloccenter.com/tin-tuc-va-su-kien/"
                                                    >Tin tức và Sự kiện</a
                                                >
                                            </li>
                                            <li
                                                id="menu-item-1029"
                                                class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1029"
                                            >
                                                <a href="https://baoloccenter.com/the-thanh-vien/"
                                                    >Thẻ thành viên</a
                                                >
                                            </li>
                                        </ul>
                                    </div>
                                </nav>
                                <!-- Main Menu End-->
                            </div>
                        </div>
                        <div class="header-right">
                            <div class="serach-button-style1">
                                <button type="button" class="search-toggler">
                                    <i class="icon-search"></i>
                                </button>
                            </div>
                            <div class="thm-space-box-2"></div>
                            <div class="header-right_buttom">
                                <div class="btns-box">
                                    <a class="btn-one" href="tel:0816591616">
                                        <span class="txt">Liên hệ với chúng tôi</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--End header-->
            <!-- Mobile Menu  -->
            <div class="mobile-menu">
                <div class="close-btn"><span class="icon fa fa-times-circle"></span></div>
                <nav class="menu-box mCustomScrollbar _mCS_1 mCS_no_scrollbar">
                    <div
                        id="mCSB_1"
                        class="mCustomScrollBox mCS-light mCSB_vertical mCSB_inside"
                        style="max-height: 932px"
                        tabindex="0"
                    >
                        <div
                            id="mCSB_1_container"
                            class="mCSB_container mCS_y_hidden mCS_no_scrollbar_y"
                            style="position: relative; top: 0; left: 0"
                            dir="ltr"
                        >
                            <div class="nav-logo">
                                <a href="https://baoloccenter.com/"
                                    ><img
                                        src="https://baoloccenter.com/wp-content/uploads/2021/10/logo-bao-loc-cent.png"
                                        alt="Logo"
                                        class="mCS_img_loaded"
                                /></a>
                            </div>
                            <div class="menu-outer">
                                <!--Here Menu Will Come Automatically Via Javascript / Same Menu as in Header-->

                                <div
                                    class="collapse navbar-collapse show clearfix"
                                    id="navbarSupportedContent"
                                >
                                    <ul id="menu-primary-menu" class="navigation clearfix">
                                        <li
                                            id="menu-item-2926"
                                            class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-2788 current_page_item menu-item-2926"
                                        >
                                            <a href="https://baoloccenter.com/" aria-current="page"
                                                >Trang chủ</a
                                            >
                                        </li>
                                        <li
                                            id="menu-item-1027"
                                            class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1027"
                                        >
                                            <a href="https://baoloccenter.com/gio-hoat-dong/"
                                                >Giờ hoạt động</a
                                            >
                                        </li>
                                        <li
                                            id="menu-item-1485"
                                            class="menu-item menu-item-type-post_type menu-item-object-page menu-item-has-children menu-item-1485"
                                        >
                                            <a href="https://baoloccenter.com/thuong-hieu/">Thương hiệu</a>
                                            <ul class="sub-menu">
                                                <li
                                                    id="menu-item-3649"
                                                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-3649"
                                                >
                                                    <a href="https://baoloccenter.com/thuong-hieu/"
                                                        >Thương hiệu</a
                                                    >
                                                </li>
                                                <li
                                                    id="menu-item-3648"
                                                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-3648"
                                                >
                                                    <a href="https://baoloccenter.com/so-do-tang/"
                                                        >Sơ đồ tầng</a
                                                    >
                                                </li>
                                            </ul>
                                            <div class="dropdown-btn">
                                                <span class="fa fa-angle-right"></span>
                                            </div>
                                        </li>
                                        <li
                                            id="menu-item-612"
                                            class="menu-item menu-item-type-post_type menu-item-object-page menu-item-612"
                                        >
                                            <a href="https://baoloccenter.com/khuyen-mai/">Khuyến mãi</a>
                                        </li>
                                        <li
                                            id="menu-item-1076"
                                            class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1076"
                                        >
                                            <a href="https://baoloccenter.com/tin-tuc-va-su-kien/"
                                                >Tin tức và Sự kiện</a
                                            >
                                        </li>
                                        <li
                                            id="menu-item-1029"
                                            class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1029"
                                        >
                                            <a href="https://baoloccenter.com/the-thanh-vien/"
                                                >Thẻ thành viên</a
                                            >
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <!--Social Links-->
                            <div class="serach-button-style2">
                                <form role="search" method="get" action="https://baoloccenter.com/">
                                    <input
                                        type="search"
                                        class="form-control"
                                        id="search-form-678f1a47735cd"
                                        placeholder="Tìm kiếm..."
                                        value=""
                                        name="s"
                                        required="required"
                                    />
                                    <button type="button"><i class="icon-search"></i></button>
                                </form>
                            </div>
                            <div class="social-links">
                                <ul class="clearfix"></ul>
                            </div>
                        </div>
                        <div
                            id="mCSB_1_scrollbar_vertical"
                            class="mCSB_scrollTools mCSB_1_scrollbar mCS-light mCSB_scrollTools_vertical"
                            style="display: none"
                        >
                            <div class="mCSB_draggerContainer">
                                <div
                                    id="mCSB_1_dragger_vertical"
                                    class="mCSB_dragger"
                                    style="position: absolute; min-height: 30px; height: 0px; top: 0px"
                                    oncontextmenu="return false;"
                                >
                                    <div class="mCSB_dragger_bar" style="line-height: 30px"></div>
                                    <div class="mCSB_draggerRail"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </nav>
            </div>
            <!-- End Mobile Menu -->
        </header>
        <div data-elementor-type="wp-page" data-elementor-id="2788" class="elementor elementor-2788">
            <section
                class="elementor-section elementor-top-section elementor-element elementor-element-c299bbf elementor-section-full_width elementor-hidden-desktop elementor-hidden-tablet elementor-hidden-mobile elementor-section-height-default elementor-section-height-default"
                data-id="c299bbf"
                data-element_type="section"
            >
                <div class="elementor-container elementor-column-gap-no">
                    <div
                        class="elementor-column elementor-col-100 elementor-top-column elementor-element elementor-element-b8cfedb"
                        data-id="b8cfedb"
                        data-element_type="column"
                    >
                        <div class="elementor-widget-wrap elementor-element-populated">
                            <div
                                class="elementor-element elementor-element-516c57b elementor-widget elementor-widget-homepro_banner"
                                data-id="516c57b"
                                data-element_type="widget"
                                data-widget_type="homepro_banner.default"
                            >
                                <div class="elementor-widget-container">
                                    <section class="main-slider style1">
                                        <div class="slider-box">
                                            <!-- Banner Carousel -->
                                            <div
                                                class="banner-carousel owl-theme owl-carousel owl-loaded owl-drag"
                                            >
                                                <!-- Slide -->

                                                <div class="owl-stage-outer">
                                                    <div
                                                        class="owl-stage"
                                                        style="
                                                            transform: translate3d(0px, 0px, 0px);
                                                            transition: all;
                                                        "
                                                    >
                                                        <div class="owl-item">
                                                            <div class="slide">
                                                                <div class="slide-shape1"></div>
                                                                <div class="slide-shape2"></div>
                                                                <div class="slide-shape3"></div>
                                                                <div class="slide-shape4">
                                                                    <img
                                                                        decoding="async"
                                                                        width="1920"
                                                                        height="682"
                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/07/banner1-m.jpg"
                                                                        class="zoom-fade"
                                                                        alt=""
                                                                    />
                                                                </div>
                                                                <div class="auto-container">
                                                                    <div class="content">
                                                                        <div class="big-title">
                                                                            <h2></h2>
                                                                        </div>
                                                                        <div class="text">
                                                                            <p></p>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="owl-nav disabled">
                                                    <button
                                                        type="button"
                                                        role="presentation"
                                                        class="owl-prev"
                                                    >
                                                        <span class="flaticon-left-arrow-1"></span></button
                                                    ><button
                                                        type="button"
                                                        role="presentation"
                                                        class="owl-next"
                                                    >
                                                        <span class="flaticon-left-arrow-1 right"></span>
                                                    </button>
                                                </div>
                                                <div class="owl-dots disabled">
                                                    <button role="button" class="owl-dot active">
                                                        <span></span>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </section>
                                    <!-- End Main Slider -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section
                class="elementor-section elementor-top-section elementor-element elementor-element-3744a41 elementor-section-full_width elementor-section-height-default elementor-section-height-default"
                data-id="3744a41"
                data-element_type="section"
            >
                <div class="elementor-container elementor-column-gap-no">
                    <div
                        class="elementor-column elementor-col-100 elementor-top-column elementor-element elementor-element-13c0397"
                        data-id="13c0397"
                        data-element_type="column"
                    >
                        <div class="elementor-widget-wrap elementor-element-populated">
                            <div
                                class="elementor-element elementor-element-a40ea0e elementor-widget elementor-widget-image"
                                data-id="a40ea0e"
                                data-element_type="widget"
                                data-widget_type="image.default"
                            >
                                <div class="elementor-widget-container">
                                    <style>
                                        /*! elementor - v3.21.0 - 08-05-2024 */
                                        .elementor-widget-image {
                                            text-align: center;
                                        }
                                        .elementor-widget-image a {
                                            display: inline-block;
                                        }
                                        .elementor-widget-image a img[src$='.svg'] {
                                            width: 48px;
                                        }
                                        .elementor-widget-image img {
                                            vertical-align: middle;
                                            display: inline-block;
                                        }
                                    </style>
                                    <img
                                        decoding="async"
                                        width="2560"
                                        height="975"
                                        src="https://baoloccenter.com/wp-content/uploads/2024/12/COVER-FACEBOOK-2-scaled.jpg"
                                        class="attachment-full size-full wp-image-4182"
                                        alt=""
                                        srcset="
                                            https://baoloccenter.com/wp-content/uploads/2024/12/COVER-FACEBOOK-2-scaled.jpg   2560w,
                                            https://baoloccenter.com/wp-content/uploads/2024/12/COVER-FACEBOOK-2-300x114.jpg   300w,
                                            https://baoloccenter.com/wp-content/uploads/2024/12/COVER-FACEBOOK-2-1024x390.jpg 1024w,
                                            https://baoloccenter.com/wp-content/uploads/2024/12/COVER-FACEBOOK-2-768x292.jpg   768w,
                                            https://baoloccenter.com/wp-content/uploads/2024/12/COVER-FACEBOOK-2-1536x585.jpg 1536w,
                                            https://baoloccenter.com/wp-content/uploads/2024/12/COVER-FACEBOOK-2-2048x780.jpg 2048w
                                        "
                                        sizes="(max-width: 2560px) 100vw, 2560px"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section
                class="elementor-section elementor-top-section elementor-element elementor-element-28e826f elementor-section-full_width mbws_section_uudai elementor-section-height-default elementor-section-height-default"
                data-id="28e826f"
                data-element_type="section"
            >
                <div class="elementor-container elementor-column-gap-no">
                    <div
                        class="elementor-column elementor-col-100 elementor-top-column elementor-element elementor-element-c660791 mbws_uudaimoinhat"
                        data-id="c660791"
                        data-element_type="column"
                    >
                        <div class="elementor-widget-wrap elementor-element-populated">
                            <div
                                class="elementor-element elementor-element-7db7c31 mbws_uudai e-transform elementor-widget elementor-widget-homepro_project"
                                data-id="7db7c31"
                                data-element_type="widget"
                                data-settings='{"_transform_rotateZ_effect":{"unit":"px","size":"","sizes":[]},"_transform_rotateZ_effect_tablet":{"unit":"deg","size":"","sizes":[]},"_transform_rotateZ_effect_mobile":{"unit":"deg","size":"","sizes":[]}}'
                                data-widget_type="homepro_project.default"
                            >
                                <div class="elementor-widget-container">
                                    <!--Start Project Style2 Area-->
                                    <section class="project-style2-area rspdtop150">
                                        <div class="container">
                                            <div class="sec-title text-center">
                                                <div class="sub-title">
                                                    <p></p>
                                                </div>
                                                <h2>ƯU ĐÃI MỚI NHẤT</h2>
                                            </div>
                                            <div class="row text-right-rtl">
                                                <!--Start Single project Item-->
                                                <div class="col-xl-6 col-lg-6">
                                                    <div class="single-project-item">
                                                        <div class="img-holder">
                                                            <div class="inner">
                                                                <img
                                                                    loading="lazy"
                                                                    decoding="async"
                                                                    width="2362"
                                                                    height="2362"
                                                                    src="https://baoloccenter.com/wp-content/uploads/2024/10/POST-1-1.jpg"
                                                                    class="attachment-full size-full"
                                                                    alt=""
                                                                />
                                                                <div class="zoom-button">
                                                                    <a
                                                                        class="lightbox-image"
                                                                        data-fancybox="gallery"
                                                                        href="https://baoloccenter.com/wp-content/uploads/2024/10/POST-1-1.jpg"
                                                                    >
                                                                        <i class="flaticon-plus"></i>
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="text-holder">
                                                            <h4>
                                                                <a href="/khuyen-mai/"
                                                                    >TOOKI RA MẮT SIÊU COMBO HẤP DẪN</a
                                                                >
                                                            </h4>
                                                            <div class="category">
                                                                <div class="border-box"></div>
                                                                <p></p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--End Single project Item-->
                                                <!--Start Single project Item-->
                                                <div class="col-xl-6 col-lg-6">
                                                    <div class="single-project-item">
                                                        <div class="img-holder">
                                                            <div class="inner">
                                                                <img
                                                                    loading="lazy"
                                                                    decoding="async"
                                                                    width="2362"
                                                                    height="2362"
                                                                    src="https://baoloccenter.com/wp-content/uploads/2024/12/POSTS-1.jpg"
                                                                    class="attachment-full size-full"
                                                                    alt=""
                                                                />
                                                                <div class="zoom-button">
                                                                    <a
                                                                        class="lightbox-image"
                                                                        data-fancybox="gallery"
                                                                        href="https://baoloccenter.com/wp-content/uploads/2024/12/POSTS-1.jpg"
                                                                    >
                                                                        <i class="flaticon-plus"></i>
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="text-holder">
                                                            <h4>
                                                                <a href="/khuyen-mai/">ƯU ĐÃI THÁNG 11</a>
                                                            </h4>
                                                            <div class="category">
                                                                <div class="border-box"></div>
                                                                <p></p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--End Single project Item-->
                                                <!--Start Single project Item-->
                                                <div class="col-xl-6 col-lg-6">
                                                    <div class="single-project-item">
                                                        <div class="img-holder">
                                                            <div class="inner">
                                                                <img
                                                                    loading="lazy"
                                                                    decoding="async"
                                                                    width="1201"
                                                                    height="1201"
                                                                    src="https://baoloccenter.com/wp-content/uploads/2024/09/Anh-bia-CTKM-delta.png"
                                                                    class="attachment-full size-full"
                                                                    alt=""
                                                                />
                                                                <div class="zoom-button">
                                                                    <a
                                                                        class="lightbox-image"
                                                                        data-fancybox="gallery"
                                                                        href="https://baoloccenter.com/wp-content/uploads/2024/09/Anh-bia-CTKM-delta.png"
                                                                    >
                                                                        <i class="flaticon-plus"></i>
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="text-holder">
                                                            <h4>
                                                                <a href="/khuyen-mai/"
                                                                    >GIẢM ĐẾN 50%++ CUỐI THÁNG “SĂN” ĐỒ ĐẸP
                                                                    GIÁ HỜI
                                                                    <img
                                                                        draggable="false"
                                                                        role="img"
                                                                        class="emoji"
                                                                        alt="🔥"
                                                                        src="https://s.w.org/images/core/emoji/15.0.3/svg/1f525.svg" /><img
                                                                        draggable="false"
                                                                        role="img"
                                                                        class="emoji"
                                                                        alt="🔥"
                                                                        src="https://s.w.org/images/core/emoji/15.0.3/svg/1f525.svg"
                                                                /></a>
                                                            </h4>
                                                            <div class="category">
                                                                <div class="border-box"></div>
                                                                <p></p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--End Single project Item-->
                                                <!--Start Single project Item-->
                                                <div class="col-xl-6 col-lg-6">
                                                    <div class="single-project-item">
                                                        <div class="img-holder">
                                                            <div class="inner">
                                                                <img
                                                                    loading="lazy"
                                                                    decoding="async"
                                                                    width="2362"
                                                                    height="2362"
                                                                    src="https://baoloccenter.com/wp-content/uploads/2024/10/POST-3.jpg"
                                                                    class="attachment-full size-full"
                                                                    alt=""
                                                                />
                                                                <div class="zoom-button">
                                                                    <a
                                                                        class="lightbox-image"
                                                                        data-fancybox="gallery"
                                                                        href="https://baoloccenter.com/wp-content/uploads/2024/10/POST-3.jpg"
                                                                    >
                                                                        <i class="flaticon-plus"></i>
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="text-holder">
                                                            <h4>
                                                                <a href="/khuyen-mai/"
                                                                    >ORCHARD PREMIUM MỪNG NGÀY QUỐC TẾ PHỤ NỮ
                                                                    20/10</a
                                                                >
                                                            </h4>
                                                            <div class="category">
                                                                <div class="border-box"></div>
                                                                <p></p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--End Single project Item-->
                                            </div>
                                        </div>
                                    </section>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section
                class="elementor-section elementor-top-section elementor-element elementor-element-712bf05 elementor-section-full_width elementor-section-height-default elementor-section-height-default"
                data-id="712bf05"
                data-element_type="section"
            >
                <div class="elementor-container elementor-column-gap-no">
                    <div
                        class="elementor-column elementor-col-100 elementor-top-column elementor-element elementor-element-743b051"
                        data-id="743b051"
                        data-element_type="column"
                    >
                        <div class="elementor-widget-wrap elementor-element-populated">
                            <div
                                class="elementor-element elementor-element-a71ab01 mbws_tinnoibat elementor-widget elementor-widget-homepro_features"
                                data-id="a71ab01"
                                data-element_type="widget"
                                data-widget_type="homepro_features.default"
                            >
                                <div class="elementor-widget-container">
                                    <!--Start Features Style1 Area-->
                                    <section class="features-style1-area">
                                        <div class="gradient-bg"></div>
                                        <div class="shape1"></div>
                                        <div class="container">
                                            <div class="sec-title text-center">
                                                <div class="sub-title">
                                                    <p></p>
                                                </div>
                                                <h2>TIN NỔI BẬT VÀ SỰ KIỆN</h2>
                                            </div>
                                            <div class="row">
                                                <!--Start Single Features Style1-->
                                                <div class="col-xl-4 col-lg-4">
                                                    <div class="single-features-style1 text-center">
                                                        <div class="icon">
                                                            <img
                                                                loading="lazy"
                                                                decoding="async"
                                                                width="2048"
                                                                height="1365"
                                                                src="https://baoloccenter.com/wp-content/uploads/2024/09/HMG03789-copy.jpeg"
                                                                class="attachment-full size-full"
                                                                alt=""
                                                            />
                                                        </div>
                                                        <div class="text">
                                                            <h4>
                                                                <a
                                                                    href="/💙-chao-don-cac-thi-sinh-miss-cosmo-den-voi-bao-loc-center-💙/"
                                                                    >Chào đón các thí sinh Miss Cosmo đến với
                                                                    Bao Loc Center</a
                                                                >
                                                            </h4>
                                                            <p>
                                                                Bao Loc Center vinh hạnh khi đón tiếp các thí
                                                                sinh Miss Cosmo đến tham quan và mua sắm.
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--End Single Features Style1-->
                                                <!--Start Single Features Style1-->
                                                <div class="col-xl-4 col-lg-4">
                                                    <div class="single-features-style1 text-center">
                                                        <div class="icon">
                                                            <img
                                                                loading="lazy"
                                                                decoding="async"
                                                                width="2560"
                                                                height="1920"
                                                                src="https://baoloccenter.com/wp-content/uploads/2024/10/a640ebb9e6ae5ef007bf64.jpg"
                                                                class="attachment-full size-full"
                                                                alt=""
                                                            />
                                                        </div>
                                                        <div class="text">
                                                            <h4>
                                                                <a href="/tin-tuc-va-su-kien/"
                                                                    >Khai Trương Havaianas</a
                                                                >
                                                            </h4>
                                                            <p>
                                                                Chào đón cửa hàng Havaianas mới toanh tại TTTM
                                                                Bao Loc Center
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--End Single Features Style1-->
                                                <!--Start Single Features Style1-->
                                                <div class="col-xl-4 col-lg-4">
                                                    <div class="single-features-style1 text-center">
                                                        <div class="icon">
                                                            <img
                                                                loading="lazy"
                                                                decoding="async"
                                                                width="2560"
                                                                height="1920"
                                                                src="https://baoloccenter.com/wp-content/uploads/2024/09/1a261033e3b344ed1da2.jpg"
                                                                class="attachment-full size-full"
                                                                alt=""
                                                            />
                                                        </div>
                                                        <div class="text">
                                                            <h4>
                                                                <a href="/tin-tuc-va-su-kien/"
                                                                    >Khai Trương Delta</a
                                                                >
                                                            </h4>
                                                            <p>
                                                                Chào đón cửa hàng Delta mới toanh tại TTTM Bao
                                                                Loc Center
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--End Single Features Style1-->
                                                <!--Start Single Features Style1-->
                                                <div class="col-xl-4 col-lg-4">
                                                    <div class="single-features-style1 text-center">
                                                        <div class="icon">
                                                            <img
                                                                loading="lazy"
                                                                decoding="async"
                                                                width="2560"
                                                                height="1920"
                                                                src="https://baoloccenter.com/wp-content/uploads/2024/09/c1136d959c153b4b6204.jpg"
                                                                class="attachment-full size-full"
                                                                alt=""
                                                            />
                                                        </div>
                                                        <div class="text">
                                                            <h4>
                                                                <a href="/tin-tuc-va-su-kien/"
                                                                    >Khai Trương Hummel</a
                                                                >
                                                            </h4>
                                                            <p>
                                                                Chào đón cửa hàng Hummel mới toanh tại TTTM
                                                                Bao Loc Center
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--End Single Features Style1-->
                                                <!--Start Single Features Style1-->
                                                <div class="col-xl-4 col-lg-4">
                                                    <div class="single-features-style1 text-center">
                                                        <div class="icon">
                                                            <img
                                                                loading="lazy"
                                                                decoding="async"
                                                                width="2560"
                                                                height="1920"
                                                                src="https://baoloccenter.com/wp-content/uploads/2024/12/0e8f93a37477cc299566.jpg"
                                                                class="attachment-full size-full"
                                                                alt=""
                                                            />
                                                        </div>
                                                        <div class="text">
                                                            <h4>
                                                                <a href="/tin-tuc-va-su-kien/"
                                                                    >Lễ Hội Halloween</a
                                                                >
                                                            </h4>
                                                            <p>
                                                                Chương trình vẽ mặt miễn phí cùng hóa trang cà
                                                                kheo và robot khổng lồ
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--End Single Features Style1-->
                                                <!--Start Single Features Style1-->
                                                <div class="col-xl-4 col-lg-4">
                                                    <div class="single-features-style1 text-center">
                                                        <div class="icon">
                                                            <img
                                                                loading="lazy"
                                                                decoding="async"
                                                                width="1440"
                                                                height="1080"
                                                                src="https://baoloccenter.com/wp-content/uploads/2024/12/470245437_122129922620459618_4903762542530572045_n.jpg"
                                                                class="attachment-full size-full"
                                                                alt=""
                                                            />
                                                        </div>
                                                        <div class="text">
                                                            <h4>
                                                                <a href="/tin-tuc-va-su-kien/"
                                                                    >Đêm nhạc Hòa Nhịp Giáng Sinh</a
                                                                >
                                                            </h4>
                                                            <p>
                                                                Đêm nhạc sôi động diễn ra vào ngày 14.12.2024
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--End Single Features Style1-->
                                            </div>
                                        </div>
                                    </section>
                                    <!--End Features Style1 Area-->
                                </div>
                            </div>
                            <div
                                class="elementor-element elementor-element-c12bbb5 mbws_card12 elementor-widget elementor-widget-homepro_choose"
                                data-id="c12bbb5"
                                data-element_type="widget"
                                data-widget_type="homepro_choose.default"
                            >
                                <div class="elementor-widget-container">
                                    <!--Start Choose Style1 Area-->
                                    <section class="choose-style1-area">
                                        <div class="thm-shape-bg1" style="background-image: url()"></div>
                                        <div class="shape1">
                                            <img
                                                loading="lazy"
                                                decoding="async"
                                                width="2560"
                                                height="1176"
                                                src="https://baoloccenter.com/wp-content/uploads/2024/09/p-01-3-scaled.jpg"
                                                class="attachment-full size-full"
                                                alt=""
                                            />
                                        </div>
                                        <div class="container">
                                            <div class="row text-right-rtl">
                                                <div class="col-xl-6 col-lg-6">
                                                    <div class="choose-style1__title">
                                                        <div class="sec-title">
                                                            <div class="sub-title">
                                                                <p></p>
                                                            </div>
                                                            <h2>ĐĂNG KÝ THẺ THÀNH VIÊN</h2>
                                                        </div>
                                                        <div class="inner-text">
                                                            <p></p>
                                                            <div class="btns-box">
                                                                <a
                                                                    class="btn-one style2"
                                                                    href="/the-thanh-vien/"
                                                                >
                                                                    <span class="txt">Đăng kí ngay</span>
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-xl-6 col-lg-6">
                                                    <div class="choose-style__content">
                                                        <div class="shape2"></div>
                                                        <ul>
                                                            <li
                                                                class="wow slideInLeft"
                                                                style="visibility: visible"
                                                            >
                                                                <div class="inner">
                                                                    <div class="icon icon-1">
                                                                        <svg
                                                                            xmlns="http://www.w3.org/2000/svg"
                                                                            xmlns:xlink="http://www.w3.org/1999/xlink"
                                                                            id="Layer_1"
                                                                            x="0px"
                                                                            y="0px"
                                                                            width="306px"
                                                                            height="197px"
                                                                            viewBox="0 0 306 197"
                                                                            xml:space="preserve"
                                                                        >
                                                                            <image
                                                                                id="image0"
                                                                                width="306"
                                                                                height="197"
                                                                                x="0"
                                                                                y="0"
                                                                                xlink:href="data:image/png;base64,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"
                                                                            ></image>
                                                                        </svg>
                                                                    </div>
                                                                    <div class="text">
                                                                        <h3>GOLD CARD</h3>
                                                                        <p>
                                                                            Nhận Voucher 100.000đ vào ngày
                                                                            sinh khi mua sắm với hóa đơn từ
                                                                            1.000.000đ. <br />Quà Tết Nguyên
                                                                            Đán. <br />Tặng điểm thưởng khi
                                                                            mua sắm vào những ngày lễ lớn
                                                                            trong năm.
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                            </li>
                                                            <li
                                                                class="wow slideInLeft"
                                                                style="visibility: visible"
                                                            >
                                                                <div class="inner">
                                                                    <div class="icon icon-2">
                                                                        <svg
                                                                            xmlns="http://www.w3.org/2000/svg"
                                                                            xmlns:xlink="http://www.w3.org/1999/xlink"
                                                                            id="Layer_1"
                                                                            x="0px"
                                                                            y="0px"
                                                                            width="301px"
                                                                            height="197px"
                                                                            viewBox="0 0 301 197"
                                                                            xml:space="preserve"
                                                                        >
                                                                            <image
                                                                                id="image0"
                                                                                width="301"
                                                                                height="197"
                                                                                x="0"
                                                                                y="0"
                                                                                xlink:href="data:image/png;base64,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"
                                                                            ></image>
                                                                        </svg>
                                                                    </div>
                                                                    <div class="text">
                                                                        <h3>DIAMOND CARD</h3>
                                                                        <p>
                                                                            Nhận Voucher 300.000đ vào ngày
                                                                            sinh khi mua sắm với hóa đơn từ
                                                                            1.000.000đ. <br />Quà Tết Nguyên
                                                                            Đán. <br />Tặng điểm thưởng khi
                                                                            mua sắm vào những ngày lễ lớn
                                                                            trong năm.
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </section>
                                    <!--End Choose Style1 Area-->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section
                class="elementor-section elementor-top-section elementor-element elementor-element-4b0640b elementor-section-full_width mbws_videobaoloccnter elementor-section-height-default elementor-section-height-default"
                data-id="4b0640b"
                data-element_type="section"
            >
                <div class="elementor-container elementor-column-gap-no">
                    <div
                        class="elementor-column elementor-col-100 elementor-top-column elementor-element elementor-element-0711093"
                        data-id="0711093"
                        data-element_type="column"
                    >
                        <div class="elementor-widget-wrap elementor-element-populated">
                            <div
                                class="elementor-element elementor-element-c46e4b4 elementor-widget elementor-widget-heading"
                                data-id="c46e4b4"
                                data-element_type="widget"
                                data-widget_type="heading.default"
                            >
                                <div class="elementor-widget-container">
                                    <style>
                                        /*! elementor - v3.21.0 - 08-05-2024 */
                                        .elementor-heading-title {
                                            padding: 0;
                                            margin: 0;
                                            line-height: 1;
                                        }
                                        .elementor-widget-heading
                                            .elementor-heading-title[class*='elementor-size-']
                                            > a {
                                            color: inherit;
                                            font-size: inherit;
                                            line-height: inherit;
                                        }
                                        .elementor-widget-heading
                                            .elementor-heading-title.elementor-size-small {
                                            font-size: 15px;
                                        }
                                        .elementor-widget-heading
                                            .elementor-heading-title.elementor-size-medium {
                                            font-size: 19px;
                                        }
                                        .elementor-widget-heading
                                            .elementor-heading-title.elementor-size-large {
                                            font-size: 29px;
                                        }
                                        .elementor-widget-heading .elementor-heading-title.elementor-size-xl {
                                            font-size: 39px;
                                        }
                                        .elementor-widget-heading
                                            .elementor-heading-title.elementor-size-xxl {
                                            font-size: 59px;
                                        }
                                    </style>
                                    <h2 class="elementor-heading-title elementor-size-default">
                                        VIDEO GIỚI THIỆU BẢO LỘC CENTER
                                    </h2>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section
                class="elementor-section elementor-top-section elementor-element elementor-element-5dce4c1 elementor-section-full_width elementor-section-height-default elementor-section-height-default"
                data-id="5dce4c1"
                data-element_type="section"
            >
                <div class="elementor-container elementor-column-gap-no">
                    <div
                        class="elementor-column elementor-col-100 elementor-top-column elementor-element elementor-element-2ac1047"
                        data-id="2ac1047"
                        data-element_type="column"
                    >
                        <div class="elementor-widget-wrap elementor-element-populated">
                            <div
                                class="elementor-element elementor-element-f3e639f mbws_videobaoloc elementor-widget elementor-widget-homepro_video_gallery"
                                data-id="f3e639f"
                                data-element_type="widget"
                                data-widget_type="homepro_video_gallery.default"
                            >
                                <div class="elementor-widget-container">
                                    <!--Start Video Gallery Style1 Area-->
                                    <section class="video-gallery-style1">
                                        <div class="container">
                                            <div class="video-gallery-style1_content">
                                                <div
                                                    class="video-gallery-img"
                                                    style="background-image: url()"
                                                >
                                                    <div class="video-gallery-inner">
                                                        <div class="icon">
                                                            <a
                                                                class="video-popup"
                                                                title="Video Gallery"
                                                                href="https://baoloccenter.com/wp-content/uploads/2024/09/VIDEO-PHOI-CANH-3D-TONG-THE-DU-AN-1.mp4"
                                                            >
                                                                <span class="flaticon-play-button"></span>
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </section>
                                    <!--End Video Gallery Style1 Area-->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section
                class="elementor-section elementor-top-section elementor-element elementor-element-7df098c elementor-section-full_width elementor-hidden-desktop elementor-hidden-tablet elementor-hidden-mobile elementor-section-height-default elementor-section-height-default"
                data-id="7df098c"
                data-element_type="section"
            >
                <div class="elementor-container elementor-column-gap-no">
                    <div
                        class="elementor-column elementor-col-100 elementor-top-column elementor-element elementor-element-9749dd3"
                        data-id="9749dd3"
                        data-element_type="column"
                    >
                        <div class="elementor-widget-wrap elementor-element-populated">
                            <div
                                class="elementor-element elementor-element-34e20ff mbws_thuonghieu elementor-widget elementor-widget-homepro_features"
                                data-id="34e20ff"
                                data-element_type="widget"
                                data-widget_type="homepro_features.default"
                            >
                                <div class="elementor-widget-container">
                                    <!--Start Features Style1 Area-->
                                    <section class="features-style1-area">
                                        <div class="gradient-bg"></div>
                                        <div class="shape1"></div>
                                        <div class="container">
                                            <div class="sec-title text-center">
                                                <div class="sub-title">
                                                    <p></p>
                                                </div>
                                                <h2>CÁC THƯƠNG HIỆU NỔI BẬT TẠI BAO LOC CENTER</h2>
                                            </div>
                                            <div class="row">
                                                <!--Start Single Features Style1-->
                                                <div class="col-xl-4 col-lg-4">
                                                    <div class="single-features-style1 text-center">
                                                        <div class="icon">
                                                            <img
                                                                loading="lazy"
                                                                decoding="async"
                                                                width="500"
                                                                height="353"
                                                                src="https://baoloccenter.com/wp-content/uploads/2024/07/jb1.jpg"
                                                                class="attachment-full size-full"
                                                                alt=""
                                                            />
                                                        </div>
                                                        <div class="text">
                                                            <h4>
                                                                <a
                                                                    href="/kume-khai-truong-cua-hang-tai-bao-loc-center/"
                                                                    >JOLLIBEE</a
                                                                >
                                                            </h4>
                                                            <p>KHỐI A - TẦNG 1</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--End Single Features Style1-->
                                                <!--Start Single Features Style1-->
                                                <div class="col-xl-4 col-lg-4">
                                                    <div class="single-features-style1 text-center">
                                                        <div class="icon">
                                                            <img
                                                                loading="lazy"
                                                                decoding="async"
                                                                width="500"
                                                                height="353"
                                                                src="https://baoloccenter.com/wp-content/uploads/2024/07/tx1.jpg"
                                                                class="attachment-full size-full"
                                                                alt=""
                                                            />
                                                        </div>
                                                        <div class="text">
                                                            <h4>
                                                                <a
                                                                    href="/khai-truong-cua-hang-sakos-tai-bao-loc-center/"
                                                                    >TEXAS CHICKEN</a
                                                                >
                                                            </h4>
                                                            <p>KHỐI A - TẦNG 1</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--End Single Features Style1-->
                                                <!--Start Single Features Style1-->
                                                <div class="col-xl-4 col-lg-4">
                                                    <div class="single-features-style1 text-center">
                                                        <div class="icon">
                                                            <img
                                                                loading="lazy"
                                                                decoding="async"
                                                                width="500"
                                                                height="353"
                                                                src="https://baoloccenter.com/wp-content/uploads/2024/07/mn1.jpg"
                                                                class="attachment-full size-full"
                                                                alt=""
                                                            />
                                                        </div>
                                                        <div class="text">
                                                            <h4><a href="">MINISO</a></h4>
                                                            <p>KHỐI A - TẦNG 1</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--End Single Features Style1-->
                                                <!--Start Single Features Style1-->
                                                <div class="col-xl-4 col-lg-4">
                                                    <div class="single-features-style1 text-center">
                                                        <div class="icon"></div>
                                                        <div class="text">
                                                            <h4><a href="">V - SIXTYFOUR</a></h4>
                                                            <p>KHỐI A - TẦNG 1</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--End Single Features Style1-->
                                                <!--Start Single Features Style1-->
                                                <div class="col-xl-4 col-lg-4">
                                                    <div class="single-features-style1 text-center">
                                                        <div class="icon">
                                                            <img
                                                                loading="lazy"
                                                                decoding="async"
                                                                width="500"
                                                                height="353"
                                                                src="https://baoloccenter.com/wp-content/uploads/2024/07/humel1.jpg"
                                                                class="attachment-full size-full"
                                                                alt=""
                                                            />
                                                        </div>
                                                        <div class="text">
                                                            <h4><a href="">HUMMEL</a></h4>
                                                            <p>KHỐI A - TẦNG 1</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--End Single Features Style1-->
                                                <!--Start Single Features Style1-->
                                                <div class="col-xl-4 col-lg-4">
                                                    <div class="single-features-style1 text-center">
                                                        <div class="icon">
                                                            <img
                                                                loading="lazy"
                                                                decoding="async"
                                                                width="500"
                                                                height="353"
                                                                src="https://baoloccenter.com/wp-content/uploads/2024/07/orc.jpg"
                                                                class="attachment-full size-full"
                                                                alt=""
                                                            />
                                                        </div>
                                                        <div class="text">
                                                            <h4><a href="">ORCHARD</a></h4>
                                                            <p>KHỐI A - TẦNG 1</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--End Single Features Style1-->
                                                <!--Start Single Features Style1-->
                                                <div class="col-xl-4 col-lg-4">
                                                    <div class="single-features-style1 text-center">
                                                        <div class="icon">
                                                            <img
                                                                loading="lazy"
                                                                decoding="async"
                                                                width="500"
                                                                height="353"
                                                                src="https://baoloccenter.com/wp-content/uploads/2024/07/el1.jpg"
                                                                class="attachment-full size-full"
                                                                alt=""
                                                            />
                                                        </div>
                                                        <div class="text">
                                                            <h4><a href="">ELLE</a></h4>
                                                            <p>KHỐI A - TẦNG 1</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--End Single Features Style1-->
                                                <!--Start Single Features Style1-->
                                                <div class="col-xl-4 col-lg-4">
                                                    <div class="single-features-style1 text-center">
                                                        <div class="icon">
                                                            <img
                                                                loading="lazy"
                                                                decoding="async"
                                                                width="500"
                                                                height="353"
                                                                src="https://baoloccenter.com/wp-content/uploads/2024/07/del1.jpg"
                                                                class="attachment-full size-full"
                                                                alt=""
                                                            />
                                                        </div>
                                                        <div class="text">
                                                            <h4><a href="">DEALTA SPORT</a></h4>
                                                            <p>KHỐI A - TẦNG 2</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--End Single Features Style1-->
                                                <!--Start Single Features Style1-->
                                                <div class="col-xl-4 col-lg-4">
                                                    <div class="single-features-style1 text-center">
                                                        <div class="icon">
                                                            <img
                                                                loading="lazy"
                                                                decoding="async"
                                                                width="500"
                                                                height="353"
                                                                src="https://baoloccenter.com/wp-content/uploads/2024/07/sakos1.jpg"
                                                                class="attachment-full size-full"
                                                                alt=""
                                                            />
                                                        </div>
                                                        <div class="text">
                                                            <h4><a href="">SAKOS</a></h4>
                                                            <p>KHỐI A - TẦNG 2</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--End Single Features Style1-->
                                                <!--Start Single Features Style1-->
                                                <div class="col-xl-4 col-lg-4">
                                                    <div class="single-features-style1 text-center">
                                                        <div class="icon">
                                                            <img
                                                                loading="lazy"
                                                                decoding="async"
                                                                width="500"
                                                                height="353"
                                                                src="https://baoloccenter.com/wp-content/uploads/2021/10/kume1.jpg"
                                                                class="attachment-full size-full"
                                                                alt=""
                                                            />
                                                        </div>
                                                        <div class="text">
                                                            <h4><a href="">KUME</a></h4>
                                                            <p>KHỐI A - TẦNG 2</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--End Single Features Style1-->
                                                <!--Start Single Features Style1-->
                                                <div class="col-xl-4 col-lg-4">
                                                    <div class="single-features-style1 text-center">
                                                        <div class="icon">
                                                            <img
                                                                loading="lazy"
                                                                decoding="async"
                                                                width="500"
                                                                height="353"
                                                                src="https://baoloccenter.com/wp-content/uploads/2024/07/sg1.jpg"
                                                                class="attachment-full size-full"
                                                                alt=""
                                                            />
                                                        </div>
                                                        <div class="text">
                                                            <h4><a href="">STARGO</a></h4>
                                                            <p>KHỐI A - TẦNG 2</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--End Single Features Style1-->
                                                <!--Start Single Features Style1-->
                                                <div class="col-xl-4 col-lg-4">
                                                    <div class="single-features-style1 text-center">
                                                        <div class="icon">
                                                            <img
                                                                loading="lazy"
                                                                decoding="async"
                                                                width="500"
                                                                height="353"
                                                                src="https://baoloccenter.com/wp-content/uploads/2024/07/lug.jpg"
                                                                class="attachment-full size-full"
                                                                alt=""
                                                            />
                                                        </div>
                                                        <div class="text">
                                                            <h4><a href="">LUG.VN</a></h4>
                                                            <p>KHỐI A - TẦNG 1</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--End Single Features Style1-->
                                                <!--Start Single Features Style1-->
                                                <div class="col-xl-4 col-lg-4">
                                                    <div class="single-features-style1 text-center">
                                                        <div class="icon">
                                                            <img
                                                                loading="lazy"
                                                                decoding="async"
                                                                width="500"
                                                                height="353"
                                                                src="https://baoloccenter.com/wp-content/uploads/2024/07/sam.jpg"
                                                                class="attachment-full size-full"
                                                                alt=""
                                                            />
                                                        </div>
                                                        <div class="text">
                                                            <h4><a href="">SAMSONITE</a></h4>
                                                            <p>KHỐI A - TẦNG 1</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--End Single Features Style1-->
                                                <!--Start Single Features Style1-->
                                                <div class="col-xl-4 col-lg-4">
                                                    <div class="single-features-style1 text-center">
                                                        <div class="icon">
                                                            <img
                                                                loading="lazy"
                                                                decoding="async"
                                                                width="500"
                                                                height="353"
                                                                src="https://baoloccenter.com/wp-content/uploads/2024/07/manh.jpg"
                                                                class="attachment-full size-full"
                                                                alt=""
                                                            />
                                                        </div>
                                                        <div class="text">
                                                            <h4><a href="">MINH ANH LEATHER</a></h4>
                                                            <p>KHỐI A - TẦNG 1</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--End Single Features Style1-->
                                                <!--Start Single Features Style1-->
                                                <div class="col-xl-4 col-lg-4">
                                                    <div class="single-features-style1 text-center">
                                                        <div class="icon">
                                                            <img
                                                                loading="lazy"
                                                                decoding="async"
                                                                width="500"
                                                                height="353"
                                                                src="https://baoloccenter.com/wp-content/uploads/2024/07/cmc1.jpg"
                                                                class="attachment-full size-full"
                                                                alt=""
                                                            />
                                                        </div>
                                                        <div class="text">
                                                            <h4><a href="">CMC LEATHER</a></h4>
                                                            <p>KHỐI A - TẦNG 1</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--End Single Features Style1-->
                                                <!--Start Single Features Style1-->
                                                <div class="col-xl-4 col-lg-4">
                                                    <div class="single-features-style1 text-center">
                                                        <div class="icon">
                                                            <img
                                                                loading="lazy"
                                                                decoding="async"
                                                                width="500"
                                                                height="353"
                                                                src="https://baoloccenter.com/wp-content/uploads/2024/07/vns1.jpg"
                                                                class="attachment-full size-full"
                                                                alt=""
                                                            />
                                                        </div>
                                                        <div class="text">
                                                            <h4><a href="">VINASILK</a></h4>
                                                            <p>KHỐI A - TẦNG 1</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--End Single Features Style1-->
                                                <!--Start Single Features Style1-->
                                                <div class="col-xl-4 col-lg-4">
                                                    <div class="single-features-style1 text-center">
                                                        <div class="icon">
                                                            <img
                                                                loading="lazy"
                                                                decoding="async"
                                                                width="500"
                                                                height="353"
                                                                src="https://baoloccenter.com/wp-content/uploads/2024/07/fahasa1.jpg"
                                                                class="attachment-full size-full"
                                                                alt=""
                                                            />
                                                        </div>
                                                        <div class="text">
                                                            <h4><a href="">NHÀ SÁCH FAHASA</a></h4>
                                                            <p>KHỐI A - TẦNG 1</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--End Single Features Style1-->
                                            </div>
                                        </div>
                                    </section>
                                    <!--End Features Style1 Area-->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section
                class="elementor-section elementor-top-section elementor-element elementor-element-1582407 elementor-section-full_width elementor-section-height-default elementor-section-height-default"
                data-id="1582407"
                data-element_type="section"
            >
                <div class="elementor-container elementor-column-gap-no">
                    <div
                        class="elementor-column elementor-col-100 elementor-top-column elementor-element elementor-element-6af50a5"
                        data-id="6af50a5"
                        data-element_type="column"
                    >
                        <div class="elementor-widget-wrap elementor-element-populated">
                            <div
                                class="elementor-element elementor-element-04bad47 mbws_thuonghieunoibatnew elementor-widget elementor-widget-homepro_testimonials"
                                data-id="04bad47"
                                data-element_type="widget"
                                data-widget_type="homepro_testimonials.default"
                            >
                                <div class="elementor-widget-container">
                                    <!--Start Testimonials Style1 area -->
                                    <section class="testimonials-style1-area">
                                        <div class="container">
                                            <div class="sec-title text-center">
                                                <div class="sub-title">
                                                    <p></p>
                                                </div>
                                                <h2>CÁC THƯƠNG HIỆU TẠI BAO LOC CENTER</h2>
                                            </div>
                                            <div class="row">
                                                <div class="col-xl-12">
                                                    <div class="testimonials-style1__content">
                                                        <div
                                                            class="theme_carousel testimonials-carousel_1 owl-dot-style1 owl-theme owl-carousel owl-loaded owl-drag"
                                                            data-options='{"loop": true, "margin": 30, "autoheight":true, "lazyload":true, "nav": false, "dots": true, "autoplay": true, "autoplayTimeout": 6000, "smartSpeed": 300, "responsive":{ "0" :{ "items": "1" }, "600" :{ "items" : "1" }, "768" :{ "items" : "1" } , "992":{ "items" : "2" }, "1200":{ "items" : "3" }}}'
                                                        >
                                                            <!--Start Single Testimonials Style1-->

                                                            <!--End Single Testimonials Style1-->
                                                            <!--Start Single Testimonials Style1-->

                                                            <!--End Single Testimonials Style1-->
                                                            <!--Start Single Testimonials Style1-->

                                                            <!--End Single Testimonials Style1-->
                                                            <!--Start Single Testimonials Style1-->

                                                            <!--End Single Testimonials Style1-->
                                                            <!--Start Single Testimonials Style1-->

                                                            <!--End Single Testimonials Style1-->
                                                            <!--Start Single Testimonials Style1-->

                                                            <!--End Single Testimonials Style1-->
                                                            <!--Start Single Testimonials Style1-->

                                                            <!--End Single Testimonials Style1-->
                                                            <!--Start Single Testimonials Style1-->

                                                            <!--End Single Testimonials Style1-->
                                                            <!--Start Single Testimonials Style1-->

                                                            <!--End Single Testimonials Style1-->
                                                            <!--Start Single Testimonials Style1-->

                                                            <!--End Single Testimonials Style1-->
                                                            <!--Start Single Testimonials Style1-->

                                                            <!--End Single Testimonials Style1-->
                                                            <!--Start Single Testimonials Style1-->

                                                            <!--End Single Testimonials Style1-->
                                                            <!--Start Single Testimonials Style1-->

                                                            <!--End Single Testimonials Style1-->
                                                            <!--Start Single Testimonials Style1-->

                                                            <!--End Single Testimonials Style1-->
                                                            <!--Start Single Testimonials Style1-->

                                                            <!--End Single Testimonials Style1-->
                                                            <!--Start Single Testimonials Style1-->

                                                            <!--End Single Testimonials Style1-->
                                                            <!--Start Single Testimonials Style1-->

                                                            <!--End Single Testimonials Style1-->
                                                            <!--Start Single Testimonials Style1-->

                                                            <!--End Single Testimonials Style1-->
                                                            <!--Start Single Testimonials Style1-->

                                                            <!--End Single Testimonials Style1-->
                                                            <!--Start Single Testimonials Style1-->

                                                            <!--End Single Testimonials Style1-->
                                                            <!--Start Single Testimonials Style1-->

                                                            <!--End Single Testimonials Style1-->
                                                            <div class="owl-stage-outer">
                                                                <div
                                                                    class="owl-stage"
                                                                    style="
                                                                        transform: translate3d(
                                                                            -8170px,
                                                                            0px,
                                                                            0px
                                                                        );
                                                                        transition: 0.3s;
                                                                        width: 18490px;
                                                                    "
                                                                >
                                                                    <div
                                                                        class="owl-item cloned"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1080"
                                                                                        height="767"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-06.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>SAKOS</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 2</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item cloned"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1080"
                                                                                        height="766"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-08.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>STARGO</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 2</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item cloned"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1081"
                                                                                        height="766"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-13.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>SUNNY</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 2</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item cloned"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1081"
                                                                                        height="767"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-12.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>SWEET DREAM LATEX</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 2</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item cloned"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1801"
                                                                                        height="1277"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-15.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>
                                                                                        BABY LOVE - KIDS STORE
                                                                                    </h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 2</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item cloned"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1080"
                                                                                        height="767"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-09.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>
                                                                                        SUNDAY GAME - KIDS
                                                                                        ZONE
                                                                                    </h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 3</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item cloned"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1080"
                                                                                        height="767"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-11.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>
                                                                                        SUNDAY GAME - GAME
                                                                                        ZONE
                                                                                    </h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 3</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item cloned"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1080"
                                                                                        height="767"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-10.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>
                                                                                        SUNDAY GAME - OUTDOOR
                                                                                        GAME
                                                                                    </h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 1</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item cloned"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1080"
                                                                                        height="766"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-18.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>VIET SMILE</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 2</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item cloned"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1800"
                                                                                        height="1277"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/10/ANH-WEBSITE-19.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>Havaianas</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 1</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item cloned"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1080"
                                                                                        height="766"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/11/ANH-WEBSITE-20.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>D'fly</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 2</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1800"
                                                                                        height="1277"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-17.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>NHA TRANG PEARL</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 1</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1801"
                                                                                        height="1277"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-16.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>ORCHARD PREMIUM</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 1</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1080"
                                                                                        height="767"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/07/ANH-WEBSITE-02.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>TOKKI</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 1</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1080"
                                                                                        height="767"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-14.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>KUME</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 1</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="500"
                                                                                        height="353"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/07/humel1.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>HUMMEL</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 1</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1081"
                                                                                        height="767"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-03.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>KELME</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 1</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1080"
                                                                                        height="766"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/07/ANH-WEBSITE-01.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>TRẦM NGỌC VIỆT</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 1</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1080"
                                                                                        height="767"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2021/10/ANH-WEBSITE-07.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>
                                                                                        EURO VIET PERFUMERY
                                                                                    </h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 1</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item active"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1081"
                                                                                        height="766"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-04.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>DEALTA</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 2</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1080"
                                                                                        height="766"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-05.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>LOAN FASHION</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 2</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1080"
                                                                                        height="767"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-06.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>SAKOS</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 2</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1080"
                                                                                        height="766"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-08.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>STARGO</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 2</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1081"
                                                                                        height="766"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-13.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>SUNNY</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 2</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1081"
                                                                                        height="767"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-12.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>SWEET DREAM LATEX</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 2</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1801"
                                                                                        height="1277"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-15.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>
                                                                                        BABY LOVE - KIDS STORE
                                                                                    </h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 2</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1080"
                                                                                        height="767"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-09.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>
                                                                                        SUNDAY GAME - KIDS
                                                                                        ZONE
                                                                                    </h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 3</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1080"
                                                                                        height="767"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-11.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>
                                                                                        SUNDAY GAME - GAME
                                                                                        ZONE
                                                                                    </h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 3</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1080"
                                                                                        height="767"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-10.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>
                                                                                        SUNDAY GAME - OUTDOOR
                                                                                        GAME
                                                                                    </h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 1</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1080"
                                                                                        height="766"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-18.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>VIET SMILE</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 2</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1800"
                                                                                        height="1277"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/10/ANH-WEBSITE-19.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>Havaianas</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 1</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1080"
                                                                                        height="766"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/11/ANH-WEBSITE-20.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>D'fly</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 2</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item cloned"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1800"
                                                                                        height="1277"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-17.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>NHA TRANG PEARL</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 1</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item cloned"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1801"
                                                                                        height="1277"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-16.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>ORCHARD PREMIUM</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 1</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item cloned"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1080"
                                                                                        height="767"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/07/ANH-WEBSITE-02.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>TOKKI</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 1</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item cloned"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1080"
                                                                                        height="767"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-14.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>KUME</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 1</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item cloned"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="500"
                                                                                        height="353"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/07/humel1.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>HUMMEL</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 1</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item cloned"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1081"
                                                                                        height="767"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-03.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>KELME</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 1</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item cloned"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1080"
                                                                                        height="766"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/07/ANH-WEBSITE-01.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>TRẦM NGỌC VIỆT</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 1</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item cloned"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1080"
                                                                                        height="767"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2021/10/ANH-WEBSITE-07.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>
                                                                                        EURO VIET PERFUMERY
                                                                                    </h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 1</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item cloned"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1081"
                                                                                        height="766"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-04.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>DEALTA</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 2</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item cloned"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1080"
                                                                                        height="766"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-05.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>LOAN FASHION</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 2</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="owl-item cloned"
                                                                        style="
                                                                            width: 400px;
                                                                            margin-right: 30px;
                                                                        "
                                                                    >
                                                                        <div
                                                                            class="single-testimonials-style1"
                                                                        >
                                                                            <div class="client-info">
                                                                                <div class="img-box">
                                                                                    <img
                                                                                        loading="lazy"
                                                                                        decoding="async"
                                                                                        width="1080"
                                                                                        height="767"
                                                                                        src="https://baoloccenter.com/wp-content/uploads/2024/09/ANH-WEBSITE-06.jpg"
                                                                                        class="attachment-full size-full"
                                                                                        alt=""
                                                                                    />
                                                                                </div>
                                                                                <div class="title-box">
                                                                                    <h3>SAKOS</h3>
                                                                                    <span
                                                                                        >KHỐI A - TẦNG 2</span
                                                                                    >
                                                                                </div>
                                                                            </div>
                                                                            <div class="text">
                                                                                <p></p>
                                                                                <div class="date-box">
                                                                                    <p></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="owl-nav disabled">
                                                                <button
                                                                    type="button"
                                                                    role="presentation"
                                                                    class="owl-prev"
                                                                >
                                                                    <span aria-label="Previous"
                                                                        >‹</span
                                                                    ></button
                                                                ><button
                                                                    type="button"
                                                                    role="presentation"
                                                                    class="owl-next"
                                                                >
                                                                    <span aria-label="Next">›</span>
                                                                </button>
                                                            </div>
                                                            <div class="owl-dots">
                                                                <button role="button" class="owl-dot">
                                                                    <span></span></button
                                                                ><button role="button" class="owl-dot">
                                                                    <span></span></button
                                                                ><button role="button" class="owl-dot">
                                                                    <span></span></button
                                                                ><button role="button" class="owl-dot">
                                                                    <span></span></button
                                                                ><button role="button" class="owl-dot">
                                                                    <span></span></button
                                                                ><button role="button" class="owl-dot">
                                                                    <span></span></button
                                                                ><button role="button" class="owl-dot">
                                                                    <span></span></button
                                                                ><button role="button" class="owl-dot">
                                                                    <span></span></button
                                                                ><button role="button" class="owl-dot active">
                                                                    <span></span></button
                                                                ><button role="button" class="owl-dot">
                                                                    <span></span></button
                                                                ><button role="button" class="owl-dot">
                                                                    <span></span></button
                                                                ><button role="button" class="owl-dot">
                                                                    <span></span></button
                                                                ><button role="button" class="owl-dot">
                                                                    <span></span></button
                                                                ><button role="button" class="owl-dot">
                                                                    <span></span></button
                                                                ><button role="button" class="owl-dot">
                                                                    <span></span></button
                                                                ><button role="button" class="owl-dot">
                                                                    <span></span></button
                                                                ><button role="button" class="owl-dot">
                                                                    <span></span></button
                                                                ><button role="button" class="owl-dot">
                                                                    <span></span></button
                                                                ><button role="button" class="owl-dot">
                                                                    <span></span></button
                                                                ><button role="button" class="owl-dot">
                                                                    <span></span></button
                                                                ><button role="button" class="owl-dot">
                                                                    <span></span>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </section>
                                    <!--End Testimonials Style1 area -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section
                class="elementor-section elementor-top-section elementor-element elementor-element-eccf191 elementor-section-full_width elementor-section-height-default elementor-section-height-default"
                data-id="eccf191"
                data-element_type="section"
            >
                <div class="elementor-container elementor-column-gap-no">
                    <div
                        class="elementor-column elementor-col-100 elementor-top-column elementor-element elementor-element-88c1152"
                        data-id="88c1152"
                        data-element_type="column"
                    >
                        <div class="elementor-widget-wrap"></div>
                    </div>
                </div>
            </section>
        </div>
        <footer class="footer-area">
            <div data-elementor-type="page" data-elementor-id="2849" class="elementor elementor-2849">
                <section
                    class="elementor-section elementor-top-section elementor-element elementor-element-5d7318b5 elementor-section-boxed elementor-section-height-default elementor-section-height-default"
                    data-id="5d7318b5"
                    data-element_type="section"
                >
                    <div class="elementor-background-overlay"></div>
                    <div class="elementor-container elementor-column-gap-default">
                        <div
                            class="elementor-column elementor-col-25 elementor-top-column elementor-element elementor-element-4c1eda5b mbws_footer1"
                            data-id="4c1eda5b"
                            data-element_type="column"
                        >
                            <div class="elementor-widget-wrap elementor-element-populated">
                                <div
                                    class="elementor-element elementor-element-1c3a628 mbws_footerimg elementor-widget elementor-widget-image"
                                    data-id="1c3a628"
                                    data-element_type="widget"
                                    data-widget_type="image.default"
                                >
                                    <div class="elementor-widget-container">
                                        <a href="/">
                                            <img
                                                width="2074"
                                                height="1229"
                                                src="https://baoloccenter.com/wp-content/uploads/2021/10/LOGO-BAO-LOC-CENTER-white.png"
                                                class="attachment-full size-full wp-image-3730"
                                                alt=""
                                            />
                                        </a>
                                    </div>
                                </div>
                                <div
                                    class="elementor-element elementor-element-cb414b3 elementor-widget elementor-widget-homepro_footer_contact"
                                    data-id="cb414b3"
                                    data-element_type="widget"
                                    data-widget_type="homepro_footer_contact.default"
                                >
                                    <div class="elementor-widget-container">
                                        <!--Start single footer widget-->
                                        <div class="single-footer-widget margin__top pdtop50">
                                            <div class="title">
                                                <h3>Liên hệ</h3>
                                            </div>
                                            <div class="footer-widget-contact-info">
                                                <ul>
                                                    <li>
                                                        <div class="inner">
                                                            <div class="icon mapmarker">
                                                                <span class="flaticon-pin-1"></span>
                                                            </div>
                                                            <div class="text">
                                                                <p>
                                                                    280 - 280A Trần Phú, P.Lộc Sơn, TP Bảo
                                                                    Lộc, Lâm Đồng
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li>
                                                        <div class="inner">
                                                            <div class="icon mapmarker">
                                                                <span
                                                                    class="flaticon-microphone-black-shape"
                                                                ></span>
                                                            </div>
                                                            <div class="text">
                                                                <p>
                                                                    <a href="tel:0816591616">0816 59 1616</a>
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li>
                                                        <div class="inner">
                                                            <div class="icon mapmarker">
                                                                <span class="flaticon-email"></span>
                                                            </div>
                                                            <div class="text">
                                                                <p><a href="#">Đang cập nhật...</a></p>
                                                            </div>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                        <!--End single footer widget-->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div
                            class="elementor-column elementor-col-50 elementor-top-column elementor-element elementor-element-3aa9f7d7 mbws_footer2"
                            data-id="3aa9f7d7"
                            data-element_type="column"
                        >
                            <div class="elementor-widget-wrap elementor-element-populated">
                                <div
                                    class="elementor-element elementor-element-2dcb5dd elementor-widget elementor-widget-heading"
                                    data-id="2dcb5dd"
                                    data-element_type="widget"
                                    data-widget_type="heading.default"
                                >
                                    <div class="elementor-widget-container">
                                        <h4 class="elementor-heading-title elementor-size-default">
                                            Theo dõi tôi
                                        </h4>
                                    </div>
                                </div>
                                <div
                                    class="elementor-element elementor-element-98e63aa elementor-shape-circle elementor-widget__width-initial e-grid-align-left elementor-grid-0 elementor-widget elementor-widget-social-icons"
                                    data-id="98e63aa"
                                    data-element_type="widget"
                                    data-widget_type="social-icons.default"
                                >
                                    <div class="elementor-widget-container">
                                        <style>
                                            /*! elementor - v3.21.0 - 08-05-2024 */
                                            .elementor-widget-social-icons.elementor-grid-0
                                                .elementor-widget-container,
                                            .elementor-widget-social-icons.elementor-grid-mobile-0
                                                .elementor-widget-container,
                                            .elementor-widget-social-icons.elementor-grid-tablet-0
                                                .elementor-widget-container {
                                                line-height: 1;
                                                font-size: 0;
                                            }
                                            .elementor-widget-social-icons:not(.elementor-grid-0):not(
                                                    .elementor-grid-tablet-0
                                                ):not(.elementor-grid-mobile-0)
                                                .elementor-grid {
                                                display: inline-grid;
                                            }
                                            .elementor-widget-social-icons .elementor-grid {
                                                grid-column-gap: var(--grid-column-gap, 5px);
                                                grid-row-gap: var(--grid-row-gap, 5px);
                                                grid-template-columns: var(--grid-template-columns);
                                                justify-content: var(--justify-content, center);
                                                justify-items: var(--justify-content, center);
                                            }
                                            .elementor-icon.elementor-social-icon {
                                                font-size: var(--icon-size, 25px);
                                                line-height: var(--icon-size, 25px);
                                                width: calc(
                                                    var(--icon-size, 25px) + 2 * var(--icon-padding, 0.5em)
                                                );
                                                height: calc(
                                                    var(--icon-size, 25px) + 2 * var(--icon-padding, 0.5em)
                                                );
                                            }
                                            .elementor-social-icon {
                                                --e-social-icon-icon-color: #fff;
                                                display: inline-flex;
                                                background-color: #69727d;
                                                align-items: center;
                                                justify-content: center;
                                                text-align: center;
                                                cursor: pointer;
                                            }
                                            .elementor-social-icon i {
                                                color: var(--e-social-icon-icon-color);
                                            }
                                            .elementor-social-icon svg {
                                                fill: var(--e-social-icon-icon-color);
                                            }
                                            .elementor-social-icon:last-child {
                                                margin: 0;
                                            }
                                            .elementor-social-icon:hover {
                                                opacity: 0.9;
                                                color: #fff;
                                            }
                                            .elementor-social-icon-android {
                                                background-color: #a4c639;
                                            }
                                            .elementor-social-icon-apple {
                                                background-color: #999;
                                            }
                                            .elementor-social-icon-behance {
                                                background-color: #1769ff;
                                            }
                                            .elementor-social-icon-bitbucket {
                                                background-color: #205081;
                                            }
                                            .elementor-social-icon-codepen {
                                                background-color: #000;
                                            }
                                            .elementor-social-icon-delicious {
                                                background-color: #39f;
                                            }
                                            .elementor-social-icon-deviantart {
                                                background-color: #05cc47;
                                            }
                                            .elementor-social-icon-digg {
                                                background-color: #005be2;
                                            }
                                            .elementor-social-icon-dribbble {
                                                background-color: #ea4c89;
                                            }
                                            .elementor-social-icon-elementor {
                                                background-color: #d30c5c;
                                            }
                                            .elementor-social-icon-envelope {
                                                background-color: #ea4335;
                                            }
                                            .elementor-social-icon-facebook,
                                            .elementor-social-icon-facebook-f {
                                                background-color: #3b5998;
                                            }
                                            .elementor-social-icon-flickr {
                                                background-color: #0063dc;
                                            }
                                            .elementor-social-icon-foursquare {
                                                background-color: #2d5be3;
                                            }
                                            .elementor-social-icon-free-code-camp,
                                            .elementor-social-icon-freecodecamp {
                                                background-color: #006400;
                                            }
                                            .elementor-social-icon-github {
                                                background-color: #333;
                                            }
                                            .elementor-social-icon-gitlab {
                                                background-color: #e24329;
                                            }
                                            .elementor-social-icon-globe {
                                                background-color: #69727d;
                                            }
                                            .elementor-social-icon-google-plus,
                                            .elementor-social-icon-google-plus-g {
                                                background-color: #dd4b39;
                                            }
                                            .elementor-social-icon-houzz {
                                                background-color: #7ac142;
                                            }
                                            .elementor-social-icon-instagram {
                                                background-color: #262626;
                                            }
                                            .elementor-social-icon-jsfiddle {
                                                background-color: #487aa2;
                                            }
                                            .elementor-social-icon-link {
                                                background-color: #818a91;
                                            }
                                            .elementor-social-icon-linkedin,
                                            .elementor-social-icon-linkedin-in {
                                                background-color: #0077b5;
                                            }
                                            .elementor-social-icon-medium {
                                                background-color: #00ab6b;
                                            }
                                            .elementor-social-icon-meetup {
                                                background-color: #ec1c40;
                                            }
                                            .elementor-social-icon-mixcloud {
                                                background-color: #273a4b;
                                            }
                                            .elementor-social-icon-odnoklassniki {
                                                background-color: #f4731c;
                                            }
                                            .elementor-social-icon-pinterest {
                                                background-color: #bd081c;
                                            }
                                            .elementor-social-icon-product-hunt {
                                                background-color: #da552f;
                                            }
                                            .elementor-social-icon-reddit {
                                                background-color: #ff4500;
                                            }
                                            .elementor-social-icon-rss {
                                                background-color: #f26522;
                                            }
                                            .elementor-social-icon-shopping-cart {
                                                background-color: #4caf50;
                                            }
                                            .elementor-social-icon-skype {
                                                background-color: #00aff0;
                                            }
                                            .elementor-social-icon-slideshare {
                                                background-color: #0077b5;
                                            }
                                            .elementor-social-icon-snapchat {
                                                background-color: #fffc00;
                                            }
                                            .elementor-social-icon-soundcloud {
                                                background-color: #f80;
                                            }
                                            .elementor-social-icon-spotify {
                                                background-color: #2ebd59;
                                            }
                                            .elementor-social-icon-stack-overflow {
                                                background-color: #fe7a15;
                                            }
                                            .elementor-social-icon-steam {
                                                background-color: #00adee;
                                            }
                                            .elementor-social-icon-stumbleupon {
                                                background-color: #eb4924;
                                            }
                                            .elementor-social-icon-telegram {
                                                background-color: #2ca5e0;
                                            }
                                            .elementor-social-icon-threads {
                                                background-color: #000;
                                            }
                                            .elementor-social-icon-thumb-tack {
                                                background-color: #1aa1d8;
                                            }
                                            .elementor-social-icon-tripadvisor {
                                                background-color: #589442;
                                            }
                                            .elementor-social-icon-tumblr {
                                                background-color: #35465c;
                                            }
                                            .elementor-social-icon-twitch {
                                                background-color: #6441a5;
                                            }
                                            .elementor-social-icon-twitter {
                                                background-color: #1da1f2;
                                            }
                                            .elementor-social-icon-viber {
                                                background-color: #665cac;
                                            }
                                            .elementor-social-icon-vimeo {
                                                background-color: #1ab7ea;
                                            }
                                            .elementor-social-icon-vk {
                                                background-color: #45668e;
                                            }
                                            .elementor-social-icon-weibo {
                                                background-color: #dd2430;
                                            }
                                            .elementor-social-icon-weixin {
                                                background-color: #31a918;
                                            }
                                            .elementor-social-icon-whatsapp {
                                                background-color: #25d366;
                                            }
                                            .elementor-social-icon-wordpress {
                                                background-color: #21759b;
                                            }
                                            .elementor-social-icon-x-twitter {
                                                background-color: #000;
                                            }
                                            .elementor-social-icon-xing {
                                                background-color: #026466;
                                            }
                                            .elementor-social-icon-yelp {
                                                background-color: #af0606;
                                            }
                                            .elementor-social-icon-youtube {
                                                background-color: #cd201f;
                                            }
                                            .elementor-social-icon-500px {
                                                background-color: #0099e5;
                                            }
                                            .elementor-shape-rounded .elementor-icon.elementor-social-icon {
                                                border-radius: 10%;
                                            }
                                            .elementor-shape-circle .elementor-icon.elementor-social-icon {
                                                border-radius: 50%;
                                            }
                                        </style>
                                        <div class="elementor-social-icons-wrapper elementor-grid">
                                            <span class="elementor-grid-item">
                                                <a
                                                    class="elementor-icon elementor-social-icon elementor-social-icon-facebook elementor-repeater-item-abe4dd0"
                                                    target="_blank"
                                                >
                                                    <span class="elementor-screen-only">Facebook</span>
                                                    <svg
                                                        class="e-font-icon-svg e-fab-facebook"
                                                        viewBox="0 0 512 512"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                    >
                                                        <path
                                                            d="M504 256C504 119 393 8 256 8S8 119 8 256c0 123.78 90.69 226.38 209.25 245V327.69h-63V256h63v-54.64c0-62.15 37-96.48 93.67-96.48 27.14 0 55.52 4.84 55.52 4.84v61h-31.28c-30.8 0-40.41 19.12-40.41 38.73V256h68.78l-11 71.69h-57.78V501C413.31 482.38 504 379.78 504 256z"
                                                        ></path>
                                                    </svg>
                                                </a>
                                            </span>
                                            <span class="elementor-grid-item">
                                                <a
                                                    class="elementor-icon elementor-social-icon elementor-social-icon-twitter elementor-repeater-item-e5f821a"
                                                    target="_blank"
                                                >
                                                    <span class="elementor-screen-only">Twitter</span>
                                                    <svg
                                                        class="e-font-icon-svg e-fab-twitter"
                                                        viewBox="0 0 512 512"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                    >
                                                        <path
                                                            d="M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z"
                                                        ></path>
                                                    </svg>
                                                </a>
                                            </span>
                                            <span class="elementor-grid-item">
                                                <a
                                                    class="elementor-icon elementor-social-icon elementor-social-icon-youtube elementor-repeater-item-f562eb7"
                                                    target="_blank"
                                                >
                                                    <span class="elementor-screen-only">Youtube</span>
                                                    <svg
                                                        class="e-font-icon-svg e-fab-youtube"
                                                        viewBox="0 0 576 512"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                    >
                                                        <path
                                                            d="M549.655 124.083c-6.281-23.65-24.787-42.276-48.284-48.597C458.781 64 288 64 288 64S117.22 64 74.629 75.486c-23.497 6.322-42.003 24.947-48.284 48.597-11.412 42.867-11.412 132.305-11.412 132.305s0 89.438 11.412 132.305c6.281 23.65 24.787 41.5 48.284 47.821C117.22 448 288 448 288 448s170.78 0 213.371-11.486c23.497-6.321 42.003-24.171 48.284-47.821 11.412-42.867 11.412-132.305 11.412-132.305s0-89.438-11.412-132.305zm-317.51 213.508V175.185l142.739 81.205-142.739 81.201z"
                                                        ></path>
                                                    </svg>
                                                </a>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div
                            class="elementor-column elementor-col-25 elementor-top-column elementor-element elementor-element-50024670 mbws_footer3"
                            data-id="50024670"
                            data-element_type="column"
                        >
                            <div class="elementor-widget-wrap elementor-element-populated">
                                <div
                                    class="elementor-element elementor-element-fc83366 elementor-widget elementor-widget-homepro_footer_service"
                                    data-id="fc83366"
                                    data-element_type="widget"
                                    data-widget_type="homepro_footer_service.default"
                                >
                                    <div class="elementor-widget-container">
                                        <!--Start single footer widget-->
                                        <div class="single-footer-widget margin__top">
                                            <div class="title">
                                                <h3>Danh mục</h3>
                                            </div>
                                            <div class="footer-widget-links">
                                                <ul>
                                                    <li><a href="/about-us/">Về chúng tôi</a></li>
                                                    <li><a href="#">Danh sách</a></li>
                                                    <li><a href="#">Làm thế nào để nó hoạt động</a></li>
                                                    <li>
                                                        <a href="/our-projects/">Dịch vụ của chúng tôi</a>
                                                    </li>
                                                    <li><a href="/blog/">Blog của chúng tôi</a></li>
                                                    <li><a href="/contact-us/">Liên hệ chúng tôi</a></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <!--End single footer widget-->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
            <div class="footer-bottom">
                <div class="container">
                    <div class="bottom-inner">
                        <div class="copyright">
                            <p></p>
                            <div id="mbmcl">
                                <a
                                    href="https://www.matbao.ws/gui-yeu-cau-ho-tro"
                                    target="_blank"
                                    style="text-decoration: none"
                                    >Web Design &amp; Support</a
                                >
                                by <a href="https://www.matbao.ws" target="_blank"><b>Mắt Bão WS</b></a>
                            </div>
                            <p></p>
                        </div>
                        <div class="footer-menu">
                            <ul class="footer-nav"></ul>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <script type="text/javascript">
        const lazyloadRunObserver = () => {
            const lazyloadBackgrounds = document.querySelectorAll();
            const lazyloadBackgroundObserver = new IntersectionObserver(
                (entries) => {
                    entries.forEach((entry) => {
                        if (entry.isIntersecting) {
                            let lazyloadBackground = entry.target;
                            if (lazyloadBackground) {
                                lazyloadBackground.classList.add('e-lazyloaded');
                            }
                            lazyloadBackgroundObserver.unobserve(entry.target);
                        }
                    });
                },
                { rootMargin: '200px 0px 200px 0px' }
            );
            lazyloadBackgrounds.forEach((lazyloadBackground) => {
                lazyloadBackgroundObserver.observe(lazyloadBackground);
            });
        };
        const events = ['DOMContentLoaded', 'elementor/lazyload/observe'];
        events.forEach((event) => {
            document.addEventListener(event, lazyloadRunObserver);
        });
    </script>
    <link
        rel="stylesheet"
        id="elementor-post-2849-css"
        href="https://baoloccenter.com/wp-content/uploads/elementor/css/post-2849.css?ver=1727188810"
        type="text/css"
        media="all"
    />
    <link
        rel="stylesheet"
        id="elementor-icons-shared-0-css"
        href="https://baoloccenter.com/wp-content/plugins/hoppex-core/assets/elementor/icon/css/flaticon.css?ver=6.6.1"
        type="text/css"
        media="all"
    />
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/plugins/contact-form-7/includes/swv/js/index.js?ver=5.9.4"
        id="swv-js"
    ></script>
    <script type="text/javascript" id="contact-form-7-js-extra">
        /* <![CDATA[ */
        var wpcf7 = {
            api: { root: 'https:\/\/baoloccenter.com\/wp-json\/', namespace: 'contact-form-7\/v1' },
        };
        /* ]]> */
    </script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/plugins/contact-form-7/includes/js/index.js?ver=5.9.4"
        id="contact-form-7-js"
    ></script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/themes/hoppex/assets/js/aos.js?ver=1737431623"
        id="aos-js"
    ></script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/themes/hoppex/assets/js/appear.js?ver=1737431623"
        id="appear-js"
    ></script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/themes/hoppex/assets/js/bootstrap.bundle.min.js?ver=1737431623"
        id="bootstrap-bundle-js"
    ></script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/themes/hoppex/assets/js/bootstrap-select.min.js?ver=1737431623"
        id="bootstrap-select-js"
    ></script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/themes/hoppex/assets/js/isotope.js?ver=1737431623"
        id="isotope-js"
    ></script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/themes/hoppex/assets/js/jquery.countTo.js?ver=1737431623"
        id="jquery-countto-js"
    ></script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/themes/hoppex/assets/js/jquery.easing.min.js?ver=1737431623"
        id="jquery-easing-js"
    ></script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/themes/hoppex/assets/js/jquery.enllax.min.js?ver=1737431623"
        id="jquery-enllax-js"
    ></script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/themes/hoppex/assets/js/jquery.fancybox.js?ver=1737431623"
        id="jquery-fancybox-js"
    ></script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/themes/hoppex/assets/js/jquery.magnific-popup.min.js?ver=1737431623"
        id="jquery-magnific-popup-js"
    ></script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/themes/hoppex/assets/js/jquery.paroller.min.js?ver=1737431623"
        id="jquery-paroller-js"
    ></script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/themes/hoppex/assets/js/jquery-ui.js?ver=1737431623"
        id="jquery-ui-js"
    ></script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/themes/hoppex/assets/js/knob.js?ver=1737431623"
        id="knob-js"
    ></script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/themes/hoppex/assets/js/owl.js?ver=1737431623"
        id="owl-js"
    ></script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/themes/hoppex/assets/js/pagenav.js?ver=1737431623"
        id="pagenav-js"
    ></script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/themes/hoppex/assets/js/parallax.min.js?ver=1737431623"
        id="parallax-js"
    ></script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/themes/hoppex/assets/js/scrollbar.js?ver=1737431623"
        id="scrollbar-js"
    ></script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/themes/hoppex/assets/js/TweenMax.min.js?ver=1737431623"
        id="tweenmax-js"
    ></script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/themes/hoppex/assets/js/wow.js?ver=1737431623"
        id="wow-js"
    ></script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/themes/hoppex/assets/js/jquery.bootstrap-touchspin.js?ver=1737431623"
        id="jquery-bootstrap-touchspin-js"
    ></script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/themes/hoppex/assets/js/jquery.nice-select.min.js?ver=1737431623"
        id="jquery-nice-select-js"
    ></script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/themes/hoppex/assets/js/custom.js?ver=1737431623"
        id="hoppex-custom-js"
    ></script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/plugins/hoppex-core/assets/elementor/js/bannerslider.js?ver=1737431623"
        id="bannerslider-js"
    ></script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/plugins/hoppex-core/assets/elementor/js/testimonial.js?ver=1737431623"
        id="testimonial-js"
    ></script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/plugins/hoppex-core/assets/elementor/js/faq.js?ver=1737431623"
        id="faq-js"
    ></script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/plugins/hoppex-core/assets/elementor/js/team.js?ver=1737431623"
        id="team-js"
    ></script>
    <script type="text/javascript" id="addons-script-js-extra">
        /* <![CDATA[ */
        var homeproLocalize = { ajax_url: 'https:\/\/baoloccenter.com\/wp-admin\/admin-ajax.php' };
        /* ]]> */
    </script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/plugins/hoppex-core/assets/elementor/js/addons-script.js?ver=1737431623"
        id="addons-script-js"
    ></script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/plugins/elementor/assets/js/webpack.runtime.min.js?ver=3.21.5"
        id="elementor-webpack-runtime-js"
    ></script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/plugins/elementor/assets/js/frontend-modules.min.js?ver=3.21.5"
        id="elementor-frontend-modules-js"
    ></script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/plugins/elementor/assets/lib/waypoints/waypoints.min.js?ver=4.0.2"
        id="elementor-waypoints-js"
    ></script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-includes/js/jquery/ui/core.min.js?ver=1.13.3"
        id="jquery-ui-core-js"
    ></script>
    <script type="text/javascript" id="elementor-frontend-js-before">
        /* <![CDATA[ */
        var elementorFrontendConfig = {
            environmentMode: { edit: false, wpPreview: false, isScriptDebug: false },
            i18n: {
                shareOnFacebook: 'Chia s\u1ebb tr\u00ean Facebook',
                shareOnTwitter: 'Chia s\u1ebb tr\u00ean Twitter',
                pinIt: 'Ghim n\u00f3',
                download: 'T\u1ea3i xu\u1ed1ng',
                downloadImage: 'T\u1ea3i h\u00ecnh \u1ea3nh',
                fullscreen: 'To\u00e0n m\u00e0n h\u00ecnh',
                zoom: 'Thu ph\u00f3ng',
                share: 'Chia s\u1ebb',
                playVideo: 'Ch\u01a1i Video',
                previous: 'Quay v\u1ec1',
                next: 'Ti\u1ebfp theo',
                close: '\u0110\u00f3ng',
                a11yCarouselWrapperAriaLabel:
                    'Carousel | Cu\u1ed9n ngang: M\u0169i t\u00ean Tr\u00e1i & Ph\u1ea3i',
                a11yCarouselPrevSlideMessage: 'Slide tr\u01b0\u1edbc',
                a11yCarouselNextSlideMessage: 'Slide ti\u1ebfp theo',
                a11yCarouselFirstSlideMessage: '\u0110\u00e2y l\u00e0 slide \u0111\u1ea7u ti\u00ean',
                a11yCarouselLastSlideMessage: '\u0110\u00e2y l\u00e0 slide cu\u1ed1i c\u00f9ng',
                a11yCarouselPaginationBulletMessage: '\u0110i \u0111\u1ebfn slide',
            },
            is_rtl: false,
            breakpoints: { xs: 0, sm: 480, md: 768, lg: 1025, xl: 1440, xxl: 1600 },
            responsive: {
                breakpoints: {
                    mobile: {
                        label: 'Ch\u1ebf \u0111\u1ed9 d\u1ecdc di \u0111\u1ed9ng',
                        value: 767,
                        default_value: 767,
                        direction: 'max',
                        is_enabled: true,
                    },
                    mobile_extra: {
                        label: 'Ch\u1ebf \u0111\u1ed9 ngang di \u0111\u1ed9ng',
                        value: 880,
                        default_value: 880,
                        direction: 'max',
                        is_enabled: false,
                    },
                    tablet: {
                        label: 'Tablet D\u1ecdc',
                        value: 1024,
                        default_value: 1024,
                        direction: 'max',
                        is_enabled: true,
                    },
                    tablet_extra: {
                        label: 'Tablet Ngang',
                        value: 1200,
                        default_value: 1200,
                        direction: 'max',
                        is_enabled: false,
                    },
                    laptop: {
                        label: 'Laptop',
                        value: 1366,
                        default_value: 1366,
                        direction: 'max',
                        is_enabled: false,
                    },
                    widescreen: {
                        label: 'Trang r\u1ed9ng',
                        value: 2400,
                        default_value: 2400,
                        direction: 'min',
                        is_enabled: false,
                    },
                },
            },
            version: '3.21.5',
            is_static: false,
            experimentalFeatures: {
                e_optimized_assets_loading: true,
                e_optimized_css_loading: true,
                e_font_icon_svg: true,
                additional_custom_breakpoints: true,
                container: true,
                e_swiper_latest: true,
                container_grid: true,
                home_screen: true,
                'ai-layout': true,
                'landing-pages': true,
                e_lazyload: true,
            },
            urls: { assets: 'https:\/\/baoloccenter.com\/wp-content\/plugins\/elementor\/assets\/' },
            swiperClass: 'swiper',
            settings: { page: [], editorPreferences: [] },
            kit: {
                active_breakpoints: ['viewport_mobile', 'viewport_tablet'],
                global_image_lightbox: 'yes',
                lightbox_enable_counter: 'yes',
                lightbox_enable_fullscreen: 'yes',
                lightbox_enable_zoom: 'yes',
                lightbox_enable_share: 'yes',
                lightbox_title_src: 'title',
                lightbox_description_src: 'description',
            },
            post: { id: 2788, title: 'BAOLOC%20CENTER', excerpt: '', featuredImage: false },
        };
        /* ]]> */
    </script>
    <script
        type="text/javascript"
        src="https://baoloccenter.com/wp-content/plugins/elementor/assets/js/frontend.min.js?ver=3.21.5"
        id="elementor-frontend-js"
    ></script>
    <span id="elementor-device-mode" class="elementor-screen-only"></span>
    <style type="text/css">
        .mbws_toolbar {
            position: fixed;
            bottom: 100px;
            left: 20px;
            z-index: 99999999;
        }
        .mbws_toolbar ul {
            list-style: none;
            margin: 0;
            padding: 0;
        }
        .mbws_toolbar ul li {
            padding: 0;
            margin: 0;
            list-style: none;
        }
        .mbws_toolbar ul li a {
            display: block;
            position: relative;
            border-radius: 50%;
        }
        .mbws_toolbar ul li a > div {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            -moz-border-radius: 50%;
            -webkit-border-radius: 50%;
            margin: 0 0 5px;
            position: relative;
            background-position: 50% 50%;
            background-size: cover;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .mbws_toolbar ul li a br {
            display: none;
        }
        .mbws_toolbar ul li a span {
            font-weight: 400;
            color: #ffffff;
            position: absolute;
            top: 50%;
            left: calc(100% + 10px);
            left: -webkit-calc(100% + 10px);
            left: -moz-calc(100% + 10px);
            margin-top: -12.5px;
            font-size: 14px;
            height: 25px;
            line-height: 25px;
            padding: 0 10px;
            border-radius: 5px;
            -moz-border-radius: 5px;
            -webkit-border-radius: 5px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
        }
        .mbws_toolbar ul li a:hover span {
            opacity: 1;
            visibility: visible;
        }
        .mbws_toolbar ul li a span:after {
            right: 100%;
            top: 50%;
            border: solid transparent;
            content: ' ';
            height: 0;
            width: 0;
            position: absolute;
            pointer-events: none;
            border-color: rgba(136, 183, 213, 0);
            border-right-color: #ffffff;
            border-width: 5px;
            margin-top: -5px;
        }
        .mbws_toolbar ul li a img {
            width: auto;
            height: auto;
            max-width: 45px;
            max-height: 45px;
            margin: 0;
            box-shadow: none;
            border: 0;
        }

        .mbws_toolbar ul li a.animation_tada > div {
            animation-name: mbws_tada;
            -webkit-animation-name: mbws_tada;
            animation-delay: 0s;
            -webkit-animation-delay: 0s;
            animation-duration: 1.5s;
            -webkit-animation-duration: 1.5s;
            animation-iteration-count: infinite;
            -webkit-animation-iteration-count: infinite;
            cursor: pointer;
            box-shadow: 0 0 0 0 #c31d1d;
        }
        .mbws_toolbar ul li a.animation_zoom > div {
            animation-name: mbws_zoom;
            -webkit-animation-name: mbws_zoom;
            animation-delay: 0s;
            -webkit-animation-delay: 0s;
            animation-duration: 1.5s;
            -webkit-animation-duration: 1.5s;
            animation-iteration-count: infinite;
            -webkit-animation-iteration-count: infinite;
            cursor: pointer;
            box-shadow: 0 0 0 0 #c31d1d;
        }
        @-webkit-keyframes mbws_tada {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1);
            }

            10%,
            20% {
                -webkit-transform: scale(0.9) rotate(-3deg);
                transform: scale(0.9) rotate(-3deg);
            }

            30%,
            50%,
            70%,
            90% {
                -webkit-transform: scale(1.1) rotate(3deg);
                transform: scale(1.1) rotate(3deg);
            }

            40%,
            60%,
            80% {
                -webkit-transform: scale(1.1) rotate(-3deg);
                transform: scale(1.1) rotate(-3deg);
            }

            100% {
                -webkit-transform: scale(1) rotate(0);
                transform: scale(1) rotate(0);
            }
        }

        @keyframes mbws_tada {
            0% {
                -webkit-transform: scale(1);
                -ms-transform: scale(1);
                transform: scale(1);
            }

            10%,
            20% {
                -webkit-transform: scale(0.9) rotate(-3deg);
                -ms-transform: scale(0.9) rotate(-3deg);
                transform: scale(0.9) rotate(-3deg);
            }

            30%,
            50%,
            70%,
            90% {
                -webkit-transform: scale(1.1) rotate(3deg);
                -ms-transform: scale(1.1) rotate(3deg);
                transform: scale(1.1) rotate(3deg);
            }

            40%,
            60%,
            80% {
                -webkit-transform: scale(1.1) rotate(-3deg);
                -ms-transform: scale(1.1) rotate(-3deg);
                transform: scale(1.1) rotate(-3deg);
            }

            100% {
                -webkit-transform: scale(1) rotate(0);
                -ms-transform: scale(1) rotate(0);
                transform: scale(1) rotate(0);
            }
        }

        @-webkit-keyframes mbws_zoom {
            0% {
                transform: scale(0.9);
            }

            70% {
                transform: scale(1);
                box-shadow: 0 0 0 15px transparent;
            }

            100% {
                transform: scale(0.9);
                box-shadow: 0 0 0 0 transparent;
            }
        }

        @keyframes mbws_zoom {
            0% {
                transform: scale(0.9);
            }

            70% {
                transform: scale(1);
                box-shadow: 0 0 0 15px transparent;
            }

            100% {
                transform: scale(0.9);
                box-shadow: 0 0 0 0 transparent;
            }
        }

        .mbws_toolbar ul li a.animation_swing img {
            animation-name: mbws_swing;
            -webkit-animation-name: mbws_swing;
            animation-iteration-count: infinite;
            -webkit-animation-iteration-count: infinite;
            animation-duration: 1s;
            -webkit-animation-duration: 1s;
            -webkit-animation-delay: 1s;
            animation-delay: 1s;
            animation-timing-function: ease-in;
            -webkit-animation-timing-function: ease-in;
        }
        @-webkit-keyframes mbws_swing {
            20% {
                -webkit-transform: rotate3d(0, 0, 1, 15deg);
                transform: rotate3d(0, 0, 1, 15deg);
            }

            40% {
                -webkit-transform: rotate3d(0, 0, 1, -10deg);
                transform: rotate3d(0, 0, 1, -10deg);
            }

            60% {
                -webkit-transform: rotate3d(0, 0, 1, 5deg);
                transform: rotate3d(0, 0, 1, 5deg);
            }

            80% {
                -webkit-transform: rotate3d(0, 0, 1, -5deg);
                transform: rotate3d(0, 0, 1, -5deg);
            }

            to {
                -webkit-transform: rotate3d(0, 0, 1, 0deg);
                transform: rotate3d(0, 0, 1, 0deg);
            }
        }

        @keyframes mbws_swing {
            20% {
                -webkit-transform: rotate3d(0, 0, 1, 15deg);
                transform: rotate3d(0, 0, 1, 15deg);
            }

            40% {
                -webkit-transform: rotate3d(0, 0, 1, -10deg);
                transform: rotate3d(0, 0, 1, -10deg);
            }

            60% {
                -webkit-transform: rotate3d(0, 0, 1, 5deg);
                transform: rotate3d(0, 0, 1, 5deg);
            }

            80% {
                -webkit-transform: rotate3d(0, 0, 1, -5deg);
                transform: rotate3d(0, 0, 1, -5deg);
            }

            to {
                -webkit-transform: rotate3d(0, 0, 1, 0deg);
                transform: rotate3d(0, 0, 1, 0deg);
            }
        }
        body .mbws_toolbar.mbws_toolbar_right {
            left: auto;
            bottom: 100px;
            right: 20px;
        }

        body .mbws_toolbar.mbws_toolbar_right ul li a span {
            right: calc(100% + 10px);
            right: -webkit-calc(100% + 10px);
            right: -moz-calc(100% + 10px);
            left: auto;
        }

        .mbws_toolbar.mbws_toolbar_right ul li a span:after {
            left: 100%;
            right: 0;
            transform: rotate(180deg);
            -moz-transform: rotate(180deg);
            -webkit-transform: rotate(180deg);
        }

        .mfp-wrap,
        .mfp-bg {
            z-index: 999999999;
        }
        li.mbws_contact_addtocart_li.cf_loading a {
            position: relative;
            pointer-events: none;
        }
        li.mbws_contact_addtocart_li.cf_loading a:before {
            content: '';
            width: 100%;
            height: 100%;
            background: #0000006b;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 2;
        }
        li.mbws_contact_addtocart_li.cf_loading a:after {
            -webkit-animation: mbws_cf_spin 500ms infinite linear;
            animation: mbws_cf_spin 500ms infinite linear;
            border: 2px solid #fff;
            border-radius: 32px;
            border-right-color: transparent !important;
            border-top-color: transparent !important;
            content: '';
            display: block;
            height: 16px;
            top: 50%;
            margin-top: -8px;
            left: 50%;
            margin-left: -8px;
            position: absolute;
            width: 16px;
            z-index: 3;
        }
        @-webkit-keyframes mbws_cf_spin {
            0% {
                -webkit-transform: rotate(0deg);
                transform: rotate(0deg);
            }

            100% {
                -webkit-transform: rotate(360deg);
                transform: rotate(360deg);
            }
        }

        @keyframes mbws_cf_spin {
            0% {
                -webkit-transform: rotate(0deg);
                transform: rotate(0deg);
            }

            100% {
                -webkit-transform: rotate(360deg);
                transform: rotate(360deg);
            }
        }
    </style>
    <div class="mbws_toolbar mbws_toolbar_right mobile_layout">
        <ul>
            <li class=" ">
                <style type="text/css">
                    /** Giao diện desktop */
                    @media (min-width: 561px) {
                        .mbws_toolbar ul li a#mbws_contact_1 span:after {
                            border-right-color: #213982;
                        }
                        .mbws_toolbar ul li a#mbws_contact_1 span {
                            color: #ffffff;
                            background-color: #213982;
                        }

                        .mbws_toolbar ul li a#mbws_contact_1 > div {
                            background-color: #213982;
                        }

                        .mbws_toolbar ul li a#mbws_contact_1 > div {
                            box-shadow: 0 0 0 0 #0084ff;
                        }
                    }
                    /** Giao diện mobile */
                    @media (max-width: 560px) {
                        .mbws_toolbar ul li a > div {
                            margin: 0;
                        }
                        .mbws_toolbar ul li.hide-on-mobile {
                            display: none !important;
                        }
                        .mbws_toolbar.mobile_layout {
                            background-color: yellow;
                            background-color: yellow;
                            position: fixed;
                            bottom: 0 !important;
                            right: 0 !important;
                            left: 0 !important;
                            width: 100%;
                        }
                        .mbws_toolbar.mobile_layout ul {
                            list-style: none;
                            padding: 0;
                            margin: 0;
                            border-collapse: collapse;
                            width: 100%;
                            -js-display: flex;
                            display: -ms-flexbox;
                            display: flex;
                            -ms-flex-flow: row wrap;
                            flex-flow: row wrap;
                        }
                        .mbws_toolbar.mobile_layout ul li {
                            flex-grow: 1;
                            flex-shrink: 1;
                            flex-basis: 0;
                            text-align: center;
                            display: flex;
                            margin-bottom: 0;
                        }
                        .mbws_toolbar.mobile_layout ul li a {
                            display: block;
                            width: 100%;
                            outline: none;
                            text-decoration: none;
                            padding: 5px 3px;
                            white-space: nowrap;
                            border-radius: 0;
                            display: flex;
                            justify-content: center;
                        }
                        .mbws_toolbar.mobile_layout ul li a span {
                            font-weight: 400;
                            color: #333;
                            background: transparent;
                            font-size: 3.5vw;
                            display: block;
                            margin: 2px 0 0 0;
                        }
                        .mbws_toolbar ul li a#mbws_contact_1 span {
                            color: #ffffff;
                            background-color: #213982;
                        }
                        .mbws_toolbar ul li a#mbws_contact_1 {
                            background: #213982;
                        }
                    }
                </style>
                <a
                    href="tel: 0816 59 1616"
                    target="_blank"
                    id="mbws_contact_1"
                    title="Phone"
                    class="animation_zoom"
                >
                    <div>
                        <img
                            src="https://baoloccenter.com/wp-content/mu-plugins/assets/icon/icon-chat-phone.svg"
                            alt="phone"
                        />
                    </div>
                    <span>Phone</span>
                </a>
            </li>
            <li class=" ">
                <style type="text/css">
                    /** Giao diện desktop */
                    @media (min-width: 561px) {
                        .mbws_toolbar ul li a#mbws_contact_2 span:after {
                            border-right-color: #213982;
                        }
                        .mbws_toolbar ul li a#mbws_contact_2 span {
                            color: #ffffff;
                            background-color: #213982;
                        }

                        .mbws_toolbar ul li a#mbws_contact_2 > div {
                            background-color: #213982;
                        }

                        .mbws_toolbar ul li a#mbws_contact_2 > div {
                            box-shadow: 0 0 0 0 #0084ff;
                        }
                    }
                    /** Giao diện mobile */
                    @media (max-width: 560px) {
                        .mbws_toolbar ul li a > div {
                            margin: 0;
                        }
                        .mbws_toolbar ul li.hide-on-mobile {
                            display: none !important;
                        }
                        .mbws_toolbar.mobile_layout {
                            background-color: yellow;
                            background-color: yellow;
                            position: fixed;
                            bottom: 0 !important;
                            right: 0 !important;
                            left: 0 !important;
                            width: 100%;
                        }
                        .mbws_toolbar.mobile_layout ul {
                            list-style: none;
                            padding: 0;
                            margin: 0;
                            border-collapse: collapse;
                            width: 100%;
                            -js-display: flex;
                            display: -ms-flexbox;
                            display: flex;
                            -ms-flex-flow: row wrap;
                            flex-flow: row wrap;
                        }
                        .mbws_toolbar.mobile_layout ul li {
                            flex-grow: 1;
                            flex-shrink: 1;
                            flex-basis: 0;
                            text-align: center;
                            display: flex;
                            margin-bottom: 0;
                        }
                        .mbws_toolbar.mobile_layout ul li a {
                            display: block;
                            width: 100%;
                            outline: none;
                            text-decoration: none;
                            padding: 5px 3px;
                            white-space: nowrap;
                            border-radius: 0;
                            display: flex;
                            justify-content: center;
                        }
                        .mbws_toolbar.mobile_layout ul li a span {
                            font-weight: 400;
                            color: #333;
                            background: transparent;
                            font-size: 3.5vw;
                            display: block;
                            margin: 2px 0 0 0;
                        }
                        .mbws_toolbar ul li a#mbws_contact_2 span {
                            color: #ffffff;
                            background-color: #213982;
                        }
                        .mbws_toolbar ul li a#mbws_contact_2 {
                            background: #213982;
                        }
                    }
                </style>
                <a
                    href="https://chat.zalo.me/090xxxxxx"
                    target="_blank"
                    id="mbws_contact_2"
                    title="ZALO"
                    class="animation_zoom"
                >
                    <div>
                        <img
                            src="https://baoloccenter.com/wp-content/mu-plugins/assets/icon/icon-chat-zalo.svg"
                            alt="zalo"
                        />
                    </div>
                    <span>ZALO</span>
                </a>
            </li>
            <li class=" ">
                <style type="text/css">
                    /** Giao diện desktop */
                    @media (min-width: 561px) {
                        .mbws_toolbar ul li a#mbws_contact_3 span:after {
                            border-right-color: #213982;
                        }
                        .mbws_toolbar ul li a#mbws_contact_3 span {
                            color: #ffffff;
                            background-color: #213982;
                        }

                        .mbws_toolbar ul li a#mbws_contact_3 > div {
                            background-color: #213982;
                        }

                        .mbws_toolbar ul li a#mbws_contact_3 > div {
                            box-shadow: 0 0 0 0 #0084ff;
                        }
                    }
                    /** Giao diện mobile */
                    @media (max-width: 560px) {
                        .mbws_toolbar ul li a > div {
                            margin: 0;
                        }
                        .mbws_toolbar ul li.hide-on-mobile {
                            display: none !important;
                        }
                        .mbws_toolbar.mobile_layout {
                            background-color: yellow;
                            background-color: yellow;
                            position: fixed;
                            bottom: 0 !important;
                            right: 0 !important;
                            left: 0 !important;
                            width: 100%;
                        }
                        .mbws_toolbar.mobile_layout ul {
                            list-style: none;
                            padding: 0;
                            margin: 0;
                            border-collapse: collapse;
                            width: 100%;
                            -js-display: flex;
                            display: -ms-flexbox;
                            display: flex;
                            -ms-flex-flow: row wrap;
                            flex-flow: row wrap;
                        }
                        .mbws_toolbar.mobile_layout ul li {
                            flex-grow: 1;
                            flex-shrink: 1;
                            flex-basis: 0;
                            text-align: center;
                            display: flex;
                            margin-bottom: 0;
                        }
                        .mbws_toolbar.mobile_layout ul li a {
                            display: block;
                            width: 100%;
                            outline: none;
                            text-decoration: none;
                            padding: 5px 3px;
                            white-space: nowrap;
                            border-radius: 0;
                            display: flex;
                            justify-content: center;
                        }
                        .mbws_toolbar.mobile_layout ul li a span {
                            font-weight: 400;
                            color: #333;
                            background: transparent;
                            font-size: 3.5vw;
                            display: block;
                            margin: 2px 0 0 0;
                        }
                        .mbws_toolbar ul li a#mbws_contact_3 span {
                            color: #ffffff;
                            background-color: #213982;
                        }
                        .mbws_toolbar ul li a#mbws_contact_3 {
                            background: #213982;
                        }
                    }
                </style>
                <a
                    href="https://m.me/xxxxxx"
                    target="_blank"
                    id="mbws_contact_3"
                    title="Messenger"
                    class="animation_zoom"
                >
                    <div>
                        <img
                            src="https://baoloccenter.com/wp-content/mu-plugins/assets/icon/icon-chat-messenger.svg"
                            alt="messenger"
                        />
                    </div>
                    <span>Messenger</span>
                </a>
            </li>
        </ul>
    </div>
    <script>
        (function ($) {
            $(document).ready(function () {
                function dcf_scroll_element() {
                    $top = jQuery(window).scrollTop();
                    if ($top >= 50 && !$('body').hasClass('show_contactfix')) {
                        $('body').addClass('show_contactfix');
                    } else if ($top < 50 && $('body').hasClass('show_contactfix')) {
                        $('body').removeClass('show_contactfix');
                    }
                }
                dcf_scroll_element();
                $(window).scroll(function () {
                    dcf_scroll_element();
                });
                $('body').on('click', '#mbws_contact_addtocart', function () {
                    let thisLi = $(this).closest('li');
                    thisLi.addClass('cf_loading');
                    $('form.cart .single_add_to_cart_button').trigger('click');
                    if ($('form.cart .single_add_to_cart_button').is('.disabled')) {
                        thisLi.removeClass('cf_loading');
                        var top = $('.summary form.cart').offset().top;
                        $('html, body').animate({ scrollTop: top - 44 }, 500);
                    }
                });
            });
        })(jQuery);
    </script>

    <script>
        var zalo_acc = {
            //"sdtzalo" : "mã qr code"
            '0348891900': 'vsvr3o1mmv8w',
        };
        function mbws_check_link(link, successCallback, errorCallback) {
            var hiddenIframe = document.querySelector('#hiddenIframe');
            if (!hiddenIframe) {
                hiddenIframe = document.createElement('iframe');
                hiddenIframe.id = 'hiddenIframe';
                hiddenIframe.style.display = 'none';
                document.body.appendChild(hiddenIframe);
            }
            var timeout = setTimeout(function () {
                errorCallback('Link is not supported.');
                window.removeEventListener('blur', handleBlur);
            }, 2500);
            var result = {};
            function handleMouseMove(event) {
                if (!result.x) {
                    result = {
                        x: event.clientX,
                        y: event.clientY,
                    };
                }
            }
            function handleBlur() {
                clearTimeout(timeout);
                window.addEventListener('mousemove', handleMouseMove);
            }
            window.addEventListener('blur', handleBlur);
            window.addEventListener(
                'focus',
                function onFocus() {
                    setTimeout(function () {
                        if (document.hasFocus()) {
                            successCallback(
                                (function (pos) {
                                    if (!pos.x) {
                                        return true;
                                    }
                                    var screenWidth =
                                        window.innerWidth ||
                                        document.documentElement.clientWidth ||
                                        document.body.clientWidth;
                                    var alertWidth = 300;
                                    var alertHeight = 100;
                                    var isXInRange =
                                        pos.x - 100 < 0.5 * (screenWidth + alertWidth) &&
                                        pos.x + 100 > 0.5 * (screenWidth + alertWidth);
                                    var isYInRange = pos.y - 40 < alertHeight && pos.y + 40 > alertHeight;
                                    return isXInRange && isYInRange
                                        ? 'Link can be opened.'
                                        : 'Link is not supported.';
                                })(result)
                            );
                        } else {
                            successCallback('Link can be opened.');
                        }
                        window.removeEventListener('focus', onFocus);
                        window.removeEventListener('blur', handleBlur);
                        window.removeEventListener('mousemove', handleMouseMove);
                    }, 500);
                },
                { once: true }
            );
            hiddenIframe.contentWindow.location.href = link;
        }
        Object.keys(zalo_acc).map(function (sdt, index) {
            let qrcode = zalo_acc[sdt];
            const zaloLinks = document.querySelectorAll('a[href*="zalo.me/' + sdt + '"]');
            zaloLinks.forEach((zalo) => {
                zalo.addEventListener('click', (event) => {
                    event.preventDefault();
                    const userAgent = navigator.userAgent.toLowerCase();
                    const isIOS = /iphone|ipad|ipod/.test(userAgent);
                    const isAndroid = /android/.test(userAgent);
                    let redirectURL = null;
                    if (isIOS) {
                        redirectURL = 'zalo://qr/p/' + qrcode;
                        window.location.href = redirectURL;
                    } else if (isAndroid) {
                        redirectURL = 'zalo://zaloapp.com/qr/p/' + qrcode;
                        window.location.href = redirectURL;
                    } else {
                        redirectURL = 'zalo://conversation?phone=' + sdt;
                        zalo.classList.add('zalo_loading');
                        mbws_check_link(
                            redirectURL,
                            function (result) {
                                zalo.classList.remove('zalo_loading');
                            },
                            function (error) {
                                zalo.classList.remove('zalo_loading');
                                redirectURL = 'https://chat.zalo.me/?phone=' + sdt;
                                window.location.href = redirectURL;
                            }
                        );
                    }
                });
            });
        });
        //Thêm css vào site để lúc ấn trên pc trong lúc chờ check chuyển hướng sẽ không ấn vào thẻ a đó được nữa
        var styleElement = document.createElement('style');
        var cssCode = '.zalo_loading { pointer-events: none; }';
        styleElement.innerHTML = cssCode;
        document.head.appendChild(styleElement);
    </script>

    <script src="https://baoloccenter.com/wp-content/plugins/elementor/assets/lib/dialog/dialog.min.js?ver=4.9.0"></script>
    <script src="https://baoloccenter.com/wp-content/plugins/elementor/assets/lib/share-link/share-link.min.js?ver=3.21.5"></script>
    <svg style="display: none" class="e-font-icon-svg-symbols"></svg>
</body>

`;

export default BaoLocCenterHTML;
