import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Appointment, AppointmentStatus } from 'types/appointment.type';
import { useAppointments } from 'hooks/useAppointments';
import { FaCalendarAlt, FaClock, FaMapMarkerAlt, FaInfoCircle, FaEdit, FaTrash } from 'react-icons/fa';
import customMessage from 'components/ModalConfirm/customMessage';
import PAGE_URL from 'constants/PAGE_URL';
import './AppointmentDetail.scss';

interface AppointmentDetailProps {
    appointment: Appointment;
}

const AppointmentDetail: React.FC<AppointmentDetailProps> = ({ appointment }) => {
    const navigate = useNavigate();
    const { updateAppointment, deleteAppointment } = useAppointments();
    
    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('vi-VN', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };
    
    const getStatusLabel = (status: AppointmentStatus) => {
        switch (status) {
            case AppointmentStatus.CONFIRMED:
                return 'Đã xác nhận';
            case AppointmentStatus.CANCELLED:
                return 'Đã hủy';
            case AppointmentStatus.COMPLETED:
                return 'Đã hoàn thành';
            default:
                return 'Chờ xác nhận';
        }
    };
    
    const getStatusClass = (status: AppointmentStatus) => {
        switch (status) {
            case AppointmentStatus.CONFIRMED:
                return 'status-confirmed';
            case AppointmentStatus.CANCELLED:
                return 'status-cancelled';
            case AppointmentStatus.COMPLETED:
                return 'status-completed';
            default:
                return 'status-pending';
        }
    };
    
    const handleEdit = () => {
        navigate(`${PAGE_URL.SCHEDULE}/edit/${appointment.id}`);
    };
    
    const handleDelete = () => {
        customMessage.warning(
            'Bạn có chắc chắn muốn xóa lịch hẹn này không?'
        );
        
        // In a real implementation, we would show a confirmation dialog
        // and only delete if the user confirms
        deleteAppointment(appointment.id).then(success => {
            if (success) {
                navigate(PAGE_URL.SCHEDULE);
            }
        });
    };
    
    const handleCancel = () => {
        updateAppointment(appointment.id, { status: AppointmentStatus.CANCELLED });
    };
    
    return (
        <div className="appointment-detail">
            <div className="appointment-detail__header">
                <h2 className="appointment-detail__title">{appointment.title}</h2>
                <div className={`appointment-detail__status ${getStatusClass(appointment.status)}`}>
                    {getStatusLabel(appointment.status)}
                </div>
            </div>
            
            <div className="appointment-detail__info">
                <div className="info-item">
                    <FaCalendarAlt className="info-item__icon" />
                    <div className="info-item__content">
                        <span className="info-item__label">Ngày hẹn</span>
                        <span className="info-item__value">{formatDate(appointment.date)}</span>
                    </div>
                </div>
                
                <div className="info-item">
                    <FaClock className="info-item__icon" />
                    <div className="info-item__content">
                        <span className="info-item__label">Giờ hẹn</span>
                        <span className="info-item__value">{appointment.time}</span>
                    </div>
                </div>
                
                <div className="info-item">
                    <FaMapMarkerAlt className="info-item__icon" />
                    <div className="info-item__content">
                        <span className="info-item__label">Địa điểm</span>
                        <span className="info-item__value">{appointment.location}</span>
                    </div>
                </div>
                
                <div className="info-item info-item--full">
                    <FaInfoCircle className="info-item__icon" />
                    <div className="info-item__content">
                        <span className="info-item__label">Mô tả</span>
                        <span className="info-item__value">{appointment.description}</span>
                    </div>
                </div>
                
                {appointment.notes && (
                    <div className="info-item info-item--full">
                        <FaInfoCircle className="info-item__icon" />
                        <div className="info-item__content">
                            <span className="info-item__label">Ghi chú</span>
                            <span className="info-item__value">{appointment.notes}</span>
                        </div>
                    </div>
                )}
            </div>
            
            <div className="appointment-detail__actions">
                {appointment.status !== AppointmentStatus.CANCELLED && (
                    <button 
                        className="btn btn-outline"
                        onClick={handleCancel}
                    >
                        Hủy lịch hẹn
                    </button>
                )}
                
                <button 
                    className="btn btn-icon"
                    onClick={handleEdit}
                >
                    <FaEdit />
                </button>
                
                <button 
                    className="btn btn-icon btn-icon--danger"
                    onClick={handleDelete}
                >
                    <FaTrash />
                </button>
            </div>
            
            <div className="appointment-detail__reminder">
                <p>Vui lòng đến đúng giờ và mang theo đầy đủ giấy tờ cần thiết.</p>
                <p>Nếu có thay đổi, vui lòng cập nhật hoặc hủy lịch hẹn trước ít nhất 24 giờ.</p>
            </div>
        </div>
    );
};

export default AppointmentDetail;
