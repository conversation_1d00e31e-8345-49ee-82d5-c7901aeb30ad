import React, { memo } from 'react';
import { useCrawlData } from '../../../hooks/useCrawlData';
import { LazyLoadImage } from 'react-lazy-load-image-component';
import 'react-lazy-load-image-component/src/effects/blur.css';
import { useNavigate } from 'react-router-dom';
import { FaCalendarAlt, FaBuilding, FaArrowRight } from 'react-icons/fa';
import './StaticData.scss';
import JobList from './JobList';

function StaticData() {
    const { data, isLoading, error } = useCrawlData();

    const navigate = useNavigate();

    const handleNewsClick = (link: string) => {
        if (link && link !== 'N/A') {
            // If it's an internal link to a news detail page
            if (link.includes('/tin-tuc/') || link.includes('/news/')) {
                const newsId = link.split('/').pop();
                navigate(`/news/${newsId}`, { state: { url: link } });
            } else {
                // External link
                window.open(link, '_blank');
            }
        }
    };

    if (isLoading) {
        return (
            <div className="job-data-container">
                <div className="loading-section">
                    <div className="loading-spinner"></div>
                    <p>Đang tải dữ liệu...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="job-data-container">
                <div className="error-section">
                    <p>Đã xảy ra lỗi khi tải dữ liệu. Vui lòng thử lại sau.</p>
                </div>
            </div>
        );
    }

    return (
        <div className="job-data-container">
            {/* Featured Jobs Section */}
            <div className="static-data">
                <JobList />

                {/* News Section */}
                <div className="section-container">
                    <div className="section-header">
                        <h2 className="section-title">Tin Tức & Sự Kiện</h2>
                        <div className="section-line"></div>
                    </div>

                    <div className="news-grid">
                        {data?.marketReports.map((news, index) => (
                            <div
                                key={`news-${index}`}
                                className="news-card"
                                onClick={() => handleNewsClick(news.link)}
                            >
                                <div className="news-card-image">
                                    <LazyLoadImage
                                        src={
                                            news.image ||
                                            'https://thongtinvieclamkhanhhoa.vn/assets/images/brand/trung-tam-dich-viec-lam-logo-header.svg'
                                        }
                                        alt={news.title}
                                        effect="blur"
                                        width="100%"
                                        height="100%"
                                        placeholder={<div className="image-placeholder"></div>}
                                    />
                                </div>

                                <div className="news-card-content">
                                    <h3 className="news-title">{news.title}</h3>

                                    {news.date && (
                                        <div className="news-date">
                                            <FaCalendarAlt className="news-date-icon" />
                                            <span>{news.date}</span>
                                        </div>
                                    )}

                                    <div className="news-card-action">
                                        <span className="view-news-btn">
                                            Xem chi tiết <FaArrowRight className="arrow-icon" />
                                        </span>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Center Activities Section */}
                <div className="section-container">
                    <div className="section-header">
                        <h2 className="section-title">Hoạt Động Trung Tâm</h2>
                        <div className="section-line"></div>
                    </div>

                    <div className="activities-grid">
                        {data?.centerActivities.slice(0, 4).map((activity, index) => (
                            <div
                                key={`activity-${index}`}
                                className="activity-card"
                                onClick={() => handleNewsClick(activity.link)}
                            >
                                <div className="activity-card-image">
                                    <LazyLoadImage
                                        src={
                                            activity.image ||
                                            'https://thongtinvieclamkhanhhoa.vn/assets/images/brand/trung-tam-dich-viec-lam-logo-header.svg'
                                        }
                                        alt={activity.title}
                                        effect="blur"
                                        width="100%"
                                        height="100%"
                                        placeholder={<div className="image-placeholder"></div>}
                                    />
                                    <div className="activity-overlay">
                                        <FaBuilding className="activity-icon" />
                                    </div>
                                </div>

                                <div className="activity-card-content">
                                    <h3 className="activity-title">{activity.title}</h3>

                                    {activity.date && (
                                        <div className="activity-date">
                                            <FaCalendarAlt className="activity-date-icon" />
                                            <span>{activity.date}</span>
                                        </div>
                                    )}

                                    <div className="activity-card-action">
                                        <span className="view-activity-btn">
                                            Xem chi tiết <FaArrowRight className="arrow-icon" />
                                        </span>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    );
}

export default memo(StaticData);
