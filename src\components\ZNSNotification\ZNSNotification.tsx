import React from 'react';
import { Mo<PERSON>, But<PERSON> } from 'zmp-ui';
import { useZNSNotifications } from 'hooks/useZNSNotifications';
import './ZNSNotification.scss';

const ZNSNotification: React.FC = () => {
    const { currentNotification, isVisible, closeNotification } = useZNSNotifications();

    if (!currentNotification || !isVisible) {
        return null;
    }

    const handleContactBHTN = () => {
        // Logic để liên hệ phòng BHTN
        // Có thể mở dialer hoặc chuyển đến trang liên hệ
        closeNotification();
    };

    return (
        <Modal visible={isVisible} onClose={closeNotification} title="" className="zns-notification-modal">
            <div className="zns-notification-content">
                {/* Header với logo */}
                <div className="zns-header">
                    <div className="zns-logo">
                        <div className="logo-placeholder">🏛️</div>
                    </div>
                    <div className="zns-title">
                        <h3>TRUNG TÂM DỊCH VỤ VIỆC LÀM</h3>
                        <h3>TỈNH ĐẮK LẮK</h3>
                    </div>
                </div>

                {/* Nội dung thông báo */}
                <div className="zns-message">
                    <pre className="message-text">{currentNotification.message}</pre>
                </div>

                {/* Buttons */}
                <div className="zns-actions">
                    <Button
                        type="primary"
                        size="large"
                        fullWidth
                        onClick={handleContactBHTN}
                        className="contact-btn"
                    >
                        📞 Liên hệ phòng BHTN
                    </Button>
                </div>
            </div>
        </Modal>
    );
};

export default ZNSNotification;
