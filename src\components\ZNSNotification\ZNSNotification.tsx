import React from 'react';
import { Mo<PERSON>, Button } from 'zmp-ui';
import { useZNSNotifications } from 'hooks/useZNSNotifications';
import './ZNSNotification.scss';

const ZNSNotification: React.FC = () => {
    const { currentNotification, isVisible, closeNotification } = useZNSNotifications();

    if (!currentNotification || !isVisible) {
        return null;
    }

    const handleContactBHTN = () => {
        // Logic để liên hệ phòng BHTN
        // Có thể mở dialer hoặc chuyển đến trang liên hệ
        closeNotification();
    };

    return (
        <Modal
            visible={isVisible}
            onClose={closeNotification}
            title=""
            className="zns-notification-modal"
            maskClosable={false}
        >
            <div className="zns-notification-content">
                {/* Header với logo */}
                <div className="zns-header">
                    <div className="zns-logo">
                        <div className="logo-placeholder">🏛️</div>
                    </div>
                    <div className="zns-title">
                        <div className="title-main">TRUNG TÂM DỊCH VỤ VIỆC LÀM</div>
                        <div className="title-sub">TỈNH ĐẮK LẮK</div>
                    </div>
                </div>

                {/* Tiêu đề thông báo */}
                <div className="zns-notification-title">
                    <h2>📢 THÔNG BÁO TÌNH TRẠNG VIỆC LÀM HÀNG THÁNG</h2>
                </div>

                {/* Thông tin cá nhân */}
                <div className="zns-personal-info">
                    <div className="info-row">
                        <span className="label">👤 Họ và tên:</span>
                        <span className="value">{currentNotification.phieuKetQua.hoTen}</span>
                    </div>
                    <div className="info-row">
                        <span className="label">🎂 Năm sinh:</span>
                        <span className="value">{currentNotification.phieuKetQua.ngaySinh}</span>
                    </div>
                    <div className="info-row">
                        <span className="label">🆔 Số CCCD:</span>
                        <span className="value">{currentNotification.phieuKetQua.maDinhDanh}</span>
                    </div>
                    <div className="info-row">
                        <span className="label">📅 Lịch thông báo:</span>
                        <span className="value">
                            Từ ngày {currentNotification.nextNotificationDate} đến ngày{' '}
                            {currentNotification.phieuKetQua.thoiGianKetThuc}
                        </span>
                    </div>
                </div>

                {/* Nội dung chính */}
                <div className="zns-main-content">
                    <p>
                        Đến <strong>Trung tâm Dịch vụ việc làm tỉnh Đắk Lắk</strong>
                        (📍 09 đường 10/3, phường Tân Lợi, thành phố Buôn Ma Thuột) để thông báo tình trạng
                        việc làm hàng tháng theo quy định.
                    </p>
                    <div className="warning-box">
                        ⚠️ <strong>Lưu ý:</strong> Nếu không đến thông báo tình trạng việc làm sẽ bị tạm dừng
                        trợ cấp thất nghiệp theo quy định.
                    </div>
                </div>

                {/* Buttons */}
                <div className="zns-actions">
                    <Button
                        type="primary"
                        size="large"
                        fullWidth
                        onClick={handleContactBHTN}
                        className="contact-btn"
                    >
                        📞 Liên hệ phòng BHTN
                    </Button>
                </div>
            </div>
        </Modal>
    );
};

export default ZNSNotification;
