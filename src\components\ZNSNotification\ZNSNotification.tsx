import React from 'react';
import { Modal } from 'zmp-ui';
import Lottie from 'lottie-react';
import { useZNSNotifications } from 'hooks/useZNSNotifications';
import { FaCalendarAlt, FaClock, FaMapMarkerAlt, FaIdCard, FaPhoneAlt } from 'react-icons/fa';
import calendarAnimation from 'assets/lottiers/calendar.json';
import './ZNSNotification.scss';

const ZNSNotification: React.FC = () => {
    const { currentNotification, isVisible, closeNotification } = useZNSNotifications();

    if (!currentNotification || !isVisible) {
        return null;
    }

    const handleContactBHTN = () => {
        // Logic để liên hệ phòng BHTN
        // Có thể mở dialer hoặc chuyển đến trang liên hệ
        closeNotification();
    };

    return (
        <Modal
            maskClosable={false}
            visible={isVisible}
            onClose={closeNotification}
            className="zns-notification-modal"
        >
            <div className="zns-notification">
                <div className="zns-notification__header">
                    <div className="zns-notification__animation">
                        <Lottie
                            animationData={calendarAnimation}
                            loop={true}
                            style={{ height: 100, width: 300 }}
                        />
                    </div>
                    <h3 className="zns-notification__title">Thông báo việc làm</h3>
                    <div className="zns-notification__subtitle">
                        Bạn cần đến thông báo tình trạng việc làm vào ngày{' '}
                        <span className="highlight">{currentNotification.nextNotificationDate}</span>
                    </div>
                </div>

                <div className="zns-notification__content">
                    <div className="zns-notification__name">{currentNotification.phieuKetQua.hoTen}</div>

                    <div className="zns-notification__info">
                        <div className="zns-notification__info-item">
                            <FaIdCard className="zns-notification__info-icon" />
                            <span>CCCD: {currentNotification.phieuKetQua.maDinhDanh}</span>
                        </div>
                        <div className="zns-notification__info-item">
                            <FaCalendarAlt className="zns-notification__info-icon" />
                            <span>Ngày sinh: {currentNotification.phieuKetQua.ngaySinh}</span>
                        </div>
                        <div className="zns-notification__info-item">
                            <FaClock className="zns-notification__info-icon" />
                            <span>Thời hạn: Đến ngày {currentNotification.phieuKetQua.thoiGianKetThuc}</span>
                        </div>
                        <div className="zns-notification__info-item">
                            <FaMapMarkerAlt className="zns-notification__info-icon" />
                            <span>Trung tâm Dịch vụ việc làm tỉnh Đắk Lắk</span>
                        </div>
                        <div className="zns-notification__info-item zns-notification__info-item--notes">
                            <span>📍 Địa chỉ: 09 đường 10/3, phường Tân Lợi, thành phố Buôn Ma Thuột</span>
                        </div>
                        <div className="zns-notification__info-item zns-notification__info-item--warning">
                            <span>
                                ⚠️ <strong>Lưu ý:</strong> Nếu không đến thông báo sẽ bị tạm dừng trợ cấp thất
                                nghiệp
                            </span>
                        </div>
                    </div>
                </div>

                <div className="zns-notification__actions">
                    <button
                        className="zns-notification__button zns-notification__button--secondary"
                        onClick={closeNotification}
                    >
                        Đóng
                    </button>
                    <button
                        className="zns-notification__button zns-notification__button--primary"
                        onClick={handleContactBHTN}
                    >
                        <FaPhoneAlt /> Liên hệ BHTN
                    </button>
                </div>
            </div>
        </Modal>
    );
};

export default ZNSNotification;
