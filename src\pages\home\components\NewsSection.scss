.job-data-container {
    margin-top: 20px;
    margin-bottom: 10px;
    // padding: 15px;
    // background-color: #f8f9fa;
}

.news-section {
    margin-bottom: 30px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;

    &__header {
        padding: 15px 20px;
        background-color: #0056b3;

        h2 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #fff;
            text-transform: uppercase;
        }
    }

    &__content {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
        padding: 20px;
    }

    &__item {
        display: flex;
        flex-direction: column;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        cursor: pointer;
        background-color: #fff;
        height: 100%;

        &:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
        }

        &__image {
            height: 180px;
            overflow: hidden;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                transition: transform 0.5s ease;
            }

            &:hover img {
                transform: scale(1.05);
            }
        }

        &__content {
            padding: 15px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        &__title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            line-height: 1.4;
        }

        &__date {
            font-size: 14px;
            color: #666;
            margin-top: auto;
            display: flex;
            align-items: center;

            &:before {
                content: '\1F4C5'; /* Calendar emoji */
                margin-right: 5px;
            }
        }
    }
}

.loading-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;

    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(0, 86, 179, 0.2);
        border-radius: 50%;
        border-top-color: #0056b3;
        animation: spin 1s ease-in-out infinite;
        margin-bottom: 15px;
    }

    p {
        color: #666;
        font-size: 16px;
    }
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.no-data {
    grid-column: 1 / -1;
    padding: 30px;
    text-align: center;
    color: #666;
    background-color: #f9f9f9;
    border-radius: 8px;
    font-size: 16px;
}

// Responsive
@media (max-width: 768px) {
    .news-section {
        &__content {
            grid-template-columns: 1fr;
        }

        &__item {
            &__image {
                height: 150px;
            }
        }
    }
}
